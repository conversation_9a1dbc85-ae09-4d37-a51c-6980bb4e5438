"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfig = void 0;
const typeorm_1 = require("typeorm");
let SystemConfig = class SystemConfig {
};
exports.SystemConfig = SystemConfig;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'CONFIG_ID' }),
    __metadata("design:type", Number)
], SystemConfig.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONFIG_KEY', unique: true }),
    __metadata("design:type", String)
], SystemConfig.prototype, "key", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONFIG_VALUE', nullable: true }),
    __metadata("design:type", String)
], SystemConfig.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONFIG_TYPE' }),
    __metadata("design:type", String)
], SystemConfig.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONFIG_GROUP' }),
    __metadata("design:type", String)
], SystemConfig.prototype, "group", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRIPTION', nullable: true }),
    __metadata("design:type", String)
], SystemConfig.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ENCRYPTED', default: false }),
    __metadata("design:type", Boolean)
], SystemConfig.prototype, "isEncrypted", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EDITABLE', default: true }),
    __metadata("design:type", Boolean)
], SystemConfig.prototype, "isEditable", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'SORT_ORDER', default: 0 }),
    __metadata("design:type", Number)
], SystemConfig.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', default: true }),
    __metadata("design:type", Boolean)
], SystemConfig.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', nullable: true }),
    __metadata("design:type", String)
], SystemConfig.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'CREATION_DATE' }),
    __metadata("design:type", Date)
], SystemConfig.prototype, "creationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', nullable: true }),
    __metadata("design:type", String)
], SystemConfig.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'LAST_UPDATE_DATE', nullable: true }),
    __metadata("design:type", Date)
], SystemConfig.prototype, "lastUpdateDate", void 0);
exports.SystemConfig = SystemConfig = __decorate([
    (0, typeorm_1.Entity)('SYSTEM_CONFIG')
], SystemConfig);
//# sourceMappingURL=system-config.entity.js.map