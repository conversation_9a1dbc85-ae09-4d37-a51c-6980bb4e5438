"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessOperationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const process_operation_service_1 = require("../services/process-operation.service");
const process_operation_dto_1 = require("../dto/process-operation.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let ProcessOperationController = class ProcessOperationController {
    constructor(processOperationService) {
        this.processOperationService = processOperationService;
    }
    async findAll(query) {
        return this.processOperationService.findAll(query);
    }
    async findOne(id) {
        return this.processOperationService.findOne(+id);
    }
    async create(createDto, req) {
        return this.processOperationService.create(createDto, req.user);
    }
    async update(id, updateDto, req) {
        return this.processOperationService.update(+id, updateDto, req.user);
    }
    async remove(id) {
        await this.processOperationService.remove(+id);
        return { message: '工序删除成功' };
    }
};
exports.ProcessOperationController = ProcessOperationController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取工序列表' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '成功获取工序列表',
        type: process_operation_dto_1.ProcessOperationResponseDto,
        isArray: true
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProcessOperationController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取单个工序详情' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '成功获取工序详情',
        type: process_operation_dto_1.ProcessOperationResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '工序不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProcessOperationController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建新工序' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: '工序创建成功',
        type: process_operation_dto_1.ProcessOperationResponseDto
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [process_operation_dto_1.CreateProcessOperationDto, Object]),
    __metadata("design:returntype", Promise)
], ProcessOperationController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新工序信息' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '工序更新成功',
        type: process_operation_dto_1.ProcessOperationResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '工序不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, process_operation_dto_1.UpdateProcessOperationDto, Object]),
    __metadata("design:returntype", Promise)
], ProcessOperationController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除工序' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NO_CONTENT, description: '工序删除成功' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '工序不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProcessOperationController.prototype, "remove", null);
exports.ProcessOperationController = ProcessOperationController = __decorate([
    (0, swagger_1.ApiTags)('工序管理'),
    (0, common_1.Controller)('process/operations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [process_operation_service_1.ProcessOperationService])
], ProcessOperationController);
//# sourceMappingURL=process-operation.controller.js.map