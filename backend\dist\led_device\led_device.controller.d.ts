import { LedDeviceService } from './led_device.service';
import { CreateDeviceDto } from './dto/create-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';
export declare class LedDeviceController {
    private readonly ledDeviceService;
    constructor(ledDeviceService: LedDeviceService);
    create(createDeviceDto: CreateDeviceDto): Promise<any>;
    findAll(query: any): Promise<{
        items: import("./entities/led-device.entity").LedDevice[];
        meta: any;
    }>;
    findOne(id: string): Promise<import("./entities/led-device.entity").LedDevice>;
    update(id: string, updateDeviceDto: UpdateDeviceDto): Promise<import("./entities/led-device.entity").LedDevice>;
    remove(id: string): Promise<void>;
}
