"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedDeviceModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const led_device_service_1 = require("./led_device.service");
const led_device_controller_1 = require("./led_device.controller");
const led_device_entity_1 = require("./entities/led-device.entity");
const role_module_1 = require("../role/role.module");
let LedDeviceModule = class LedDeviceModule {
};
exports.LedDeviceModule = LedDeviceModule;
exports.LedDeviceModule = LedDeviceModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([led_device_entity_1.LedDevice]),
            role_module_1.RoleModule
        ],
        controllers: [led_device_controller_1.LedDeviceController],
        providers: [led_device_service_1.LedDeviceService],
        exports: [led_device_service_1.LedDeviceService],
    })
], LedDeviceModule);
//# sourceMappingURL=led_device.module.js.map