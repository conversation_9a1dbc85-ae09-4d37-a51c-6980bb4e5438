import { DataSource } from 'typeorm';
import { CreateDataDto } from './dto/create-data.dto';
import { UpdateDataDto } from './dto/update-data.dto';
import { LedData } from './entities/led-data.entity';
export declare class LedDataService {
    private dataSource;
    constructor(dataSource: DataSource);
    create(createDataDto: CreateDataDto): Promise<any>;
    findAll(params?: any): Promise<{
        items: any[];
        meta: any;
    }>;
    findOne(id: number): Promise<any>;
    update(id: number, updateDataDto: UpdateDataDto): Promise<LedData>;
    remove(id: number): Promise<void>;
    getStatistics(params?: any): Promise<any>;
    exportData(params?: any): Promise<any[]>;
}
