import { ProcessOperationService } from '../services/process-operation.service';
import { CreateProcessOperationDto, UpdateProcessOperationDto } from '../dto/process-operation.dto';
export declare class ProcessOperationController {
    private readonly processOperationService;
    constructor(processOperationService: ProcessOperationService);
    findAll(query: any): Promise<{
        items: import("../entities/process-operation.entity").ProcessOperation[];
        meta: any;
    }>;
    findOne(id: string): Promise<import("../entities/process-operation.entity").ProcessOperation>;
    create(createDto: CreateProcessOperationDto, req: any): Promise<import("../entities/process-operation.entity").ProcessOperation>;
    update(id: string, updateDto: UpdateProcessOperationDto, req: any): Promise<import("../entities/process-operation.entity").ProcessOperation>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
