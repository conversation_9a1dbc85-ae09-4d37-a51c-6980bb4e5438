"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedPlan = void 0;
const typeorm_1 = require("typeorm");
let LedPlan = class LedPlan {
};
exports.LedPlan = LedPlan;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], LedPlan.prototype, "ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_ID' }),
    __metadata("design:type", String)
], LedPlan.prototype, "LED_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_NAME' }),
    __metadata("design:type", String)
], LedPlan.prototype, "LED_NAME", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_PLAN_DATA', nullable: true }),
    __metadata("design:type", String)
], LedPlan.prototype, "LED_PLAN_DATA", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PLAN_DATE', nullable: true }),
    __metadata("design:type", String)
], LedPlan.prototype, "PLAN_DATE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PLAN_TYPE', nullable: true }),
    __metadata("design:type", String)
], LedPlan.prototype, "PLAN_TYPE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'WORK_TYPE', nullable: true }),
    __metadata("design:type", String)
], LedPlan.prototype, "WORK_TYPE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PUSH_STATUS', default: '0' }),
    __metadata("design:type", String)
], LedPlan.prototype, "PUSH_STATUS", void 0);
exports.LedPlan = LedPlan = __decorate([
    (0, typeorm_1.Entity)({ name: 'LED_PLAN_INFO' })
], LedPlan);
//# sourceMappingURL=led-plan.entity.js.map