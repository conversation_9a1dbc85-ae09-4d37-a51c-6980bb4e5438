"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleController = void 0;
const common_1 = require("@nestjs/common");
const role_service_1 = require("./role.service");
const create_role_dto_1 = require("./dto/create-role.dto");
const update_role_dto_1 = require("./dto/update-role.dto");
const assign_menu_dto_1 = require("./dto/assign-menu.dto");
let RoleController = class RoleController {
    constructor(roleService) {
        this.roleService = roleService;
    }
    async create(createRoleDto) {
        try {
            const role = await this.roleService.create(createRoleDto);
            return {
                success: true,
                message: '角色创建成功',
                data: role
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '角色创建失败',
                data: null
            };
        }
    }
    async findAll(query) {
        try {
            const result = await this.roleService.findAll(query);
            return {
                success: true,
                message: '获取角色列表成功',
                data: result.items,
                meta: result.meta
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取角色列表失败',
                data: [],
                meta: {
                    totalItems: 0,
                    itemsPerPage: 10,
                    currentPage: 1,
                    totalPages: 0
                }
            };
        }
    }
    async getRoleTypes() {
        try {
            const types = await this.roleService.getRoleTypes();
            return {
                success: true,
                message: '获取角色类型成功',
                data: types
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取角色类型失败',
                data: []
            };
        }
    }
    async findOne(id) {
        try {
            const role = await this.roleService.findOne(id);
            return {
                success: true,
                message: '获取角色详情成功',
                data: role
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取角色详情失败',
                data: null
            };
        }
    }
    async getRoleMenus(id) {
        try {
            const menus = await this.roleService.getRoleMenus(id);
            return {
                success: true,
                message: '获取角色菜单权限成功',
                data: menus
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取角色菜单权限失败',
                data: []
            };
        }
    }
    async update(id, updateRoleDto) {
        try {
            const role = await this.roleService.update(id, updateRoleDto);
            return {
                success: true,
                message: '角色更新成功',
                data: role
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '角色更新失败',
                data: null
            };
        }
    }
    async assignMenus(id, assignMenuDto) {
        try {
            await this.roleService.assignMenus(id, assignMenuDto);
            return {
                success: true,
                message: '菜单权限分配成功',
                data: null
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '菜单权限分配失败',
                data: null
            };
        }
    }
    async remove(id) {
        try {
            await this.roleService.remove(id);
            return {
                success: true,
                message: '角色删除成功',
                data: null
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '角色删除失败',
                data: null
            };
        }
    }
};
exports.RoleController = RoleController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_role_dto_1.CreateRoleDto]),
    __metadata("design:returntype", Promise)
], RoleController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RoleController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('types'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RoleController.prototype, "getRoleTypes", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/menus'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleController.prototype, "getRoleMenus", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_role_dto_1.UpdateRoleDto]),
    __metadata("design:returntype", Promise)
], RoleController.prototype, "update", null);
__decorate([
    (0, common_1.Post)(':id/menus'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, assign_menu_dto_1.AssignMenuDto]),
    __metadata("design:returntype", Promise)
], RoleController.prototype, "assignMenus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleController.prototype, "remove", null);
exports.RoleController = RoleController = __decorate([
    (0, common_1.Controller)('roles'),
    __metadata("design:paramtypes", [role_service_1.RoleService])
], RoleController);
//# sourceMappingURL=role.controller.js.map