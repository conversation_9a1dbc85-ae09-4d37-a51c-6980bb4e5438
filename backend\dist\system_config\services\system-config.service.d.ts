import { DataSource } from 'typeorm';
import { SystemConfig } from '../entities/system-config.entity';
import { CreateSystemConfigDto, UpdateSystemConfigDto, SystemConfigQueryDto } from '../dto/system-config.dto';
export declare class SystemConfigService {
    private dataSource;
    private readonly logger;
    constructor(dataSource: DataSource);
    findAll(query: SystemConfigQueryDto): Promise<{
        items: SystemConfig[];
        meta: {
            total: number;
            currentPage?: number;
            totalPages?: number;
            itemsPerPage?: number;
        };
    }>;
    private searchConfigs;
    findOne(id: number): Promise<SystemConfig>;
    findByKey(key: string): Promise<SystemConfig>;
    create(createSystemConfigDto: CreateSystemConfigDto): Promise<any>;
    update(id: number, updateSystemConfigDto: UpdateSystemConfigDto): Promise<any>;
    updateByKey(key: string, value: string): Promise<any>;
    remove(id: number): Promise<any>;
    private transformDatabaseRecord;
}
