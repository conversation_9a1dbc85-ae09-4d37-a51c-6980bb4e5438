import { Repository, DataSource } from 'typeorm';
import { ConcentratorLedMapping } from '../entities/concentrator-led-mapping.entity';
import { LedConcentrator } from '../entities/led-concentrator.entity';
import { CreateLedMappingDto, UpdateLedMappingDto, LedMappingQueryDto } from '../dto/led-mapping.dto';
export declare class LedMappingService {
    private mappingRepository;
    private concentratorRepository;
    private dataSource;
    private readonly logger;
    constructor(mappingRepository: Repository<ConcentratorLedMapping>, concentratorRepository: Repository<LedConcentrator>, dataSource: DataSource);
    create(createLedMappingDto: CreateLedMappingDto): Promise<any>;
    findAll(queryParams: LedMappingQueryDto): Promise<{
        items: any;
        meta: {
            total: number;
            page: number;
            limit: number;
        };
    }>;
    findOne(id: number): Promise<any>;
    findByLedId(ledId: string): Promise<{
        id: any;
        concentratorId: any;
        ledId: any;
        channelNumber: any;
        description: any;
        isActive: boolean;
        concentrator: {
            id: any;
            name: any;
            code: any;
            ipAddress: any;
            portNumber: any;
            protocolType: any;
            timeoutSeconds: any;
            maxRetryCount: any;
        };
        ledName: any;
    }>;
    private findMappingByLedId;
    update(id: number, updateLedMappingDto: UpdateLedMappingDto): Promise<any>;
    remove(id: number): Promise<{
        id: number;
        message: string;
    }>;
    private checkConcentratorExists;
}
