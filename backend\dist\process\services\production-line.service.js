"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductionLineService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const production_line_entity_1 = require("../entities/production-line.entity");
let ProductionLineService = class ProductionLineService {
    constructor(productionLineRepository, dataSource) {
        this.productionLineRepository = productionLineRepository;
        this.dataSource = dataSource;
    }
    async findAll(query = {}) {
        try {
            const page = query.page ? parseInt(query.page) : 1;
            const limit = query.limit ? parseInt(query.limit) : 10;
            const offset = (page - 1) * limit;
            const search = query.search;
            const isActive = query.isActive;
            const areaCode = query.areaCode;
            let whereClause = '';
            if (search) {
                whereClause = `WHERE (line.LINE_CODE LIKE '%${search}%' OR 
                             line.LINE_NAME LIKE '%${search}%' OR 
                             line.DESCRIPTION LIKE '%${search}%')`;
            }
            if (isActive !== undefined && isActive !== '') {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `line.IS_ACTIVE = ${isActive}`;
            }
            if (areaCode) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `line.AREA_CODE = '${areaCode}'`;
            }
            const countQuery = `SELECT COUNT(*) AS total FROM PRODUCTION_LINE line ${whereClause}`;
            const countResult = await this.dataSource.query(countQuery);
            const totalItems = parseInt(countResult[0].TOTAL);
            const sqlQuery = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT line.LINE_ID AS id, 
                   line.LINE_CODE AS code, 
                   line.LINE_NAME AS name, 
                   line.DESCRIPTION AS description,
                   line.ROUTE_ID AS routeId,
                   line.LINE_MANAGER AS manager,
                   line.AREA_CODE AS areaCode,
                   line.IS_ACTIVE AS isActive,
                   line.CREATED_BY AS createdBy,
                   TO_CHAR(line.CREATION_DATE, 'YYYY-MM-DD HH24:MI:SS') AS creationDate,
                   line.LAST_UPDATED_BY AS lastUpdatedBy,
                   TO_CHAR(line.LAST_UPDATE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS lastUpdateDate,
                   route.ROUTE_NAME AS "route.name",
                   route.ROUTE_CODE AS "route.code",
                   route.ROUTE_ID AS "route.id"
            FROM PRODUCTION_LINE line
            LEFT JOIN PROCESS_ROUTE route ON line.ROUTE_ID = route.ROUTE_ID
            ${whereClause}
            ORDER BY line.LINE_ID DESC
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const lines = await this.dataSource.query(sqlQuery);
            const formattedLines = lines.map(line => {
                const formattedLine = {};
                for (const key in line) {
                    if (key.includes('.')) {
                        formattedLine[key] = line[key];
                    }
                    else {
                        const lowercaseKey = key.toLowerCase();
                        formattedLine[lowercaseKey] = line[key];
                    }
                }
                const routeData = formattedLine['route.id'] ? {
                    id: formattedLine['route.id'],
                    name: formattedLine['route.name'],
                    code: formattedLine['route.code']
                } : null;
                delete formattedLine['route.id'];
                delete formattedLine['route.name'];
                delete formattedLine['route.code'];
                return {
                    ...formattedLine,
                    route: routeData
                };
            });
            return {
                items: formattedLines,
                meta: {
                    totalItems,
                    itemCount: formattedLines.length,
                    itemsPerPage: limit,
                    totalPages: Math.ceil(totalItems / limit),
                    currentPage: page,
                },
            };
        }
        catch (error) {
            console.error('获取产线列表失败:', error);
            throw new common_1.InternalServerErrorException('获取产线列表失败');
        }
    }
    async findOne(id) {
        try {
            const sqlQuery = `
        SELECT line.LINE_ID AS id, 
               line.LINE_CODE AS code, 
               line.LINE_NAME AS name, 
               line.DESCRIPTION AS description,
               line.ROUTE_ID AS routeId,
               line.LINE_MANAGER AS manager,
               line.AREA_CODE AS areaCode,
               line.IS_ACTIVE AS isActive,
               line.CREATED_BY AS createdBy,
               TO_CHAR(line.CREATION_DATE, 'YYYY-MM-DD HH24:MI:SS') AS creationDate,
               line.LAST_UPDATED_BY AS lastUpdatedBy,
               TO_CHAR(line.LAST_UPDATE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS lastUpdateDate,
               route.ROUTE_NAME AS "route.name",
               route.ROUTE_CODE AS "route.code",
               route.ROUTE_ID AS "route.id"
        FROM PRODUCTION_LINE line
        LEFT JOIN PROCESS_ROUTE route ON line.ROUTE_ID = route.ROUTE_ID
        WHERE line.LINE_ID = :1
      `;
            const lines = await this.dataSource.query(sqlQuery, [id]);
            if (!lines || lines.length === 0) {
                throw new common_1.NotFoundException(`产线ID ${id} 不存在`);
            }
            const line = lines[0];
            const formattedLine = {};
            for (const key in line) {
                if (key.includes('.')) {
                    formattedLine[key] = line[key];
                }
                else {
                    const lowercaseKey = key.toLowerCase();
                    formattedLine[lowercaseKey] = line[key];
                }
            }
            const routeData = formattedLine['route.id'] ? {
                id: formattedLine['route.id'],
                name: formattedLine['route.name'],
                code: formattedLine['route.code']
            } : null;
            delete formattedLine['route.id'];
            delete formattedLine['route.name'];
            delete formattedLine['route.code'];
            return {
                ...formattedLine,
                route: routeData
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取产线ID ${id} 失败:`, error);
            throw new common_1.InternalServerErrorException(`获取产线失败: ${error.message}`);
        }
    }
    async create(createDto, user) {
        try {
            const line = this.productionLineRepository.create({
                code: createDto.code,
                name: createDto.name,
                description: createDto.description,
                routeId: createDto.routeId,
                manager: createDto.manager,
                areaCode: createDto.areaCode,
                isActive: createDto.isActive ?? 1,
                createdBy: user?.username || 'system',
            });
            const savedLine = await this.productionLineRepository.save(line);
            const sqlQuery = `
        SELECT line.LINE_ID AS id, 
               line.LINE_CODE AS code, 
               line.LINE_NAME AS name, 
               line.DESCRIPTION AS description,
               line.ROUTE_ID AS routeId,
               line.LINE_MANAGER AS manager,
               line.AREA_CODE AS areaCode,
               line.IS_ACTIVE AS isActive,
               line.CREATED_BY AS createdBy,
               TO_CHAR(line.CREATION_DATE, 'YYYY-MM-DD HH24:MI:SS') AS creationDate,
               line.LAST_UPDATED_BY AS lastUpdatedBy,
               TO_CHAR(line.LAST_UPDATE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS lastUpdateDate,
               route.ROUTE_NAME AS "route.name",
               route.ROUTE_CODE AS "route.code",
               route.ROUTE_ID AS "route.id"
        FROM PRODUCTION_LINE line
        LEFT JOIN PROCESS_ROUTE route ON line.ROUTE_ID = route.ROUTE_ID
        WHERE line.LINE_ID = :1
      `;
            const lines = await this.dataSource.query(sqlQuery, [savedLine.id]);
            if (lines && lines.length > 0) {
                const line = lines[0];
                const formattedLine = {};
                for (const key in line) {
                    if (key.includes('.')) {
                        formattedLine[key] = line[key];
                    }
                    else {
                        const lowercaseKey = key.toLowerCase();
                        formattedLine[lowercaseKey] = line[key];
                    }
                }
                const routeData = formattedLine['route.id'] ? {
                    id: formattedLine['route.id'],
                    name: formattedLine['route.name'],
                    code: formattedLine['route.code']
                } : null;
                delete formattedLine['route.id'];
                delete formattedLine['route.name'];
                delete formattedLine['route.code'];
                return {
                    ...formattedLine,
                    route: routeData
                };
            }
            return savedLine;
        }
        catch (error) {
            console.error('创建产线失败:', error);
            throw new common_1.InternalServerErrorException(`创建产线失败: ${error.message}`);
        }
    }
    async update(id, updateDto, user) {
        try {
            const line = await this.findOne(id);
            Object.assign(line, {
                ...updateDto,
                lastUpdatedBy: user?.username || 'system',
                lastUpdateDate: new Date(),
            });
            await this.productionLineRepository.save(line);
            const sqlQuery = `
        SELECT line.LINE_ID AS id, 
               line.LINE_CODE AS code, 
               line.LINE_NAME AS name, 
               line.DESCRIPTION AS description,
               line.ROUTE_ID AS routeId,
               line.LINE_MANAGER AS manager,
               line.AREA_CODE AS areaCode,
               line.IS_ACTIVE AS isActive,
               line.CREATED_BY AS createdBy,
               TO_CHAR(line.CREATION_DATE, 'YYYY-MM-DD HH24:MI:SS') AS creationDate,
               line.LAST_UPDATED_BY AS lastUpdatedBy,
               TO_CHAR(line.LAST_UPDATE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS lastUpdateDate,
               route.ROUTE_NAME AS "route.name",
               route.ROUTE_CODE AS "route.code",
               route.ROUTE_ID AS "route.id"
        FROM PRODUCTION_LINE line
        LEFT JOIN PROCESS_ROUTE route ON line.ROUTE_ID = route.ROUTE_ID
        WHERE line.LINE_ID = :1
      `;
            const lines = await this.dataSource.query(sqlQuery, [id]);
            if (lines && lines.length > 0) {
                const line = lines[0];
                const formattedLine = {};
                for (const key in line) {
                    if (key.includes('.')) {
                        formattedLine[key] = line[key];
                    }
                    else {
                        const lowercaseKey = key.toLowerCase();
                        formattedLine[lowercaseKey] = line[key];
                    }
                }
                const routeData = formattedLine['route.id'] ? {
                    id: formattedLine['route.id'],
                    name: formattedLine['route.name'],
                    code: formattedLine['route.code']
                } : null;
                delete formattedLine['route.id'];
                delete formattedLine['route.name'];
                delete formattedLine['route.code'];
                return {
                    ...formattedLine,
                    route: routeData
                };
            }
            return line;
        }
        catch (error) {
            console.error(`更新产线ID ${id} 失败:`, error);
            throw new common_1.InternalServerErrorException(`更新产线失败: ${error.message}`);
        }
    }
    async remove(id) {
        const line = await this.findOne(id);
        await this.productionLineRepository.remove(line);
    }
};
exports.ProductionLineService = ProductionLineService;
exports.ProductionLineService = ProductionLineService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(production_line_entity_1.ProductionLine)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource])
], ProductionLineService);
//# sourceMappingURL=production-line.service.js.map