"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jwt_1 = require("@nestjs/jwt");
const bcrypt = __importStar(require("bcryptjs"));
const led_user_entity_1 = require("./entities/led-user.entity");
let AuthService = class AuthService {
    constructor(usersRepository, jwtService, dataSource) {
        this.usersRepository = usersRepository;
        this.jwtService = jwtService;
        this.dataSource = dataSource;
    }
    async signUp(createUserDto) {
        const { username, password } = createUserDto;
        const salt = await bcrypt.genSalt();
        const hashedPassword = await bcrypt.hash(password, salt);
        try {
            await this.dataSource.query(`INSERT INTO LED_USERS(ID, USERNAME, PASSWORD) 
         VALUES(SEQ_USER_ID.NEXTVAL, :1, :2)`, [username, hashedPassword]);
        }
        catch (error) {
            if (error.message.includes('ORA-00001')) {
                throw new common_1.ConflictException('Username already exists');
            }
            else {
                console.error('Error saving user:', error);
                throw new common_1.InternalServerErrorException();
            }
        }
    }
    async signIn(authCredentialsDto) {
        const { username, password } = authCredentialsDto;
        const users = await this.dataSource.query(`SELECT * FROM LED_USERS WHERE USERNAME = :1`, [username]);
        const user = users[0];
        if (user && (await bcrypt.compare(password, user.PASSWORD))) {
            const payload = { username, sub: user.ID };
            const accessToken = await this.jwtService.sign(payload);
            return { accessToken };
        }
        else {
            throw new common_1.UnauthorizedException('Please check your login credentials');
        }
    }
    async validateUser(username) {
        const users = await this.dataSource.query(`SELECT * FROM LED_USERS WHERE USERNAME = :1`, [username]);
        const user = users[0];
        if (!user) {
            return null;
        }
        const { PASSWORD, ...result } = user;
        return result;
    }
    async findAllUsers(params) {
        const { page, limit, search } = params;
        const offset = (page - 1) * limit;
        try {
            let baseQuery = `
        SELECT u.ID as USER_ID, u.USERNAME, u.DEPT_ID, d.DEPT_NAME
        FROM LED_USERS u
        LEFT JOIN LED_DEPARTMENT d ON u.DEPT_ID = d.DEPT_ID
      `;
            if (search) {
                baseQuery += ` WHERE u.USERNAME LIKE '%' || :1 || '%'`;
            }
            const query = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            ${baseQuery} ORDER BY u.ID DESC
          ) a WHERE ROWNUM <= :${search ? '2' : '1'}
        ) WHERE rnum > :${search ? '3' : '2'}
      `;
            const queryParams = search
                ? [search, offset + limit, offset]
                : [offset + limit, offset];
            let countQuery = `SELECT COUNT(*) as total FROM LED_USERS u`;
            const countParams = [];
            if (search) {
                countQuery += ` WHERE u.USERNAME LIKE '%' || :1 || '%'`;
                countParams.push(search);
            }
            const users = await this.dataSource.query(query, queryParams);
            const totalResult = await this.dataSource.query(countQuery, countParams);
            const totalItems = totalResult[0].TOTAL;
            return {
                items: users,
                meta: {
                    totalItems,
                    itemCount: users.length,
                    itemsPerPage: limit,
                    totalPages: Math.ceil(totalItems / limit),
                    currentPage: page,
                },
            };
        }
        catch (error) {
            console.error('Error fetching users:', error);
            throw new common_1.InternalServerErrorException('Failed to fetch users');
        }
    }
    async findUserById(id) {
        try {
            const users = await this.dataSource.query(`SELECT u.ID as USER_ID, u.USERNAME, u.DEPT_ID, d.DEPT_NAME
         FROM LED_USERS u
         LEFT JOIN LED_DEPARTMENT d ON u.DEPT_ID = d.DEPT_ID
         WHERE u.ID = :1`, [id]);
            if (!users || users.length === 0) {
                throw new common_1.NotFoundException(`User with ID ${id} not found`);
            }
            return users[0];
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`Error fetching user with ID ${id}:`, error);
            throw new common_1.InternalServerErrorException('Failed to fetch user');
        }
    }
    async createUser(createUserDto) {
        const { username, password, deptId } = createUserDto;
        const salt = await bcrypt.genSalt();
        const hashedPassword = await bcrypt.hash(password, salt);
        try {
            await this.dataSource.query(`INSERT INTO LED_USERS(ID, USERNAME, PASSWORD, DEPT_ID) 
         VALUES(SEQ_USER_ID.NEXTVAL, :1, :2, :3)`, [username, hashedPassword, deptId || null]);
            const newUsers = await this.dataSource.query(`SELECT u.ID as USER_ID, u.USERNAME, u.DEPT_ID, d.DEPT_NAME
         FROM LED_USERS u
         LEFT JOIN LED_DEPARTMENT d ON u.DEPT_ID = d.DEPT_ID
         WHERE u.USERNAME = :1`, [username]);
            return newUsers[0];
        }
        catch (error) {
            if (error.message.includes('ORA-00001')) {
                throw new common_1.ConflictException('Username already exists');
            }
            else {
                console.error('Error creating user:', error);
                throw new common_1.InternalServerErrorException('Failed to create user');
            }
        }
    }
    async updateUser(id, updateUserDto) {
        try {
            await this.findUserById(id);
            let updateQuery = `UPDATE LED_USERS SET `;
            const updateValues = [];
            const updateFields = [];
            if (updateUserDto.username) {
                updateFields.push(`USERNAME = :${updateValues.length + 1}`);
                updateValues.push(updateUserDto.username);
            }
            if (updateUserDto.password) {
                const salt = await bcrypt.genSalt();
                const hashedPassword = await bcrypt.hash(updateUserDto.password, salt);
                updateFields.push(`PASSWORD = :${updateValues.length + 1}`);
                updateValues.push(hashedPassword);
            }
            if (updateUserDto.deptId !== undefined) {
                updateFields.push(`DEPT_ID = :${updateValues.length + 1}`);
                updateValues.push(updateUserDto.deptId === null ? null : updateUserDto.deptId);
            }
            if (updateFields.length === 0) {
                return await this.findUserById(id);
            }
            updateQuery += updateFields.join(', ');
            updateQuery += ` WHERE ID = :${updateValues.length + 1}`;
            updateValues.push(id);
            await this.dataSource.query(updateQuery, updateValues);
            return await this.findUserById(id);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`Error updating user with ID ${id}:`, error);
            throw new common_1.InternalServerErrorException('Failed to update user');
        }
    }
    async deleteUser(id) {
        try {
            await this.findUserById(id);
            await this.dataSource.query(`DELETE FROM LED_USERS WHERE ID = :1`, [id]);
            return { success: true, message: `User with ID ${id} has been deleted` };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`Error deleting user with ID ${id}:`, error);
            throw new common_1.InternalServerErrorException('Failed to delete user');
        }
    }
    async resetPassword(id, resetPasswordDto) {
        const { password } = resetPasswordDto;
        try {
            await this.findUserById(id);
            const salt = await bcrypt.genSalt();
            const hashedPassword = await bcrypt.hash(password, salt);
            await this.dataSource.query(`UPDATE LED_USERS SET PASSWORD = :1 WHERE ID = :2`, [hashedPassword, id]);
            return { success: true, message: `Password for user with ID ${id} has been reset` };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`Error resetting password for user with ID ${id}:`, error);
            throw new common_1.InternalServerErrorException('Failed to reset password');
        }
    }
    async getUserProfile(userId) {
        try {
            const user = await this.findUserById(userId);
            if (!user) {
                throw new common_1.NotFoundException(`User with ID ${userId} not found`);
            }
            const userRolesQuery = `
        SELECT r.ROLE_ID, r.ROLE_CODE, r.ROLE_NAME, r.ROLE_TYPE, 
               d.DEPT_ID, d.DEPT_NAME
        FROM LED_ROLE r
        JOIN LED_USER_ROLE ur ON r.ROLE_ID = ur.ROLE_ID
        LEFT JOIN LED_DEPARTMENT d ON ur.DEPT_ID = d.DEPT_ID
        WHERE ur.USER_ID = :1 AND ur.IS_ACTIVE = 1 AND r.IS_ACTIVE = 1
      `;
            const userRoles = await this.dataSource.query(userRolesQuery, [userId]);
            const userMenusQuery = `
        SELECT DISTINCT m.*
        FROM LED_MENU m
        JOIN LED_ROLE_MENU rm ON m.MENU_ID = rm.MENU_ID
        JOIN LED_USER_ROLE ur ON rm.ROLE_ID = ur.ROLE_ID
        WHERE ur.USER_ID = :1 
        AND m.IS_ACTIVE = 1 
        AND rm.IS_ACTIVE = 1 
        AND ur.IS_ACTIVE = 1
        ORDER BY m.SORT_ORDER, m.MENU_CODE
      `;
            const userMenus = await this.dataSource.query(userMenusQuery, [userId]);
            const menuMap = new Map();
            const rootMenus = [];
            userMenus.forEach((menu) => {
                menu.children = [];
                menuMap.set(menu.MENU_ID, menu);
            });
            userMenus.forEach((menu) => {
                if (menu.PARENT_MENU_ID) {
                    const parent = menuMap.get(menu.PARENT_MENU_ID);
                    if (parent) {
                        parent.children.push(menu);
                    }
                }
                else {
                    rootMenus.push(menu);
                }
            });
            const userDevicesQuery = `
        SELECT udp.*, d.LED_NAME, d.GONGXU, d.CHANXIAN
        FROM LED_USER_DEVICE_PERMISSION udp
        JOIN LED_DEVICE_INFO d ON udp.LED_ID = d.LED_ID
        WHERE udp.USER_ID = :1 AND udp.IS_ACTIVE = 1
      `;
            const userDevices = await this.dataSource.query(userDevicesQuery, [userId]);
            return {
                ...user,
                roles: userRoles,
                menus: rootMenus,
                devices: userDevices
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`Error fetching profile for user ${userId}:`, error);
            throw new common_1.InternalServerErrorException('Failed to fetch user profile');
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(led_user_entity_1.LedUser)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        jwt_1.JwtService,
        typeorm_2.DataSource])
], AuthService);
//# sourceMappingURL=auth.service.js.map