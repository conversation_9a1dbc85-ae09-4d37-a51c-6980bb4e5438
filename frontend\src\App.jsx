import React, { useState, useEffect } from 'react';
import { Routes, Route, Link, Navigate, Outlet, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Toaster } from 'react-hot-toast';
import { logout, getCurrentUser } from './store/slices/authSlice';
import { getPublicConfigs } from './services/publicConfigService';

// Page Components (Lazy load them for better performance)
const HomePage = React.lazy(() => import('./features/home/<USER>'));
const LoginPage = React.lazy(() => import('./features/auth/pages/LoginPage'));
const RegisterPage = React.lazy(() => import('./features/auth/pages/RegisterPage'));
const DashboardPage = React.lazy(() => import('./features/dashboard/DashboardPage'));
// Placeholder for other pages
const ManualDispatchPage = React.lazy(() => import('./features/manual_dispatch/ManualDispatchPage'));
const BatchDispatchPage = React.lazy(() => import('./features/manual_dispatch/BatchDispatchPage'));

// 数据监控页面
const LedMonitorPage = React.lazy(() => import('./features/monitor/LedMonitorPage'));

// 数据管理页面
const UserManagementPage = React.lazy(() => import('./features/data_management/pages/UserManagementPage'));
const DeviceManagementPage = React.lazy(() => import('./features/data_management/pages/DeviceManagementPage'));
const PlanManagementPage = React.lazy(() => import('./features/data_management/pages/PlanManagementPage'));
const DataManagementPage = React.lazy(() => import('./features/data_management/pages/DataManagementPage'));

// 工艺路线管理页面
const ProcessOperationPage = React.lazy(() => import('./features/process/pages/ProcessOperationPage'));
const ProductionLinePage = React.lazy(() => import('./features/process/pages/ProductionLinePage'));
const ProcessRoutePage = React.lazy(() => import('./features/process/pages/ProcessRoutePage'));

// 系统配置管理页面
const SystemConfigPage = React.lazy(() => import('./features/system_config/pages/SystemConfigPage'));
const LedConcentratorPage = React.lazy(() => import('./features/system_config/pages/LedConcentratorPage'));
const LedMappingPage = React.lazy(() => import('./features/system_config/pages/LedMappingPage'));

// 事业部管理页面
const DepartmentManagementPage = React.lazy(() => import('./features/department/pages/DepartmentManagementPage'));

// ProtectedRoute component
const ProtectedRoute = () => {
  const { isAuthenticated } = useSelector((state) => state.auth);
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  return <Outlet />; // Renders child routes
};

// DepartmentRoute component - 用于事业部管理的权限控制
const DepartmentRoute = () => {
  const { user, isAuthenticated, isLoading } = useSelector((state) => state.auth);
  const dispatch = useDispatch();

  // 如果用户已认证但用户信息还没加载，尝试获取用户信息
  React.useEffect(() => {
    if (isAuthenticated && !user && !isLoading) {
      dispatch(getCurrentUser());
    }
  }, [isAuthenticated, user, isLoading, dispatch]);

  // 如果正在加载用户信息，显示加载状态
  if (isAuthenticated && (!user || isLoading)) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-pulse-slow flex flex-col items-center">
          <div className="w-12 h-12 rounded-full bg-tech-blue-gradient flex items-center justify-center shadow-tech-glow mb-4">
            <span className="text-white font-bold text-xl">LED</span>
          </div>
          <p className="text-tech-blue-400 font-medium">加载用户信息中...</p>
        </div>
      </div>
    );
  }

  // 检查用户是否有权限访问事业部管理
  const hasPermission =
    user?.roles?.some(r => r.ROLE_TYPE === 'SYSTEM') ||
    user?.menus?.some(m => m.MENU_CODE === 'department_management') ||
    // 临时允许所有已认证用户访问（用于测试）
    isAuthenticated;

  console.log('部门管理权限检查:', {
    user: user?.USERNAME,
    hasPermission,
    roles: user?.roles,
    menus: user?.menus,
    isAuthenticated,
    isLoading
  });

  return hasPermission ? <DepartmentManagementPage /> : <Navigate to="/" replace />;
};

// Basic Layout with Navigation
const Layout = () => {
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const location = useLocation();
  const [isDataMenuOpen, setIsDataMenuOpen] = useState(false);
  const [isProcessMenuOpen, setIsProcessMenuOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSystemConfigMenuOpen, setIsSystemConfigMenuOpen] = useState(false);
  const [isDispatchMenuOpen, setIsDispatchMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [platformName, setPlatformName] = useState('全流程离散制造执行协同平台');
  const [logoName, setLogoName] = useState('科力智造');

  // 关闭菜单当路由变化时
  useEffect(() => {
    setIsDataMenuOpen(false);
    setIsProcessMenuOpen(false);
    setIsMobileMenuOpen(false);
    setIsSystemConfigMenuOpen(false);
    setIsDispatchMenuOpen(false);
    setIsUserMenuOpen(false);
  }, [location.pathname]);

  // 获取用户信息
  useEffect(() => {
    if (isAuthenticated && !user) {
      dispatch(getCurrentUser());
    }
  }, [isAuthenticated, user, dispatch]);

  // 获取系统配置（平台名称和Logo名称）
  useEffect(() => {
    const fetchSystemConfigs = async () => {
      try {
        // 批量获取公开配置
        const configs = await getPublicConfigs(['PLATFORM_NAME', 'LOGO_NAME']);
        console.log('获取到的公开配置:', configs);

        if (configs.PLATFORM_NAME && configs.PLATFORM_NAME.value) {
          setPlatformName(configs.PLATFORM_NAME.value);
        }

        if (configs.LOGO_NAME && configs.LOGO_NAME.value) {
          setLogoName(configs.LOGO_NAME.value);
        }
      } catch (error) {
        console.error('获取系统配置失败:', error);
        // 使用默认值
        setPlatformName('全流程离散制造执行协同平台');
        setLogoName('科力智造');
      }
    };

    fetchSystemConfigs();
  }, []);

  const handleLogout = () => {
    dispatch(logout());
  };

  const navLinkClass = "px-4 py-2 rounded-tech text-sm font-medium transition-all duration-300 ease-in-out relative overflow-hidden group";
  const activeNavLinkClass = "bg-tech-blue-600 text-white shadow-lg border border-tech-blue-500/30";
  const inactiveNavLinkClass = "hover:bg-tech-dark-600 text-gray-300 hover:text-white hover:shadow-md transform hover:scale-105";

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <div className="min-h-screen bg-tech-dark-800 text-gray-100 flex flex-col w-full">
      {/* 导航菜单样式 */}
      <style jsx>{`
        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        .nav-item-enter {
          animation: slideDown 0.3s ease-out;
        }

        .dropdown-enter {
          animation: fadeIn 0.2s ease-out;
        }

        .mobile-menu-item {
          transition: all 0.2s ease-in-out;
        }

        .mobile-menu-item:hover {
          transform: translateX(4px);
        }

        .nav-glow {
          box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }
      `}</style>

      {/* 导航栏 */}
      <nav className="bg-tech-dark-700/95 backdrop-blur-md border-b border-tech-dark-500/50 shadow-2xl sticky top-0 z-50 w-full">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            {/* Logo 和品牌名称 - 靠左 */}
            <div className="flex items-center flex-shrink-0 mr-auto">
              <Link to="/" className="flex items-center space-x-3 group">
                <div className="w-10 h-10 rounded-xl bg-tech-blue-gradient flex items-center justify-center shadow-tech-glow transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                  <span className="text-white font-bold text-lg">KL</span>
                </div>
                <div className="hidden sm:block">
                  <div className="text-xl font-display font-bold text-gradient-blue transition-all duration-300 group-hover:text-tech-blue-300">
                    {logoName}
                  </div>
                  <div className="text-xs text-tech-gray-400 -mt-1">
                    Production Dashboard
                  </div>
                </div>
              </Link>
            </div>

            {/* 桌面导航菜单 - 靠右 */}
            <div className="hidden md:flex items-center space-x-2">
              {isAuthenticated ? (
                <>
                  {/* 首页按钮 - 登录后显示 */}
                  <Link
                    to="/"
                    className={`${navLinkClass} ${isActive('/') ? activeNavLinkClass : inactiveNavLinkClass}`}
                  >
                    <span className="relative z-10 flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                      </svg>
                      <span>首页</span>
                    </span>
                    <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                  </Link>
                  <Link
                    to="/dashboard"
                    className={`${navLinkClass} ${isActive('/dashboard') ? activeNavLinkClass : inactiveNavLinkClass}`}
                  >
                    <span className="relative z-10 flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
                      </svg>
                      <span>仪表盘</span>
                    </span>
                    <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                  </Link>
                  <Link
                    to="/led-data"
                    className={`${navLinkClass} ${isActive('/led-data') ? activeNavLinkClass : inactiveNavLinkClass}`}
                  >
                    <span className="relative z-10 flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>数据监控</span>
                    </span>
                    <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                  </Link>
                  {/* 数据下发下拉菜单 */}
                  <div className="relative group"
                    onMouseEnter={() => {
                      clearTimeout(window.dispatchMenuTimer);
                      setIsDispatchMenuOpen(true);
                    }}
                    onMouseLeave={() => {
                      window.dispatchMenuTimer = setTimeout(() => {
                        setIsDispatchMenuOpen(false);
                      }, 150);
                    }}
                  >
                    <button
                      className={`${navLinkClass} ${(location.pathname.startsWith('/manual-dispatch') || location.pathname.startsWith('/batch-dispatch')) ? activeNavLinkClass : inactiveNavLinkClass}`}
                    >
                      <span className="relative z-10 flex items-center space-x-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                        <span>数据下发</span>
                        <svg className={`w-4 h-4 transition-transform duration-200 ${isDispatchMenuOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </span>
                      <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                    </button>

                    {isDispatchMenuOpen && (
                      <div className="absolute right-0 top-full mt-1 w-52 rounded-tech shadow-2xl bg-tech-dark-600/95 backdrop-blur-md ring-1 ring-tech-dark-400/50 z-50 transform transition-all duration-200 ease-out scale-100 opacity-100">
                        <div className="py-2">
                          <Link
                            to="/manual-dispatch"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-blue-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M7 2a1 1 0 00-.707 1.707L7 4.414v3.758a1 1 0 01-.293.707l-4 4C.817 14.769 2.156 18 4.828 18h10.343c2.673 0 4.012-3.231 2.122-5.121l-4-4A1 1 0 0113 8.172V4.414l.707-.707A1 1 0 0013 2H7zm2 6.172V4h2v4.172a3 3 0 00.879 2.12l1.027 1.028a4 4 0 00-2.171.102l-.47.156a4 4 0 01-2.53 0l-.563-.187a1.993 1.993 0 00-.114-.035l1.063-1.063A3 3 0 009 8.172z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">手动派发</span>
                          </Link>
                          <Link
                            to="/batch-dispatch"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-green-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">批量派发</span>
                          </Link>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* 数据管理下拉菜单 */}
                  <div className="relative group"
                    onMouseEnter={() => {
                      clearTimeout(window.dataMenuTimer);
                      setIsDataMenuOpen(true);
                    }}
                    onMouseLeave={() => {
                      window.dataMenuTimer = setTimeout(() => {
                        setIsDataMenuOpen(false);
                      }, 150);
                    }}
                  >
                    <button
                      className={`${navLinkClass} ${(location.pathname.startsWith('/manage') || location.pathname.startsWith('/departments')) ? activeNavLinkClass : inactiveNavLinkClass}`}
                    >
                      <span className="relative z-10 flex items-center space-x-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" />
                        </svg>
                        <span>数据管理</span>
                        <svg className={`w-4 h-4 transition-transform duration-200 ${isDataMenuOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </span>
                      <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                    </button>

                    {isDataMenuOpen && (
                      <div className="absolute right-0 top-full mt-1 w-52 rounded-tech shadow-2xl bg-tech-dark-600/95 backdrop-blur-md ring-1 ring-tech-dark-400/50 z-50 transform transition-all duration-200 ease-out scale-100 opacity-100">
                        <div className="py-2">
                          <Link
                            to="/manage/users"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-blue-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">用户管理</span>
                          </Link>
                          <Link
                            to="/manage/devices"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-green-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">设备管理</span>
                          </Link>
                          <Link
                            to="/manage/plans"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-purple-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">计划管理</span>
                          </Link>
                          <Link
                            to="/manage/data"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-orange-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">数据管理</span>
                          </Link>
                          <Link
                            to="/departments"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">事业部管理</span>
                          </Link>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* 工艺路线管理下拉菜单 */}
                  <div className="relative group"
                    onMouseEnter={() => {
                      clearTimeout(window.processMenuTimer);
                      setIsProcessMenuOpen(true);
                    }}
                    onMouseLeave={() => {
                      window.processMenuTimer = setTimeout(() => {
                        setIsProcessMenuOpen(false);
                      }, 150);
                    }}
                  >
                    <button
                      className={`${navLinkClass} ${location.pathname.startsWith('/process') ? activeNavLinkClass : inactiveNavLinkClass}`}
                    >
                      <span className="relative z-10 flex items-center space-x-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                        </svg>
                        <span>工艺管理</span>
                        <svg className={`w-4 h-4 transition-transform duration-200 ${isProcessMenuOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </span>
                      <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                    </button>

                    {isProcessMenuOpen && (
                      <div className="absolute right-0 top-full mt-1 w-52 rounded-tech shadow-2xl bg-tech-dark-600/95 backdrop-blur-md ring-1 ring-tech-dark-400/50 z-50 transform transition-all duration-200 ease-out scale-100 opacity-100">
                        <div className="py-2">
                          <Link
                            to="/process/operations"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-blue-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">工序管理</span>
                          </Link>
                          <Link
                            to="/process/routes"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-green-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">工艺路线管理</span>
                          </Link>
                          <Link
                            to="/process/lines"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-purple-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">产线管理</span>
                          </Link>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* 系统配置下拉菜单 */}
                  <div className="relative group"
                    onMouseEnter={() => {
                      clearTimeout(window.systemMenuTimer);
                      setIsSystemConfigMenuOpen(true);
                    }}
                    onMouseLeave={() => {
                      window.systemMenuTimer = setTimeout(() => {
                        setIsSystemConfigMenuOpen(false);
                      }, 150);
                    }}
                  >
                    <button
                      className={`${navLinkClass} ${location.pathname.startsWith('/system') ? activeNavLinkClass : inactiveNavLinkClass}`}
                    >
                      <span className="relative z-10 flex items-center space-x-1">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                        </svg>
                        <span>系统配置</span>
                        <svg className={`w-4 h-4 transition-transform duration-200 ${isSystemConfigMenuOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </span>
                      <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                    </button>

                    {isSystemConfigMenuOpen && (
                      <div className="absolute right-0 top-full mt-1 w-52 rounded-tech shadow-2xl bg-tech-dark-600/95 backdrop-blur-md ring-1 ring-tech-dark-400/50 z-50 transform transition-all duration-200 ease-out scale-100 opacity-100">
                        <div className="py-2">
                          <Link
                            to="/system-config"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-blue-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">系统参数配置</span>
                          </Link>
                          <Link
                            to="/led-concentrator"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-green-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">LED集中器管理</span>
                          </Link>
                          <Link
                            to="/led-mapping"
                            className="flex items-center px-4 py-3 text-sm text-gray-300 hover:bg-tech-dark-500/50 hover:text-white transition-all duration-200 group/item"
                          >
                            <svg className="w-4 h-4 mr-3 text-tech-purple-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                            </svg>
                            <span className="group-hover/item:translate-x-1 transition-transform duration-200">LED映射配置</span>
                          </Link>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 用户信息下拉菜单 */}
                  <div className="relative group"
                    onMouseEnter={() => {
                      clearTimeout(window.userMenuTimer);
                      setIsUserMenuOpen(true);
                    }}
                    onMouseLeave={() => {
                      window.userMenuTimer = setTimeout(() => {
                        setIsUserMenuOpen(false);
                      }, 150);
                    }}
                  >
                    <button
                      className={`${navLinkClass} ${inactiveNavLinkClass} flex items-center space-x-2`}
                    >
                      <span className="relative z-10 flex items-center space-x-2">
                        <div className="w-8 h-8 bg-tech-blue-gradient rounded-full flex items-center justify-center shadow-md">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="flex flex-col items-start">
                          <span className="text-sm font-medium text-tech-gray-200">
                            {user?.USERNAME || '用户'}
                          </span>
                          <span className="text-xs text-tech-gray-400">
                            ID: {user?.ID || 'N/A'}
                          </span>
                        </div>
                        <svg className={`w-4 h-4 transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </span>
                      <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                    </button>

                    {/* 用户下拉菜单 */}
                    {isUserMenuOpen && (
                      <div className="absolute right-0 mt-2 w-64 bg-tech-dark-600/95 backdrop-blur-md border border-tech-dark-500/50 rounded-tech shadow-2xl z-50 dropdown-enter">
                        <div className="p-4 border-b border-tech-dark-500/50">
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-12 bg-tech-blue-gradient rounded-full flex items-center justify-center shadow-md">
                              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <div>
                              <div className="font-medium text-tech-gray-200">{user?.USERNAME || '用户'}</div>
                              <div className="text-sm text-tech-gray-400">用户ID: {user?.ID || 'N/A'}</div>
                              <div className="text-xs text-tech-gray-500 mt-1">
                                登录时间: {new Date().toLocaleString()}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="p-2">
                          <button
                            onClick={handleLogout}
                            className="w-full flex items-center space-x-3 px-3 py-2 text-tech-red-400 hover:bg-tech-red-900/30 hover:text-tech-red-300 rounded-lg transition-all duration-200"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 001-1h10.586l-2.293-2.293a1 1 0 10-1.414 1.414L14.586 5H4a3 3 0 00-3 3v8a3 3 0 003 3h8a3 3 0 003-3V8a1 1 0 10-2 0v8a1 1 0 01-1 1H4a1 1 0 01-1-1V8a1 1 0 011-1h8.586l-2.293 2.293a1 1 0 101.414 1.414L15 7.414V16a1 1 0 102 0V4a1 1 0 00-1-1H3z" clipRule="evenodd" />
                            </svg>
                            <span>退出登录</span>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  <button
                    onClick={handleLogout}
                    className={`${navLinkClass} text-tech-red-400 hover:bg-tech-red-900/30 hover:text-tech-red-300 border border-tech-red-500/30 hover:border-tech-red-400/50 hidden`}
                  >
                    <span className="relative z-10 flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 001-1h10.586l-2.293-2.293a1 1 0 10-1.414 1.414L14.586 5H4a3 3 0 00-3 3v8a3 3 0 003 3h8a3 3 0 003-3V8a1 1 0 10-2 0v8a1 1 0 01-1 1H4a1 1 0 01-1-1V8a1 1 0 011-1h8.586l-2.293 2.293a1 1 0 101.414 1.414L15 7.414V16a1 1 0 102 0V4a1 1 0 00-1-1H3z" clipRule="evenodd" />
                      </svg>
                      <span>退出</span>
                    </span>
                    <div className="absolute inset-0 bg-tech-red-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                  </button>
                </>
              ) : (
                <>
                  {/* 首页按钮 - 当前页面时蓝色背景，其他时灰色背景 */}
                  <Link
                    to="/"
                    className={`${navLinkClass} ${isActive('/') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/50' : 'bg-tech-dark-600/80 text-tech-gray-200 hover:bg-tech-dark-500 hover:text-white border border-tech-dark-500/50 hover:border-tech-blue-500/50'}`}
                  >
                    <span className="relative z-10 flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                      </svg>
                      <span>首页</span>
                    </span>
                    <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                  </Link>

                  {/* 登录按钮 - 当前页面时蓝色背景，其他时灰色背景 */}
                  <Link
                    to="/login"
                    className={`${navLinkClass} ${isActive('/login') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/50' : 'bg-tech-dark-600/80 text-tech-gray-200 hover:bg-tech-dark-500 hover:text-white border border-tech-dark-500/50 hover:border-tech-blue-500/50'}`}
                  >
                    <span className="relative z-10 flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                      </svg>
                      <span>登录</span>
                    </span>
                    <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                  </Link>

                  {/* 注册按钮 - 当前页面时蓝色背景，其他时灰色背景 */}
                  <Link
                    to="/register"
                    className={`${navLinkClass} ${isActive('/register') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/50' : 'bg-tech-dark-600/80 text-tech-gray-200 hover:bg-tech-dark-500 hover:text-white border border-tech-dark-500/50 hover:border-tech-blue-500/50'}`}
                  >
                    <span className="relative z-10 flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                      </svg>
                      <span>注册</span>
                    </span>
                    <div className="absolute inset-0 bg-tech-blue-gradient opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-tech"></div>
                  </Link>
                </>
              )}
            </div>
            
            {/* 移动端菜单按钮 */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-tech text-gray-400 hover:text-white hover:bg-tech-dark-600 focus:outline-none transition-all duration-300 group"
              >
                <span className="sr-only">打开主菜单</span>
                {/* 菜单图标 */}
                <div className="relative w-6 h-6">
                  <span className={`absolute inset-0 transition-all duration-300 ${isMobileMenuOpen ? 'rotate-45 opacity-0' : 'rotate-0 opacity-100'}`}>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                  </span>
                  {/* 关闭图标 */}
                  <span className={`absolute inset-0 transition-all duration-300 ${isMobileMenuOpen ? 'rotate-0 opacity-100' : '-rotate-45 opacity-0'}`}>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </span>
                </div>
              </button>
            </div>
          </div>
        </div>
        
        {/* 移动端菜单 */}
        <div className={`md:hidden transition-all duration-300 ease-in-out bg-tech-dark-600/95 backdrop-blur-md border-t border-tech-dark-500/50 ${isMobileMenuOpen ? 'max-h-[calc(100vh-4rem)] opacity-100 overflow-y-auto' : 'max-h-0 opacity-0 overflow-hidden'}`}>
          <div className="px-4 pt-4 pb-6 space-y-2">
              {/* 移动端用户信息 */}
              {isAuthenticated && (
                <div className="bg-tech-dark-700/50 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-4 mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-tech-blue-gradient rounded-full flex items-center justify-center shadow-md">
                      <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-tech-gray-200">{user?.USERNAME || '用户'}</div>
                      <div className="text-sm text-tech-gray-400">用户ID: {user?.ID || 'N/A'}</div>
                      <div className="text-xs text-tech-gray-500 mt-1">
                        {new Date().toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <Link
                to="/"
                className={`flex items-center space-x-3 px-4 py-3 rounded-tech text-base font-medium transition-all duration-200 ${isActive('/') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <svg className="w-5 h-5 text-tech-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                </svg>
                <span>首页</span>
              </Link>
              
              {isAuthenticated ? (
                <>
                  <Link
                    to="/dashboard"
                    className={`flex items-center space-x-3 px-4 py-3 rounded-tech text-base font-medium transition-all duration-200 ${isActive('/dashboard') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-5 h-5 text-tech-purple-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" />
                    </svg>
                    <span>仪表盘</span>
                  </Link>
                  <Link
                    to="/led-data"
                    className={`flex items-center space-x-3 px-4 py-3 rounded-tech text-base font-medium transition-all duration-200 ${isActive('/led-data') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-5 h-5 text-tech-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 3a1 1 0 012 0v4a1 1 0 01-2 0V3zM3 13a1 1 0 011-1h1a1 1 0 110 2H4a1 1 0 01-1-1zM9 3a1 1 0 000 2v4a1 1 0 002 0V5a1 1 0 100-2H9zM9 13a1 1 0 011-1h1a1 1 0 110 2h-1a1 1 0 01-1-1zM15 3a1 1 0 012 0v8a1 1 0 01-2 0V3zM15 15a1 1 0 011-1h1a1 1 0 110 2h-1a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                    <span>数据监控</span>
                  </Link>
                  {/* 数据下发标题 */}
                  <div className="flex items-center px-4 py-3 mt-4 mb-2">
                    <div className="flex items-center space-x-2">
                      <svg className="w-4 h-4 text-tech-orange-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                      <span className="text-base font-semibold text-gray-200 uppercase tracking-wider">数据下发</span>
                    </div>
                    <div className="flex-1 ml-3 h-px bg-tech-dark-500"></div>
                  </div>
                  <Link
                    to="/manual-dispatch"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/manual-dispatch') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    <span>手动派发</span>
                  </Link>
                  <Link
                    to="/batch-dispatch"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/batch-dispatch') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                    </svg>
                    <span>批量派发</span>
                  </Link>
                  
                  {/* 数据管理标题 */}
                  <div className="flex items-center px-4 py-3 mt-4 mb-2">
                    <div className="flex items-center space-x-2">
                      <svg className="w-4 h-4 text-tech-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      <span className="text-base font-semibold text-gray-200 uppercase tracking-wider">数据管理</span>
                    </div>
                    <div className="flex-1 ml-3 h-px bg-tech-dark-500"></div>
                  </div>
                  <Link
                    to="/manage/users"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/manage/users') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                    <span>用户管理</span>
                  </Link>
                  <Link
                    to="/manage/devices"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/manage/devices') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                    <span>设备管理</span>
                  </Link>
                  <Link
                    to="/manage/plans"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/manage/plans') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-purple-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                    <span>计划管理</span>
                  </Link>
                  <Link
                    to="/manage/data"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/manage/data') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-orange-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>数据管理</span>
                  </Link>
                  <Link
                    to="/departments"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/departments') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    <span>事业部管理</span>
                  </Link>

                  {/* 工艺路线管理标题 */}
                  <div className="flex items-center px-4 py-3 mt-4 mb-2">
                    <div className="flex items-center space-x-2">
                      <svg className="w-4 h-4 text-tech-purple-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                      <span className="text-base font-semibold text-gray-200 uppercase tracking-wider">工艺管理</span>
                    </div>
                    <div className="flex-1 ml-3 h-px bg-tech-dark-500"></div>
                  </div>
                  <Link
                    to="/process/operations"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/process/operations') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                    <span>工序管理</span>
                  </Link>
                  <Link
                    to="/process/routes"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/process/routes') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    <span>工艺路线管理</span>
                  </Link>
                  <Link
                    to="/process/lines"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/process/lines') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-orange-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clipRule="evenodd" />
                      <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zM8 8a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zm1 3a1 1 0 100 2h2a1 1 0 100-2H9z" clipRule="evenodd" />
                    </svg>
                    <span>产线管理</span>
                  </Link>

                  {/* 系统配置标题 */}
                  <div className="flex items-center px-4 py-3 mt-4 mb-2">
                    <div className="flex items-center space-x-2">
                      <svg className="w-4 h-4 text-tech-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                      <span className="text-base font-semibold text-gray-200 uppercase tracking-wider">系统配置</span>
                    </div>
                    <div className="flex-1 ml-3 h-px bg-tech-dark-500"></div>
                  </div>
                  <Link
                    to="/system-config"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/system-config') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>系统参数配置</span>
                  </Link>
                  <Link
                    to="/led-concentrator"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/led-concentrator') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M2 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 002 2H4a2 2 0 01-2-2V5zm3 1h6v4H5V6zm6 6H5v2h6v-2z" clipRule="evenodd" />
                      <path d="M15 7h1a2 2 0 012 2v5.5a1.5 1.5 0 01-3 0V9a1 1 0 00-1-1h-1v-1z" />
                    </svg>
                    <span>LED集中器管理</span>
                  </Link>
                  <Link
                    to="/led-mapping"
                    className={`flex items-center space-x-3 px-6 py-3 rounded-tech text-base font-medium transition-all duration-200 mobile-menu-item ${isActive('/led-mapping') ? 'bg-tech-blue-600 text-white border border-tech-blue-500/30' : 'text-gray-300 hover:bg-tech-dark-500 hover:text-white'}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <svg className="w-4 h-4 text-tech-purple-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    <span>LED映射配置</span>
                  </Link>

                  <div className="mt-6 pt-4 pb-4 border-t border-tech-dark-500">
                    <button
                      onClick={handleLogout}
                      className="flex items-center space-x-3 w-full px-4 py-3 rounded-tech text-base font-medium transition-all duration-200 text-tech-red-400 hover:bg-tech-red-900/30 hover:text-tech-red-300 border border-tech-red-500/30 hover:border-tech-red-400/50"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 001-1h10.586l-2.293-2.293a1 1 0 10-1.414 1.414L14.586 5H4a3 3 0 00-3 3v8a3 3 0 003 3h8a3 3 0 003-3V8a1 1 0 10-2 0v8a1 1 0 01-1 1H4a1 1 0 01-1-1V8a1 1 0 011-1h8.586l-2.293 2.293a1 1 0 101.414 1.414L15 7.414V16a1 1 0 102 0V4a1 1 0 00-1-1H3z" clipRule="evenodd" />
                      </svg>
                      <span>退出登录</span>
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <Link 
                    to="/login" 
                    className="block px-3 py-2 rounded-tech text-sm font-medium bg-tech-dark-500 hover:bg-tech-dark-400 text-white"
                  >
                    登录
                  </Link>
                  <Link 
                    to="/register" 
                    className="block px-3 py-2 rounded-tech text-sm font-medium bg-tech-blue-600 hover:bg-tech-blue-700 text-white mt-2"
                  >
                    注册
                  </Link>
                </>
              )}
            </div>
        </div>
      </nav>
      
      {/* 主内容区 */}
      <main className="flex-grow p-0 w-full">
        <div className="w-full">
          <React.Suspense fallback={
            <div className="flex items-center justify-center h-64">
              <div className="animate-pulse-slow flex flex-col items-center">
                <div className="w-12 h-12 rounded-full bg-tech-blue-gradient flex items-center justify-center shadow-tech-glow mb-4">
                  <span className="text-white font-bold text-xl">LED</span>
                </div>
                <p className="text-tech-blue-400 font-medium">加载中...</p>
              </div>
            </div>
          }>
            <Outlet /> {/* Child routes will render here */}
          </React.Suspense>
        </div>
      </main>
      
      {/* 简洁页脚 */}
      <footer className="bg-tech-dark-700/95 backdrop-blur-md border-t border-tech-dark-500/50 w-full">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-lg bg-tech-blue-gradient flex items-center justify-center shadow-md">
                <span className="text-white font-bold text-sm">KL</span>
              </div>
              <div className="text-sm text-tech-gray-300">
                © {new Date().getFullYear()} {platformName}
              </div>
            </div>
          </div>
        </div>

        {/* 底部装饰线 */}
        <div className="h-0.5 bg-gradient-to-r from-tech-blue-500 via-tech-purple-500 to-tech-orange-500"></div>
      </footer>
    </div>
  );
};

function App() {
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();

  // 全局用户信息获取逻辑
  useEffect(() => {
    if (isAuthenticated && !user) {
      console.log('App: 检测到已认证但无用户信息，获取用户信息');
      dispatch(getCurrentUser());
    }
  }, [isAuthenticated, user, dispatch]);

  return (
    <>
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            theme: {
              primary: 'green',
              secondary: 'black',
            },
          },
        }}
      />
      <Routes>
        <Route element={<Layout />}>
          <Route path="/" element={<HomePage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route element={<ProtectedRoute />}>
          {/* Protected Routes below */}
          <Route path="/dashboard" element={<DashboardPage />} />
          <Route path="/led-data" element={<LedMonitorPage />} />
          <Route path="/manual-dispatch" element={<ManualDispatchPage />} />
          <Route path="/batch-dispatch" element={<BatchDispatchPage />} />
          
          {/* 数据管理路由 */}
          <Route path="/manage/users" element={<UserManagementPage />} />
          <Route path="/manage/devices" element={<DeviceManagementPage />} />
          <Route path="/manage/plans" element={<PlanManagementPage />} />
          <Route path="/manage/data" element={<DataManagementPage />} />
          
          {/* 工艺路线管理路由 */}
          <Route path="/process/operations" element={<ProcessOperationPage />} />
          <Route path="/process/routes" element={<ProcessRoutePage />} />
          <Route path="/process/lines" element={<ProductionLinePage />} />

          {/* 事业部管理路由 */}
          <Route path="/departments" element={<DepartmentRoute />} />

          {/* 系统配置管理路由 */}
          <Route path="/system-config" element={<SystemConfigPage />} />
          <Route path="/led-concentrator" element={<LedConcentratorPage />} />
          <Route path="/led-mapping" element={<LedMappingPage />} />
        </Route>
        <Route path="*" element={<Navigate to="/" replace />} /> {/* Fallback for unknown routes */}
      </Route>
    </Routes>
    </>
  );
}

export default App; 