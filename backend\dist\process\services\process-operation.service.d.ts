import { Repository, DataSource } from 'typeorm';
import { ProcessOperation } from '../entities/process-operation.entity';
import { CreateProcessOperationDto, UpdateProcessOperationDto } from '../dto/process-operation.dto';
export declare class ProcessOperationService {
    private processOperationRepository;
    private dataSource;
    constructor(processOperationRepository: Repository<ProcessOperation>, dataSource: DataSource);
    findAll(query?: any): Promise<{
        items: ProcessOperation[];
        meta: any;
    }>;
    findOne(id: number): Promise<ProcessOperation>;
    create(createDto: CreateProcessOperationDto, user?: any): Promise<ProcessOperation>;
    update(id: number, updateDto: UpdateProcessOperationDto, user?: any): Promise<ProcessOperation>;
    remove(id: number): Promise<void>;
}
