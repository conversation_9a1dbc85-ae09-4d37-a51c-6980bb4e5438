"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ManualDispatchController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ManualDispatchController = void 0;
const common_1 = require("@nestjs/common");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const manual_dispatch_service_1 = require("./manual-dispatch.service");
const led_mapping_service_1 = require("../system_config/services/led-mapping.service");
const led_mapping_dto_1 = require("../system_config/dto/led-mapping.dto");
class SendDataDto {
}
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], SendDataDto.prototype, "planValue", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SendDataDto.prototype, "workType", void 0);
class BatchDataItemDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BatchDataItemDto.prototype, "ledId", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], BatchDataItemDto.prototype, "planValue", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BatchDataItemDto.prototype, "workType", void 0);
class BatchSendDataDto {
}
__decorate([
    (0, class_transformer_1.Type)(() => BatchDataItemDto),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], BatchSendDataDto.prototype, "data", void 0);
let ManualDispatchController = ManualDispatchController_1 = class ManualDispatchController {
    constructor(manualDispatchService, ledMappingService) {
        this.manualDispatchService = manualDispatchService;
        this.ledMappingService = ledMappingService;
        this.logger = new common_1.Logger(ManualDispatchController_1.name);
    }
    async getAllConcentrators() {
        return this.manualDispatchService.getAllActiveConcentrators();
    }
    async getLedMapping(ledId) {
        return this.manualDispatchService.getLedMapping(ledId);
    }
    async sendDataToLed(ledId, data) {
        this.logger.log(`收到请求 - LED设备: ${ledId}`);
        this.logger.log(`原始请求体类型: ${typeof data}`);
        this.logger.log(`原始请求体内容:`, data);
        this.logger.log(`请求体完整内容:`, JSON.stringify(data, null, 2));
        this.logger.log(`data.planValue: ${data.planValue} (类型: ${typeof data.planValue})`);
        this.logger.log(`data.workType: ${data.workType} (类型: ${typeof data.workType})`);
        const workType = data.workType || '1';
        this.logger.log(`向LED设备 ${ledId} 发送计划数据: 计划=${data.planValue}, 班次=${workType === '1' ? '白班' : '夜班'}`);
        return this.manualDispatchService.sendDataToLed(ledId, data.planValue, workType);
    }
    async readDataFromLed(ledId) {
        return this.manualDispatchService.readDataFromLed(ledId);
    }
    async createMapping(createMappingDto) {
        this.logger.log(`创建LED设备映射: ${JSON.stringify(createMappingDto)}`);
        return this.ledMappingService.create(createMappingDto);
    }
    async sendBatchDataToLeds(batchData) {
        this.logger.log(`收到批量发送请求`);
        this.logger.log(`请求体类型: ${typeof batchData}`);
        this.logger.log(`请求体内容:`, JSON.stringify(batchData, null, 2));
        if (!batchData || !batchData.data) {
            this.logger.error(`批量发送数据格式错误: batchData=${batchData}, data=${batchData?.data}`);
            throw new Error('批量发送数据格式错误');
        }
        this.logger.log(`批量发送数据到 ${batchData.data.length} 个LED设备`);
        this.logger.log(`设备列表:`, batchData.data.map(d => `LED${d.ledId}:${d.planValue}:${d.workType}`));
        return this.manualDispatchService.sendBatchDataToLeds(batchData.data);
    }
    async testRegisters() {
        this.logger.log(`测试寄存器状态`);
        return this.manualDispatchService.testAllRegisters();
    }
    async debugBatch(batchData) {
        this.logger.log(`调试批量发送`);
        return this.manualDispatchService.debugBatchSend(batchData.data);
    }
    async getAllLedDataByConcentrator(concentratorId) {
        this.logger.log(`获取集中器 ${concentratorId} 下所有LED设备数据`);
        return this.manualDispatchService.getAllLedDataByConcentrator(parseInt(concentratorId));
    }
    async getLedDataHistory(ledId, page = '1', limit = '10') {
        this.logger.log(`获取LED设备 ${ledId} 的数据历史`);
        return this.manualDispatchService.getLedDataHistory(ledId, parseInt(page), parseInt(limit));
    }
    async getRecentDispatchData(page = '1', limit = '20') {
        this.logger.log(`获取最近的派发数据记录`);
        return this.manualDispatchService.getRecentDispatchData(parseInt(page), parseInt(limit));
    }
    async getModbusStatus() {
        this.logger.log('获取ModbusTCP状态');
        return this.manualDispatchService.getModbusStatus();
    }
    async testModbusConnection(testData) {
        this.logger.log(`测试ModbusTCP连接: ${testData.ip}:${testData.port}`);
        return this.manualDispatchService.testModbusConnection(testData.ip, testData.port);
    }
    async testPlanTable() {
        this.logger.log('测试LED_PLAN_INFO表');
        return this.manualDispatchService.testPlanTable();
    }
    async testInsertPlan(data) {
        this.logger.log('测试插入计划数据');
        return this.manualDispatchService.testInsertPlan(data.ledId, data.planValue, data.workType || '1');
    }
};
exports.ManualDispatchController = ManualDispatchController;
__decorate([
    (0, common_1.Get)('concentrators'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "getAllConcentrators", null);
__decorate([
    (0, common_1.Get)('led/:ledId'),
    __param(0, (0, common_1.Param)('ledId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "getLedMapping", null);
__decorate([
    (0, common_1.Post)('led/:ledId/data'),
    __param(0, (0, common_1.Param)('ledId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "sendDataToLed", null);
__decorate([
    (0, common_1.Get)('led/:ledId/data'),
    __param(0, (0, common_1.Param)('ledId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "readDataFromLed", null);
__decorate([
    (0, common_1.Post)('led/mapping'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [led_mapping_dto_1.CreateLedMappingDto]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "createMapping", null);
__decorate([
    (0, common_1.Post)('led/batch-data'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [BatchSendDataDto]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "sendBatchDataToLeds", null);
__decorate([
    (0, common_1.Post)('led/test-registers'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "testRegisters", null);
__decorate([
    (0, common_1.Post)('led/debug-batch'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [BatchSendDataDto]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "debugBatch", null);
__decorate([
    (0, common_1.Get)('concentrator/:concentratorId/led-data'),
    __param(0, (0, common_1.Param)('concentratorId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "getAllLedDataByConcentrator", null);
__decorate([
    (0, common_1.Get)('led/:ledId/history'),
    __param(0, (0, common_1.Param)('ledId')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "getLedDataHistory", null);
__decorate([
    (0, common_1.Get)('data/recent'),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "getRecentDispatchData", null);
__decorate([
    (0, common_1.Get)('modbus/status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "getModbusStatus", null);
__decorate([
    (0, common_1.Post)('modbus/test-connection'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "testModbusConnection", null);
__decorate([
    (0, common_1.Get)('test/plan-table'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "testPlanTable", null);
__decorate([
    (0, common_1.Post)('test/insert-plan'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ManualDispatchController.prototype, "testInsertPlan", null);
exports.ManualDispatchController = ManualDispatchController = ManualDispatchController_1 = __decorate([
    (0, common_1.Controller)('manual-dispatch'),
    __metadata("design:paramtypes", [manual_dispatch_service_1.ManualDispatchService,
        led_mapping_service_1.LedMappingService])
], ManualDispatchController);
//# sourceMappingURL=manual-dispatch.controller.js.map