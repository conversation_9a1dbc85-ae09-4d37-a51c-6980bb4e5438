"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedDataModule = void 0;
const common_1 = require("@nestjs/common");
const led_data_service_1 = require("./led_data.service");
const led_data_controller_1 = require("./led_data.controller");
const typeorm_1 = require("@nestjs/typeorm");
const led_data_entity_1 = require("./entities/led-data.entity");
let LedDataModule = class LedDataModule {
};
exports.LedDataModule = LedDataModule;
exports.LedDataModule = LedDataModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([led_data_entity_1.LedData]),
        ],
        controllers: [led_data_controller_1.LedDataController],
        providers: [led_data_service_1.LedDataService],
        exports: [led_data_service_1.LedDataService],
    })
], LedDataModule);
//# sourceMappingURL=led_data.module.js.map