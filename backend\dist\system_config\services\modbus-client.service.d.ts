export declare class ModbusClientService {
    private readonly logger;
    private client;
    private isRealMode;
    constructor();
    connect(ip: string, port: number, timeout?: number): Promise<boolean>;
    close(): Promise<void>;
    writeRegister(address: number, value: number): Promise<boolean>;
    writeRegisters(address: number, values: number[]): Promise<boolean>;
    readHoldingRegisters(address: number, length: number): Promise<number[]>;
    splitTo16Bit(value: number): number[];
    mergeTo32Bit(highWord: number, lowWord: number): number;
}
