import { DataSource, EntityManager } from 'typeorm';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { Department } from './entities/department.entity';
export declare class DepartmentService {
    private dataSource;
    constructor(dataSource: DataSource);
    create(createDepartmentDto: CreateDepartmentDto): Promise<Department>;
    findAll(params?: any): Promise<{
        items: Department[];
        meta: any;
    }>;
    findOne(id: number, manager?: EntityManager): Promise<Department>;
    update(id: number, updateDepartmentDto: UpdateDepartmentDto): Promise<Department>;
    private updateChildren;
    remove(id: number): Promise<{
        success: boolean;
        message: string;
    }>;
    getTree(): Promise<Department[]>;
}
