import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  PlusIcon, 
  MagnifyingGlassIcon, 
  FunnelIcon,
  ArrowPathIcon,
  BuildingOfficeIcon,
  ListBulletIcon,
  Squares2X2Icon
} from '@heroicons/react/24/outline';
import { useToast } from '../../../contexts/ToastContext';
import PageHeader from '../../../components/common/PageHeader';
import DataTable from '../../../components/common/DataTable';
import DepartmentForm from '../components/DepartmentForm';
import DepartmentTreeView from '../components/DepartmentTreeView';
import departmentService from '../services/departmentService';

const DepartmentManagementPage = () => {
  const [currentDepartment, setCurrentDepartment] = useState(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [viewMode, setViewMode] = useState('list'); // 'list' 或 'tree'
  const [filters, setFilters] = useState({
    parentId: '',
    deptLevel: '',
    isActive: ''
  });

  const toast = useToast();
  const queryClient = useQueryClient();

  // 获取部门列表数据
  const {
    data: departmentsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['departments', currentPage, pageSize, searchTerm, filters],
    queryFn: () => departmentService.getAllDepartments({
      page: currentPage,
      limit: pageSize,
      search: searchTerm,
      ...filters
    }),
    keepPreviousData: true,
    staleTime: 0,
    cacheTime: 1000,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    onError: (error) => {
      console.error('获取部门列表失败:', error);
      toast.error('获取部门列表失败');
    }
  });

  // 获取部门树形数据
  const {
    data: treeData,
    isLoading: isTreeLoading,
    refetch: refetchTree
  } = useQuery({
    queryKey: ['departmentTree'],
    queryFn: departmentService.getDepartmentTree,
    enabled: viewMode === 'tree',
    staleTime: 0,
    cacheTime: 1000,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    onError: (error) => {
      console.error('获取部门树形结构失败:', error);
      toast.error('获取部门树形结构失败');
    }
  });

  // 创建部门
  const createDepartmentMutation = useMutation({
    mutationFn: (data) => departmentService.createDepartment(data),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries(['departments']);
        queryClient.invalidateQueries(['departmentTree']);
        setIsFormOpen(false);
        setCurrentDepartment(null);
        toast.success('部门创建成功');
      } else {
        toast.error(response.message || '部门创建失败');
      }
    },
    onError: (error) => {
      const message = error.response?.data?.message || error.message || '部门创建失败';
      toast.error(message);
    }
  });

  // 更新部门
  const updateDepartmentMutation = useMutation({
    mutationFn: ({ id, data }) => departmentService.updateDepartment(id, data),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries(['departments']);
        queryClient.invalidateQueries(['departmentTree']);
        setIsFormOpen(false);
        setCurrentDepartment(null);
        toast.success('部门更新成功');
      } else {
        toast.error(response.message || '部门更新失败');
      }
    },
    onError: (error) => {
      const message = error.response?.data?.message || error.message || '部门更新失败';
      toast.error(message);
    }
  });

  // 删除部门
  const deleteDepartmentMutation = useMutation({
    mutationFn: (id) => departmentService.deleteDepartment(id),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries(['departments']);
        queryClient.invalidateQueries(['departmentTree']);
        setIsDeleteDialogOpen(false);
        setCurrentDepartment(null);
        toast.success('部门删除成功');
      } else {
        toast.error(response.message || '部门删除失败');
      }
    },
    onError: (error) => {
      const message = error.response?.data?.message || error.message || '部门删除失败';
      toast.error(message);
    }
  });

  // 处理添加部门
  const handleAddDepartment = (data) => {
    createDepartmentMutation.mutate(data);
  };

  // 处理编辑部门
  const handleEditDepartment = (data) => {
    updateDepartmentMutation.mutate({
      id: currentDepartment.DEPT_ID,
      data
    });
  };

  // 处理删除部门
  const handleDeleteDepartment = () => {
    if (currentDepartment) {
      deleteDepartmentMutation.mutate(currentDepartment.DEPT_ID);
    }
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // 处理筛选
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setCurrentPage(1);
  };

  // 处理刷新
  const handleRefresh = () => {
    if (viewMode === 'list') {
      refetch();
    } else {
      refetchTree();
    }
    toast.success('数据已刷新');
  };

  // 处理视图模式切换
  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  // 处理分页
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // 获取数据
  const departments = departmentsData?.data || [];
  const meta = departmentsData?.meta || {
    totalItems: 0,
    itemsPerPage: pageSize,
    currentPage: currentPage,
    totalPages: 0
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <PageHeader
        title="部门管理"
        subtitle="管理系统部门信息和组织架构"
        icon={BuildingOfficeIcon}
      />

      {/* 操作栏 */}
      <div className="bg-tech-dark-800 rounded-tech p-4 border border-tech-dark-600">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* 左侧：搜索和筛选 */}
          <div className="flex flex-col sm:flex-row gap-3 flex-1">
            {/* 搜索框 */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-tech-gray-400" />
              <input
                type="text"
                placeholder="搜索部门编码或名称..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-tech-dark-700 border border-tech-dark-500 rounded-lg text-tech-gray-200 placeholder-tech-gray-400 focus:outline-none focus:ring-2 focus:ring-tech-blue-500 focus:border-transparent"
              />
            </div>

            {/* 筛选器 */}
            <div className="flex gap-2">
              <select
                value={filters.deptLevel}
                onChange={(e) => handleFilterChange({ ...filters, deptLevel: e.target.value })}
                className="px-3 py-2 bg-tech-dark-700 border border-tech-dark-500 rounded-lg text-tech-gray-200 focus:outline-none focus:ring-2 focus:ring-tech-blue-500"
              >
                <option value="">所有层级</option>
                <option value="1">一级部门</option>
                <option value="2">二级部门</option>
                <option value="3">三级部门</option>
              </select>

              <select
                value={filters.isActive}
                onChange={(e) => handleFilterChange({ ...filters, isActive: e.target.value })}
                className="px-3 py-2 bg-tech-dark-700 border border-tech-dark-500 rounded-lg text-tech-gray-200 focus:outline-none focus:ring-2 focus:ring-tech-blue-500"
              >
                <option value="">所有状态</option>
                <option value="1">启用</option>
                <option value="0">禁用</option>
              </select>
            </div>
          </div>

          {/* 右侧：视图切换和操作按钮 */}
          <div className="flex items-center gap-3">
            {/* 视图模式切换 */}
            <div className="flex bg-tech-dark-700 rounded-lg p-1">
              <button
                onClick={() => handleViewModeChange('list')}
                className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'list'
                    ? 'bg-tech-blue-600 text-white'
                    : 'text-tech-gray-400 hover:text-tech-gray-200'
                }`}
              >
                <ListBulletIcon className="h-4 w-4 mr-1" />
                列表
              </button>
              <button
                onClick={() => handleViewModeChange('tree')}
                className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'tree'
                    ? 'bg-tech-blue-600 text-white'
                    : 'text-tech-gray-400 hover:text-tech-gray-200'
                }`}
              >
                <Squares2X2Icon className="h-4 w-4 mr-1" />
                树形
              </button>
            </div>

            {/* 刷新按钮 */}
            <button
              onClick={handleRefresh}
              className="flex items-center px-3 py-2 bg-tech-dark-700 hover:bg-tech-dark-600 border border-tech-dark-500 rounded-lg text-tech-gray-200 transition-colors"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              刷新
            </button>

            {/* 添加部门按钮 */}
            <button
              onClick={() => {
                setCurrentDepartment(null);
                setIsFormOpen(true);
              }}
              className="flex items-center px-4 py-2 bg-tech-blue-600 hover:bg-tech-blue-700 text-white rounded-lg transition-colors"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              添加部门
            </button>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      {viewMode === 'list' ? (
        /* 列表视图 */
        <div className="bg-tech-dark-800 rounded-tech border border-tech-dark-600">
          <DataTable
            data={departments}
            columns={getTableColumns()}
            isLoading={isLoading}
            serverSide={true}
            totalItems={meta.totalItems}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            emptyMessage="暂无部门数据"
          />
        </div>
      ) : (
        /* 树形视图 */
        <div className="bg-tech-dark-800 rounded-tech border border-tech-dark-600 p-6">
          <DepartmentTreeView
            data={treeData?.data || []}
            loading={isTreeLoading}
            onEdit={(department) => {
              setCurrentDepartment(department);
              setIsFormOpen(true);
            }}
            onDelete={(department) => {
              setCurrentDepartment(department);
              setIsDeleteDialogOpen(true);
            }}
            onAddChild={(parentDepartment) => {
              setCurrentDepartment({ PARENT_DEPT_ID: parentDepartment.DEPT_ID });
              setIsFormOpen(true);
            }}
          />
        </div>
      )}

      {/* 部门表单弹窗 */}
      {isFormOpen && (
        <DepartmentForm
          department={currentDepartment}
          onSubmit={currentDepartment?.DEPT_ID ? handleEditDepartment : handleAddDepartment}
          onCancel={() => {
            setIsFormOpen(false);
            setCurrentDepartment(null);
          }}
          loading={createDepartmentMutation.isLoading || updateDepartmentMutation.isLoading}
        />
      )}

      {/* 删除确认弹窗 */}
      {isDeleteDialogOpen && currentDepartment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-tech-dark-800 rounded-tech p-6 w-full max-w-md border border-tech-dark-600">
            <h3 className="text-lg font-semibold text-tech-gray-200 mb-4">确认删除</h3>
            <p className="text-tech-gray-400 mb-6">
              确定要删除部门 "{currentDepartment.DEPT_NAME}" 吗？此操作不可撤销。
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setIsDeleteDialogOpen(false);
                  setCurrentDepartment(null);
                }}
                className="px-4 py-2 bg-tech-dark-700 hover:bg-tech-dark-600 text-tech-gray-200 rounded-lg transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleDeleteDepartment}
                disabled={deleteDepartmentMutation.isLoading}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                {deleteDepartmentMutation.isLoading ? '删除中...' : '确认删除'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // 表格列配置
  function getTableColumns() {
    return [
      {
        header: '部门编码',
        accessor: 'DEPT_CODE',
        sortable: true,
        cell: (row) => (
          <div className="flex items-center">
            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-tech-blue-900/30 text-tech-blue-300 border border-tech-blue-500/30">
              {row.DEPT_CODE}
            </span>
          </div>
        )
      },
      {
        header: '部门信息',
        accessor: 'DEPT_NAME',
        sortable: true,
        cell: (row) => (
          <div className="flex flex-col space-y-1">
            <div className="font-medium text-tech-gray-200">{row.DEPT_NAME}</div>
            <div className="text-xs text-tech-gray-400">
              {row.PARENT_DEPT_NAME && `上级: ${row.PARENT_DEPT_NAME}`}
              {row.DEPT_LEVEL && ` • 层级: ${row.DEPT_LEVEL}`}
            </div>
          </div>
        )
      },
      {
        header: '负责人',
        accessor: 'MANAGER_USERNAME',
        cell: (row) => (
          <div className="text-tech-gray-200">
            {row.MANAGER_USERNAME || '-'}
          </div>
        )
      },
      {
        header: '描述',
        accessor: 'DESCRIPTION',
        cell: (row) => (
          <div className="text-tech-gray-400 max-w-xs truncate">
            {row.DESCRIPTION || '-'}
          </div>
        )
      },
      {
        header: '状态',
        accessor: 'IS_ACTIVE',
        cell: (row) => (
          <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
            row.IS_ACTIVE === 1
              ? 'bg-green-900/30 text-green-300 border border-green-500/30'
              : 'bg-red-900/30 text-red-300 border border-red-500/30'
          }`}>
            {row.IS_ACTIVE === 1 ? '启用' : '禁用'}
          </span>
        )
      },
      {
        header: '排序',
        accessor: 'SORT_ORDER',
        align: 'center',
        cell: (row) => (
          <div className="text-tech-gray-400 text-center">
            {row.SORT_ORDER || 0}
          </div>
        )
      },
      {
        header: '操作',
        accessor: 'actions',
        align: 'center',
        cell: (row) => (
          <div className="flex items-center justify-center space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setCurrentDepartment(row);
                setIsFormOpen(true);
              }}
              className="text-tech-blue-400 hover:text-tech-blue-300 transition-colors"
              title="编辑"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setCurrentDepartment(row);
                setIsDeleteDialogOpen(true);
              }}
              className="text-red-400 hover:text-red-300 transition-colors"
              title="删除"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        )
      }
    ];
  }
};

export default DepartmentManagementPage;
