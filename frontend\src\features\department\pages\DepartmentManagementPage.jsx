import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  PlusIcon, 
  MagnifyingGlassIcon, 
  FunnelIcon,
  ArrowPathIcon,
  BuildingOfficeIcon,
  ListBulletIcon,
  Squares2X2Icon
} from '@heroicons/react/24/outline';
import { useToast } from '../../../contexts/ToastContext';
import PageHeader from '../../../components/common/PageHeader';
import DataTable from '../../../components/common/DataTable';
import DepartmentForm from '../components/DepartmentForm';
import DepartmentTreeView from '../components/DepartmentTreeView';
import departmentService from '../services/departmentService';

const DepartmentManagementPage = () => {
  const [currentDepartment, setCurrentDepartment] = useState(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [viewMode, setViewMode] = useState('list'); // 'list' 或 'tree'
  const [filters, setFilters] = useState({
    parentId: '',
    deptLevel: '',
    isActive: ''
  });

  const toast = useToast();
  const queryClient = useQueryClient();

  // 获取部门列表数据
  const {
    data: departmentsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['departments', currentPage, pageSize, searchTerm, filters],
    queryFn: () => departmentService.getAllDepartments({
      page: currentPage,
      limit: pageSize,
      search: searchTerm,
      ...filters
    }),
    keepPreviousData: true,
    staleTime: 0,
    cacheTime: 1000,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    onError: (error) => {
      console.error('获取部门列表失败:', error);
      toast.error('获取部门列表失败');
    }
  });

  // 获取部门树形数据
  const {
    data: treeData,
    isLoading: isTreeLoading,
    refetch: refetchTree
  } = useQuery({
    queryKey: ['departmentTree'],
    queryFn: departmentService.getDepartmentTree,
    enabled: viewMode === 'tree',
    staleTime: 0,
    cacheTime: 1000,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    onError: (error) => {
      console.error('获取部门树形结构失败:', error);
      toast.error('获取部门树形结构失败');
    }
  });

  // 创建部门
  const createDepartmentMutation = useMutation({
    mutationFn: (data) => departmentService.createDepartment(data),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries(['departments']);
        queryClient.invalidateQueries(['departmentTree']);
        setIsFormOpen(false);
        setCurrentDepartment(null);
        toast.success('部门创建成功');
      } else {
        toast.error(response.message || '部门创建失败');
      }
    },
    onError: (error) => {
      const message = error.response?.data?.message || error.message || '部门创建失败';
      toast.error(message);
    }
  });

  // 更新部门
  const updateDepartmentMutation = useMutation({
    mutationFn: ({ id, data }) => departmentService.updateDepartment(id, data),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries(['departments']);
        queryClient.invalidateQueries(['departmentTree']);
        setIsFormOpen(false);
        setCurrentDepartment(null);
        toast.success('部门更新成功');
      } else {
        toast.error(response.message || '部门更新失败');
      }
    },
    onError: (error) => {
      const message = error.response?.data?.message || error.message || '部门更新失败';
      toast.error(message);
    }
  });

  // 删除部门
  const deleteDepartmentMutation = useMutation({
    mutationFn: (id) => departmentService.deleteDepartment(id),
    onSuccess: (response) => {
      if (response.success) {
        queryClient.invalidateQueries(['departments']);
        queryClient.invalidateQueries(['departmentTree']);
        setIsDeleteDialogOpen(false);
        setCurrentDepartment(null);
        toast.success('部门删除成功');
      } else {
        toast.error(response.message || '部门删除失败');
      }
    },
    onError: (error) => {
      const message = error.response?.data?.message || error.message || '部门删除失败';
      toast.error(message);
    }
  });

  // 处理添加部门
  const handleAddDepartment = (data) => {
    createDepartmentMutation.mutate(data);
  };

  // 处理编辑部门
  const handleEditDepartment = (data) => {
    updateDepartmentMutation.mutate({
      id: currentDepartment.DEPT_ID,
      data
    });
  };

  // 处理删除部门
  const handleDeleteDepartment = () => {
    if (currentDepartment) {
      deleteDepartmentMutation.mutate(currentDepartment.DEPT_ID);
    }
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // 处理筛选
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setCurrentPage(1);
  };

  // 处理刷新
  const handleRefresh = () => {
    if (viewMode === 'list') {
      refetch();
    } else {
      refetchTree();
    }
    toast.success('数据已刷新');
  };

  // 处理视图模式切换
  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  // 处理分页
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // 获取数据 - 修复数据显示问题
  const departments = departmentsData?.data || [];
  const meta = departmentsData?.meta || {
    totalItems: 0,
    itemsPerPage: pageSize,
    currentPage: currentPage,
    totalPages: 0
  };

  // 调试日志
  console.log('部门数据:', departmentsData);
  console.log('部门列表:', departments);
  console.log('元数据:', meta);

  // 表格列配置
  const getTableColumns = () => {
    return [
      {
        header: '部门编码',
        accessor: 'DEPT_CODE',
        sortable: true,
        cell: (row) => (
          <div className="flex items-center">
            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-tech-blue-900/30 text-tech-blue-300 border border-tech-blue-500/30">
              {row.DEPT_CODE}
            </span>
          </div>
        )
      },
      {
        header: '部门信息',
        accessor: 'DEPT_NAME',
        sortable: true,
        cell: (row) => (
          <div className="flex flex-col space-y-1">
            <div className="font-medium text-tech-gray-200">{row.DEPT_NAME}</div>
            <div className="text-xs text-tech-gray-400">
              {row.PARENT_DEPT_NAME && `上级: ${row.PARENT_DEPT_NAME}`}
              {row.DEPT_LEVEL && ` • 层级: ${row.DEPT_LEVEL}`}
            </div>
          </div>
        )
      },
      {
        header: '负责人',
        accessor: 'MANAGER_USERNAME',
        cell: (row) => (
          <div className="text-tech-gray-200">
            {row.MANAGER_USERNAME || '-'}
          </div>
        )
      },
      {
        header: '描述',
        accessor: 'DESCRIPTION',
        cell: (row) => (
          <div className="text-tech-gray-400 max-w-xs truncate">
            {row.DESCRIPTION || '-'}
          </div>
        )
      },
      {
        header: '状态',
        accessor: 'IS_ACTIVE',
        cell: (row) => (
          <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
            row.IS_ACTIVE === 1
              ? 'bg-green-900/30 text-green-300 border border-green-500/30'
              : 'bg-red-900/30 text-red-300 border border-red-500/30'
          }`}>
            <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
              row.IS_ACTIVE === 1 ? 'bg-green-400' : 'bg-red-400'
            }`}></span>
            {row.IS_ACTIVE === 1 ? '启用' : '禁用'}
          </span>
        )
      },
      {
        header: '排序',
        accessor: 'SORT_ORDER',
        align: 'center',
        cell: (row) => (
          <div className="text-tech-gray-400 text-center">
            {row.SORT_ORDER || 0}
          </div>
        )
      },
      {
        header: '操作',
        accessor: 'actions',
        align: 'center',
        cell: (row) => (
          <div className="flex items-center justify-center space-x-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setCurrentDepartment(row);
                setIsFormOpen(true);
              }}
              className="group p-2 text-tech-blue-400 hover:text-tech-blue-300 hover:bg-tech-blue-900/30 rounded-lg transition-all duration-200 transform hover:scale-110"
              title="编辑部门"
            >
              <svg className="h-4 w-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setCurrentDepartment(row);
                setIsDeleteDialogOpen(true);
              }}
              className="group p-2 text-tech-red-400 hover:text-tech-red-300 hover:bg-tech-red-900/30 rounded-lg transition-all duration-200 transform hover:scale-110"
              title="删除部门"
            >
              <svg className="h-4 w-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        )
      }
    ];
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-tech-dark-800 via-tech-dark-700 to-tech-dark-800">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-tech-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-tech-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 p-4 sm:p-6 lg:p-8">
        {/* 精美的页面头部 */}
        <div className="mb-8">
          <div className="bg-tech-dark-600/50 backdrop-blur-md border border-tech-dark-500/50 rounded-2xl p-6 shadow-2xl">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-tech-blue-gradient rounded-xl flex items-center justify-center shadow-lg">
                  <BuildingOfficeIcon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl sm:text-2xl font-bold text-gradient-blue">部门管理</h1>
                  <p className="text-tech-gray-400 mt-1">智能管理组织架构，优化部门结构</p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
                {/* 视图模式切换 */}
                <div className="flex bg-tech-dark-700/50 rounded-xl p-1 backdrop-blur-sm border border-tech-dark-500/50">
                  <button
                    onClick={() => handleViewModeChange('list')}
                    className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                      viewMode === 'list'
                        ? 'bg-tech-blue-gradient text-white shadow-lg'
                        : 'text-tech-gray-400 hover:text-tech-gray-200 hover:bg-tech-dark-600/50'
                    }`}
                  >
                    <ListBulletIcon className="h-4 w-4 mr-2" />
                    列表视图
                  </button>
                  <button
                    onClick={() => handleViewModeChange('tree')}
                    className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                      viewMode === 'tree'
                        ? 'bg-tech-blue-gradient text-white shadow-lg'
                        : 'text-tech-gray-400 hover:text-tech-gray-200 hover:bg-tech-dark-600/50'
                    }`}
                  >
                    <Squares2X2Icon className="h-4 w-4 mr-2" />
                    树形视图
                  </button>
                </div>

                {/* 刷新按钮 */}
                <button
                  onClick={handleRefresh}
                  className="group relative inline-flex items-center justify-center px-4 py-2.5 border border-tech-green-400/50 text-sm font-medium rounded-xl shadow-lg text-tech-green-400 bg-tech-dark-700/50 backdrop-blur-sm hover:bg-tech-green-900/30 hover:border-tech-green-400 focus:outline-none focus:ring-2 focus:ring-tech-green-500 transition-all duration-300"
                >
                  <div className="absolute inset-0 bg-tech-green-gradient opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300"></div>
                  <ArrowPathIcon className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" />
                  <span className="relative z-10">刷新数据</span>
                </button>

                {/* 添加部门按钮 */}
                <button
                  onClick={() => {
                    setCurrentDepartment(null);
                    setIsFormOpen(true);
                  }}
                  className="group relative inline-flex items-center justify-center px-6 py-2.5 bg-tech-blue-gradient text-white text-sm font-medium rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-tech-blue-500 transition-all duration-300 transform hover:scale-105"
                >
                  <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 rounded-xl transition-opacity duration-300"></div>
                  <PlusIcon className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" />
                  <span className="relative z-10">添加部门</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 搜索和筛选栏 */}
        <div className="mb-6">
          <div className="bg-tech-dark-600/30 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-4 shadow-lg">
            <div className="flex flex-col lg:flex-row lg:items-center gap-4">
              {/* 搜索框 */}
              <div className="relative flex-1 max-w-md">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-tech-gray-400" />
                <input
                  type="text"
                  placeholder="搜索部门编码或名称..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-tech-dark-700/50 border border-tech-dark-500/50 rounded-xl text-tech-gray-200 placeholder-tech-gray-400 focus:outline-none focus:ring-2 focus:ring-tech-blue-500 focus:border-tech-blue-500/50 backdrop-blur-sm transition-all duration-300"
                />
              </div>

              {/* 筛选器 */}
              <div className="flex flex-wrap gap-3">
                <select
                  value={filters.deptLevel}
                  onChange={(e) => handleFilterChange({ ...filters, deptLevel: e.target.value })}
                  className="px-4 py-3 bg-tech-dark-700/50 border border-tech-dark-500/50 rounded-xl text-tech-gray-200 focus:outline-none focus:ring-2 focus:ring-tech-blue-500 backdrop-blur-sm transition-all duration-300"
                >
                  <option value="">所有层级</option>
                  <option value="1">一级部门</option>
                  <option value="2">二级部门</option>
                  <option value="3">三级部门</option>
                </select>

                <select
                  value={filters.isActive}
                  onChange={(e) => handleFilterChange({ ...filters, isActive: e.target.value })}
                  className="px-4 py-3 bg-tech-dark-700/50 border border-tech-dark-500/50 rounded-xl text-tech-gray-200 focus:outline-none focus:ring-2 focus:ring-tech-blue-500 backdrop-blur-sm transition-all duration-300"
                >
                  <option value="">所有状态</option>
                  <option value="1">启用</option>
                  <option value="0">禁用</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* 统计信息卡片 */}
        {departmentsData && (
          <div className="mb-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* 总部门数 */}
            <div className="bg-tech-dark-600/40 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-tech-blue-gradient rounded-xl flex items-center justify-center shadow-md">
                  <BuildingOfficeIcon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{meta.totalItems || 0}</div>
                  <div className="text-sm text-tech-gray-400">总部门数</div>
                </div>
              </div>
            </div>

            {/* 启用部门 */}
            <div className="bg-tech-dark-600/40 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-tech-green-gradient rounded-xl flex items-center justify-center shadow-md">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {departments?.filter(dept => dept.IS_ACTIVE === 1).length || 0}
                  </div>
                  <div className="text-sm text-tech-gray-400">启用部门</div>
                </div>
              </div>
            </div>

            {/* 禁用部门 */}
            <div className="bg-tech-dark-600/40 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-tech-red-gradient rounded-xl flex items-center justify-center shadow-md">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">
                    {departments?.filter(dept => dept.IS_ACTIVE === 0).length || 0}
                  </div>
                  <div className="text-sm text-tech-gray-400">禁用部门</div>
                </div>
              </div>
            </div>

            {/* 当前页数据 */}
            <div className="bg-tech-dark-600/40 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-tech-purple-gradient rounded-xl flex items-center justify-center shadow-md">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">{departments?.length || 0}</div>
                  <div className="text-sm text-tech-gray-400">当前页</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 内容区域 */}
        {viewMode === 'list' ? (
          /* 列表视图 */
          <div className="bg-tech-dark-600/30 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl shadow-2xl overflow-hidden">
            <DataTable
              data={departments}
              columns={getTableColumns()}
              isLoading={isLoading}
              serverSide={true}
              totalItems={meta.totalItems}
              pageSize={pageSize}
              currentPage={currentPage}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              emptyMessage="暂无部门数据"
              mobileCardView={true}
              renderMobileCard={(department) => (
                <div className="bg-tech-dark-700/50 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]">
                  {/* 卡片头部 */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-tech-blue-gradient rounded-lg flex items-center justify-center shadow-md">
                        <BuildingOfficeIcon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <div className="font-semibold text-tech-gray-200">{department.DEPT_NAME}</div>
                        <div className="text-sm text-tech-gray-400">编码: {department.DEPT_CODE}</div>
                      </div>
                    </div>

                    {/* 状态徽章 */}
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      department.IS_ACTIVE === 1
                        ? 'bg-tech-green-900/30 text-tech-green-300 border border-tech-green-500/30'
                        : 'bg-tech-red-900/30 text-tech-red-300 border border-tech-red-500/30'
                    }`}>
                      {department.IS_ACTIVE === 1 ? '启用' : '禁用'}
                    </div>
                  </div>

                  {/* 卡片内容 */}
                  <div className="space-y-3 mb-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-tech-dark-800/30 rounded-lg p-3">
                        <div className="text-xs text-tech-gray-400 mb-1">上级部门</div>
                        <div className="text-sm font-medium text-tech-gray-200">
                          {department.PARENT_DEPT_NAME || '无'}
                        </div>
                      </div>
                      <div className="bg-tech-dark-800/30 rounded-lg p-3">
                        <div className="text-xs text-tech-gray-400 mb-1">部门层级</div>
                        <div className="text-sm font-medium text-tech-purple-300">
                          第{department.DEPT_LEVEL || 1}级
                        </div>
                      </div>
                    </div>

                    {department.DESCRIPTION && (
                      <div className="bg-tech-dark-800/30 rounded-lg p-3">
                        <div className="text-xs text-tech-gray-400 mb-1">描述</div>
                        <div className="text-sm text-tech-gray-300 truncate">
                          {department.DESCRIPTION}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-end space-x-2 pt-3 border-t border-tech-dark-500/50">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setCurrentDepartment(department);
                        setIsFormOpen(true);
                      }}
                      className="p-2 text-tech-blue-400 hover:text-tech-blue-300 hover:bg-tech-blue-900/30 rounded-lg transition-all duration-200"
                      title="编辑部门"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setCurrentDepartment(department);
                        setIsDeleteDialogOpen(true);
                      }}
                      className="p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-all duration-200"
                      title="删除部门"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              )}
              renderPageSizeSelector={() => (
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-tech-gray-300 text-sm">
                  <span className="hidden sm:inline">每页显示:</span>
                  <span className="sm:hidden text-xs">每页:</span>
                  <select
                    value={pageSize}
                    onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                    className="bg-tech-dark-600 border border-tech-dark-500 text-tech-gray-200 rounded-lg py-1.5 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-tech-blue-500 transition-all duration-200"
                  >
                    <option value={10}>10条</option>
                    <option value={20}>20条</option>
                    <option value={50}>50条</option>
                    <option value={100}>100条</option>
                  </select>
                </div>
              )}
            />
          </div>
        ) : (
          /* 树形视图 */
          <div className="bg-tech-dark-600/30 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl shadow-2xl p-6">
            <DepartmentTreeView
              data={treeData?.data || []}
              loading={isTreeLoading}
              onEdit={(department) => {
                setCurrentDepartment(department);
                setIsFormOpen(true);
              }}
              onDelete={(department) => {
                setCurrentDepartment(department);
                setIsDeleteDialogOpen(true);
              }}
              onAddChild={(parentDepartment) => {
                setCurrentDepartment({ PARENT_DEPT_ID: parentDepartment.DEPT_ID });
                setIsFormOpen(true);
              }}
            />
          </div>
        )}

        {/* 部门表单弹窗 */}
        {isFormOpen && (
          <DepartmentForm
            department={currentDepartment}
            onSubmit={currentDepartment?.DEPT_ID ? handleEditDepartment : handleAddDepartment}
            onCancel={() => {
              setIsFormOpen(false);
              setCurrentDepartment(null);
            }}
            loading={createDepartmentMutation.isLoading || updateDepartmentMutation.isLoading}
          />
        )}

        {/* 删除确认弹窗 */}
        {isDeleteDialogOpen && currentDepartment && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-tech-dark-800/90 backdrop-blur-md rounded-2xl p-6 w-full max-w-md border border-tech-dark-600/50 shadow-2xl">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-red-500/20 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-tech-gray-200">确认删除</h3>
                  <p className="text-sm text-tech-gray-400">此操作不可撤销</p>
                </div>
              </div>

              <div className="bg-tech-dark-700/50 rounded-xl p-4 mb-6">
                <p className="text-tech-gray-300">
                  确定要删除部门 <span className="font-semibold text-tech-blue-300">"{currentDepartment.DEPT_NAME}"</span> 吗？
                </p>
                <p className="text-sm text-tech-gray-400 mt-2">
                  删除后该部门的所有子部门和关联数据也将受到影响。
                </p>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setIsDeleteDialogOpen(false);
                    setCurrentDepartment(null);
                  }}
                  className="px-4 py-2 bg-tech-dark-700/50 hover:bg-tech-dark-600 text-tech-gray-200 rounded-xl transition-all duration-200 border border-tech-dark-500/50"
                >
                  取消
                </button>
                <button
                  onClick={handleDeleteDepartment}
                  disabled={deleteDepartmentMutation.isLoading}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                >
                  {deleteDepartmentMutation.isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span>删除中...</span>
                    </div>
                  ) : (
                    '确认删除'
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

};

export default DepartmentManagementPage;
