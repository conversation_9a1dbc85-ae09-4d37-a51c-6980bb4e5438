import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { PencilIcon, TrashIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Tree, TreeNode } from 'react-organizational-chart';

// 导入通用组件
import DataTable from '../../../components/common/DataTable';
import FormModal from '../../../components/common/FormModal';
import FormField from '../../../components/common/FormField';
import ConfirmDialog from '../../../components/common/ConfirmDialog';
import { useToast } from '../../../contexts/ToastContext';

// 导入服务
import departmentService from '../services/departmentService';

const DepartmentManagementPage = () => {
  // 基本状态
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [currentDepartment, setCurrentDepartment] = useState(null);
  const [viewMode, setViewMode] = useState('list'); // 'list' 或 'tree'
  const [searchParams, setSearchParams] = useState({
    page: 1,
    limit: 10,
    search: '',
    parentId: '',
    deptLevel: '',
    isActive: ''
  });
  const [departmentOptions, setDepartmentOptions] = useState([]);
  const [activeFilters, setActiveFilters] = useState([]);
  
  const queryClient = useQueryClient();
  const toast = useToast();

  // React Hook Form
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
    getValues
  } = useForm();
  
  // 筛选表单
  const {
    register: registerFilter,
    handleSubmit: handleSubmitFilter,
    reset: resetFilter,
    formState: { errors: filterErrors },
    setValue: setFilterValue,
    getValues: getFilterValues
  } = useForm();

  // 获取部门列表
  const { data, isLoading, error } = useQuery({
    queryKey: ['departments', searchParams],
    queryFn: () => departmentService.getAllDepartments(searchParams),
    keepPreviousData: true,
  });

  // 获取部门树形结构
  const { data: treeData, isLoading: isTreeLoading } = useQuery({
    queryKey: ['departmentTree'],
    queryFn: () => departmentService.getDepartmentTree(),
    enabled: viewMode === 'tree',
  });

  // 获取部门选项
  const departmentOptionsQuery = useQuery({
    queryKey: ['departmentOptions'],
    queryFn: departmentService.getDepartmentOptions,
    onSuccess: (data) => {
      if (data && Array.isArray(data)) {
        setDepartmentOptions(data);
      }
    }
  });

  // 创建部门
  const createDepartmentMutation = useMutation({
    mutationFn: (data) => departmentService.createDepartment(data),
    onSuccess: () => {
      queryClient.invalidateQueries(['departments']);
      queryClient.invalidateQueries(['departmentTree']);
      queryClient.invalidateQueries(['departmentOptions']);
      setIsAddModalOpen(false);
      reset();
      toast.success('部门创建成功');
    },
    onError: (error) => {
      toast.error(`创建失败: ${error.message}`);
    }
  });

  // 更新部门
  const updateDepartmentMutation = useMutation({
    mutationFn: ({ id, data }) => departmentService.updateDepartment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['departments']);
      queryClient.invalidateQueries(['departmentTree']);
      queryClient.invalidateQueries(['departmentOptions']);
      setIsEditModalOpen(false);
      reset();
      toast.success('部门更新成功');
    },
    onError: (error) => {
      toast.error(`更新失败: ${error.message}`);
    }
  });

  // 删除部门
  const deleteDepartmentMutation = useMutation({
    mutationFn: (id) => departmentService.deleteDepartment(id),
    onSuccess: () => {
      queryClient.invalidateQueries(['departments']);
      queryClient.invalidateQueries(['departmentTree']);
      queryClient.invalidateQueries(['departmentOptions']);
      setIsDeleteDialogOpen(false);
      setCurrentDepartment(null);
      toast.success('部门删除成功');
    },
    onError: (error) => {
      toast.error(`删除失败: ${error.message}`);
    }
  });

  // 组件加载时获取部门选项
  useEffect(() => {
    queryClient.invalidateQueries(['departmentOptions']);
  }, [queryClient]);

  // 处理添加部门
  const handleAddDepartment = (data) => {
    createDepartmentMutation.mutate(data);
  };

  // 处理编辑按钮点击
  const handleEdit = (department) => {
    reset();
    setCurrentDepartment(department);
    
    Object.keys(department).forEach(key => {
      setValue(key, department[key]);
    });
    
    setIsEditModalOpen(true);
  };

  // 处理编辑部门提交
  const handleEditSubmit = (data) => {
    if (currentDepartment) {
      updateDepartmentMutation.mutate({ id: currentDepartment.DEPT_ID, data });
    }
  };

  // 处理删除部门
  const handleDeleteDepartment = () => {
    if (currentDepartment) {
      deleteDepartmentMutation.mutate(currentDepartment.DEPT_ID);
    }
  };

  // 打开删除对话框
  const openDeleteDialog = (department) => {
    setCurrentDepartment(department);
    setIsDeleteDialogOpen(true);
  };

  // 处理页面变化
  const handlePageChange = (page) => {
    setSearchParams((prev) => ({ ...prev, page }));
  };

  // 处理每页条目数变化
  const handlePageSizeChange = (newLimit) => {
    setSearchParams((prev) => ({ ...prev, limit: newLimit, page: 1 }));
  };

  // 处理搜索
  const handleSearch = (term) => {
    setSearchParams((prev) => ({ ...prev, search: term, page: 1 }));
  };

  // 处理筛选
  const handleFilter = (filterData) => {
    // 构建新的筛选条件
    const newFilters = [];
    const newParams = { ...searchParams, page: 1 };
    
    if (filterData.parentId) {
      newParams.parentId = filterData.parentId;
      const parentName = departmentOptions.find(d => d.value === parseInt(filterData.parentId))?.label || '未知';
      newFilters.push({
        id: 'parentId',
        label: `上级部门: ${parentName}`
      });
    } else {
      newParams.parentId = '';
    }
    
    if (filterData.deptLevel) {
      newParams.deptLevel = filterData.deptLevel;
      newFilters.push({
        id: 'deptLevel',
        label: `部门层级: ${filterData.deptLevel}`
      });
    } else {
      newParams.deptLevel = '';
    }
    
    if (filterData.isActive !== undefined && filterData.isActive !== '') {
      newParams.isActive = filterData.isActive;
      newFilters.push({
        id: 'isActive',
        label: `状态: ${filterData.isActive === '1' ? '启用' : '禁用'}`
      });
    } else {
      newParams.isActive = '';
    }
    
    setActiveFilters(newFilters);
    setSearchParams(newParams);
    setIsFilterModalOpen(false);
  };

  // 移除筛选条件
  const removeFilter = (filterId) => {
    setActiveFilters(prev => prev.filter(f => f.id !== filterId));
    setSearchParams(prev => ({ ...prev, [filterId]: '', page: 1 }));
  };

  // 清除所有筛选条件
  const clearAllFilters = () => {
    setActiveFilters([]);
    setSearchParams(prev => ({
      ...prev,
      parentId: '',
      deptLevel: '',
      isActive: '',
      page: 1
    }));
    resetFilter();
  };

  // 切换视图模式
  const toggleViewMode = () => {
    setViewMode(prev => prev === 'list' ? 'tree' : 'list');
  };

  // 表格列配置
  const columns = [
    {
      header: '部门编码',
      accessor: 'DEPT_CODE',
      sortable: true
    },
    {
      header: '部门名称',
      accessor: 'DEPT_NAME',
      sortable: true
    },
    {
      header: '上级部门',
      accessor: 'PARENT_DEPT_NAME',
      sortable: true,
      cell: (row) => row.PARENT_DEPT_NAME || '-'
    },
    {
      header: '部门层级',
      accessor: 'DEPT_LEVEL',
      sortable: true
    },
    {
      header: '排序',
      accessor: 'SORT_ORDER',
      sortable: true
    },
    {
      header: '状态',
      accessor: 'IS_ACTIVE',
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
          row.IS_ACTIVE === 1 
            ? 'bg-tech-green-900/30 text-tech-green-300 border border-tech-green-500/30' 
            : 'bg-tech-red-900/30 text-tech-red-300 border border-tech-red-500/30'
        }`}>
          <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
            row.IS_ACTIVE === 1 ? 'bg-tech-green-400' : 'bg-tech-red-400'
          }`}></span>
          {row.IS_ACTIVE === 1 ? '启用' : '禁用'}
        </span>
      )
    },
    {
      header: '操作',
      accessor: 'actions',
      cell: (row) => (
        <div className="flex items-center justify-center sm:justify-start space-x-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleEdit(row);
            }}
            className="group p-2 text-tech-blue-400 hover:text-tech-blue-300 hover:bg-tech-blue-900/30 rounded-lg transition-all duration-200 transform hover:scale-110"
            title="编辑部门"
          >
            <PencilIcon className="h-4 w-4 group-hover:scale-110 transition-transform duration-200" />
          </button>

          <button
            onClick={(e) => {
              e.stopPropagation();
              openDeleteDialog(row);
            }}
            className="group p-2 text-tech-red-400 hover:text-tech-red-300 hover:bg-tech-red-900/30 rounded-lg transition-all duration-200 transform hover:scale-110"
            title="删除部门"
          >
            <TrashIcon className="h-4 w-4 group-hover:scale-110 transition-transform duration-200" />
          </button>
        </div>
      )
    }
  ];

  // 处理表格行点击
  const handleRowClick = (department) => {
    handleEdit(department);
  };

  // 关闭所有模态框时重置表单
  useEffect(() => {
    if (!isAddModalOpen && !isEditModalOpen) {
      reset();
    }
  }, [isAddModalOpen, isEditModalOpen, reset]);

  // 渲染树节点组件
  const renderTreeNode = (node) => {
    return (
      <TreeNode 
        key={node.DEPT_ID} 
        label={
          <div className="bg-tech-dark-700 border border-tech-dark-500 rounded-lg p-3 shadow-md hover:shadow-lg transition-all duration-300 hover:bg-tech-dark-600 cursor-pointer" onClick={() => handleEdit(node)}>
            <div className="font-medium text-tech-gray-200">{node.DEPT_NAME}</div>
            <div className="text-xs text-tech-gray-400 mt-1">编码: {node.DEPT_CODE}</div>
          </div>
        }
      >
        {node.children?.map(child => renderTreeNode(child))}
      </TreeNode>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-tech-dark-800 via-tech-dark-700 to-tech-dark-800">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-tech-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-tech-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 p-4 sm:p-6 lg:p-8">
        {/* 主要内容区域 */}
        {/* 剩余页面内容将在下一部分中添加 */}
      </div>
    </div>
  );
};

export default DepartmentManagementPage; 