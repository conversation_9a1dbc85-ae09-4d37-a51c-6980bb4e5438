"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SystemConfigController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const system_config_service_1 = require("../services/system-config.service");
const system_config_dto_1 = require("../dto/system-config.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let SystemConfigController = SystemConfigController_1 = class SystemConfigController {
    constructor(systemConfigService) {
        this.systemConfigService = systemConfigService;
        this.logger = new common_1.Logger(SystemConfigController_1.name);
    }
    async findAll(query) {
        this.logger.log(`接收到查询参数: ${JSON.stringify(query)}, isActive类型: ${typeof query.isActive}`);
        return this.systemConfigService.findAll(query);
    }
    async findByKey(key) {
        return this.systemConfigService.findByKey(key);
    }
    async findOne(id) {
        return this.systemConfigService.findOne(+id);
    }
    async create(createDto) {
        this.logger.log(`创建系统配置: ${JSON.stringify(createDto)}`);
        return this.systemConfigService.create(createDto);
    }
    async updateByKey(key, value) {
        this.logger.log(`更新系统配置键 ${key} 的值: ${value}`);
        return this.systemConfigService.updateByKey(key, value);
    }
    async update(id, updateDto) {
        this.logger.log(`更新系统配置 ${id}: ${JSON.stringify(updateDto)}`);
        return this.systemConfigService.update(+id, updateDto);
    }
    async remove(id) {
        this.logger.log(`删除系统配置 ${id}`);
        return this.systemConfigService.remove(+id);
    }
};
exports.SystemConfigController = SystemConfigController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取系统配置列表' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功获取系统配置列表' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [system_config_dto_1.SystemConfigQueryDto]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('key/:key'),
    (0, swagger_1.ApiOperation)({ summary: '根据键名获取系统配置' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功获取系统配置' }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "findByKey", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定ID的系统配置' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功获取系统配置' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建新系统配置' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: '成功创建系统配置' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [system_config_dto_1.CreateSystemConfigDto]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)('key/:key'),
    (0, swagger_1.ApiOperation)({ summary: '根据键名更新系统配置值' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功更新系统配置' }),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, common_1.Body)('value')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "updateByKey", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新指定ID的系统配置' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功更新系统配置' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, system_config_dto_1.UpdateSystemConfigDto]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除指定ID的系统配置' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功删除系统配置' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "remove", null);
exports.SystemConfigController = SystemConfigController = SystemConfigController_1 = __decorate([
    (0, swagger_1.ApiTags)('系统配置管理'),
    (0, common_1.Controller)('system-config/configs'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [system_config_service_1.SystemConfigService])
], SystemConfigController);
//# sourceMappingURL=system-config.controller.js.map