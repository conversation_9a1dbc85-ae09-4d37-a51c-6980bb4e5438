import React from 'react';
import { Navigate } from 'react-router-dom';

// 图标
import {
  HomeIcon,
  ChartBarIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  ServerIcon,
  ArrowsRightLeftIcon,
  BellAlertIcon,
  DocumentDuplicateIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';

// 布局
import MainLayout from '../layouts/MainLayout';
import AuthLayout from '../layouts/AuthLayout';

// 认证页面
import LoginPage from '../features/auth/pages/LoginPage';
import RegisterPage from '../features/auth/pages/RegisterPage';
import ForgotPasswordPage from '../features/auth/pages/ForgotPasswordPage';

// 仪表盘
import DashboardPage from '../features/dashboard/pages/DashboardPage';

// 数据管理页面
import DeviceListPage from '../features/data_management/pages/DeviceListPage';

// 手动派发页面
import ManualDispatchPage from '../features/manual_dispatch/pages/ManualDispatchPage';

// 系统配置页面
import SystemConfigPage from '../features/system_config/pages/SystemConfigPage';
import LedConcentratorPage from '../features/system_config/pages/LedConcentratorPage';
import LedMappingPage from '../features/system_config/pages/LedMappingPage';

// 事业部管理页面
import DepartmentManagementPage from '../features/department/pages/DepartmentManagementPage';

// 错误页面
import NotFoundPage from '../features/error/pages/NotFoundPage';

// 认证路由
const authRoutes = [
  {
    path: 'login',
    element: <LoginPage />,
  },
  {
    path: 'register',
    element: <RegisterPage />,
  },
  {
    path: 'forgot-password',
    element: <ForgotPasswordPage />,
  },
];

// 仪表盘路由
const dashboardRoutes = [
  {
    path: '/dashboard',
    element: <DashboardPage />,
    title: '仪表盘',
    icon: <HomeIcon className="h-5 w-5" />,
    showInSidebar: true,
  },
];

// 数据管理路由
const dataManagementRoutes = [
  {
    path: '/devices',
    element: <DeviceListPage />,
    title: '设备管理',
    icon: <DocumentDuplicateIcon className="h-5 w-5" />,
    showInSidebar: true,
  },
];

// 手动派发路由
const manualDispatchRoutes = [
  {
    path: '/manual-dispatch',
    element: <ManualDispatchPage />,
    title: '手动数据派发',
    icon: <BellAlertIcon className="h-5 w-5" />,
    showInSidebar: true,
  },
];

// 事业部管理路由
const departmentRoutes = [
  {
    path: '/departments',
    element: <DepartmentManagementPage />,
    title: '部门管理',
    icon: <BuildingOfficeIcon className="h-5 w-5" />,
    showInSidebar: true,
  },
];

// 系统配置路由
const systemConfigRoutes = [
  {
    path: '/system/config',
    element: <SystemConfigPage />,
    title: '系统配置',
    icon: <Cog6ToothIcon className="h-5 w-5" />,
    showInSidebar: true,
  },
  {
    path: '/system/led-concentrators',
    element: <LedConcentratorPage />,
    title: 'LED集中器管理',
    icon: <ServerIcon className="h-5 w-5" />,
    showInSidebar: true,
  },
  {
    path: '/system/led-mappings',
    element: <LedMappingPage />,
    title: 'LED映射配置',
    icon: <ArrowsRightLeftIcon className="h-5 w-5" />,
    showInSidebar: true,
  },
];

// 错误路由
const errorRoutes = [
  {
    path: '*',
    element: <NotFoundPage />,
  },
];

// 路由配置
const routes = [
  {
    path: 'auth',
    element: <AuthLayout />,
    children: authRoutes,
  },
  {
    path: '/',
    element: <MainLayout />,
    children: [
      { path: '/', element: <Navigate to="/dashboard" /> },
      ...dashboardRoutes,
      ...dataManagementRoutes,
      ...manualDispatchRoutes,
      ...departmentRoutes,
      ...systemConfigRoutes,
      ...errorRoutes,
    ],
  },
];

// 导出
export default routes;

// 导出侧边栏菜单项
export const sidebarRoutes = [
  ...dashboardRoutes.filter(route => route.showInSidebar),
  ...dataManagementRoutes.filter(route => route.showInSidebar),
  ...manualDispatchRoutes.filter(route => route.showInSidebar),
  ...departmentRoutes.filter(route => route.showInSidebar),
  ...systemConfigRoutes.filter(route => route.showInSidebar),
];