"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedLedMappings = seedLedMappings;
const concentrator_led_mapping_entity_1 = require("../../system_config/entities/concentrator-led-mapping.entity");
const led_concentrator_entity_1 = require("../../system_config/entities/led-concentrator.entity");
const common_1 = require("@nestjs/common");
async function seedLedMappings(dataSource) {
    const logger = new common_1.Logger('LedMappingSeed');
    try {
        const existingCount = await dataSource
            .getRepository(concentrator_led_mapping_entity_1.ConcentratorLedMapping)
            .count();
        if (existingCount > 0) {
            logger.log(`已存在 ${existingCount} 条LED映射数据，跳过种子数据创建`);
            return;
        }
        const concentrators = await dataSource
            .getRepository(led_concentrator_entity_1.LedConcentrator)
            .find();
        if (!concentrators || concentrators.length === 0) {
            logger.warn('未找到任何集中器，无法创建LED映射');
            return;
        }
        const queryRunner = dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const mappings = [
                {
                    concentratorId: concentrators[0].id,
                    ledId: '161',
                    channelNumber: '0',
                    description: '生产线1 - 工位1',
                    isActive: true,
                },
                {
                    concentratorId: concentrators[0].id,
                    ledId: '162',
                    channelNumber: '1',
                    description: '生产线1 - 工位2',
                    isActive: true,
                },
                {
                    concentratorId: concentrators[0].id,
                    ledId: '163',
                    channelNumber: '2',
                    description: '生产线1 - 工位3',
                    isActive: true,
                },
            ];
            if (concentrators.length > 1) {
                mappings.push({
                    concentratorId: concentrators[1].id,
                    ledId: '164',
                    channelNumber: '0',
                    description: '生产线2 - 工位1',
                    isActive: true,
                }, {
                    concentratorId: concentrators[1].id,
                    ledId: '165',
                    channelNumber: '1',
                    description: '生产线2 - 工位2',
                    isActive: true,
                });
            }
            for (const mapping of mappings) {
                await queryRunner.query(`
          INSERT INTO CONCENTRATOR_LED_MAPPING (
            MAPPING_ID, 
            CONCENTRATOR_ID, 
            LED_ID, 
            CHANNEL_NUMBER, 
            DESCRIPTION, 
            IS_ACTIVE, 
            CREATED_BY, 
            CREATION_DATE
          ) VALUES (
            CONCENTRATOR_LED_MAPPING_SEQ.NEXTVAL, 
            ${mapping.concentratorId}, 
            '${mapping.ledId}', 
            '${mapping.channelNumber}', 
            '${mapping.description || ''}', 
            '${mapping.isActive ? '1' : '0'}', 
            'SYSTEM', 
            SYSDATE
          )
        `);
                logger.log(`成功创建LED设备 ${mapping.ledId} 的映射数据`);
            }
            await queryRunner.commitTransaction();
            logger.log(`成功创建 ${mappings.length} 条LED映射数据`);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            logger.error(`创建LED映射数据失败: ${error.message}`);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    catch (error) {
        logger.error(`创建LED映射种子数据失败: ${error.message}`);
        throw error;
    }
}
//# sourceMappingURL=led-mapping.seed.js.map