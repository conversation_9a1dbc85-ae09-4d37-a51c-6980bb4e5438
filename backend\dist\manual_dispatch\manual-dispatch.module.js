"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ManualDispatchModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const manual_dispatch_controller_1 = require("./manual-dispatch.controller");
const manual_dispatch_service_1 = require("./manual-dispatch.service");
const led_concentrator_entity_1 = require("../system_config/entities/led-concentrator.entity");
const concentrator_led_mapping_entity_1 = require("../system_config/entities/concentrator-led-mapping.entity");
const led_data_entity_1 = require("../led_data/entities/led-data.entity");
const system_config_module_1 = require("../system_config/system-config.module");
let ManualDispatchModule = class ManualDispatchModule {
};
exports.ManualDispatchModule = ManualDispatchModule;
exports.ManualDispatchModule = ManualDispatchModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                led_concentrator_entity_1.LedConcentrator,
                concentrator_led_mapping_entity_1.ConcentratorLedMapping,
                led_data_entity_1.LedData,
            ]),
            system_config_module_1.SystemConfigModule,
        ],
        controllers: [manual_dispatch_controller_1.ManualDispatchController],
        providers: [manual_dispatch_service_1.ManualDispatchService],
        exports: [manual_dispatch_service_1.ManualDispatchService],
    })
], ManualDispatchModule);
//# sourceMappingURL=manual-dispatch.module.js.map