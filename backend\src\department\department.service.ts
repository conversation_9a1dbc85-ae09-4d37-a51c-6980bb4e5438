import { Injectable, NotFoundException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { DataSource, EntityManager } from 'typeorm';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { Department } from './entities/department.entity';

@Injectable()
export class DepartmentService {
  constructor(private dataSource: DataSource) {}

  async create(createDepartmentDto: CreateDepartmentDto): Promise<Department> {
    return this.dataSource.transaction(async (manager) => {
      try {
        // 1. 检查编码是否已存在
        const existingDept = await manager.query(
          `SELECT DEPT_ID FROM LED_DEPARTMENT WHERE DEPT_CODE = :1`,
          [createDepartmentDto.DEPT_CODE],
        );

        if (existingDept.length > 0) {
          throw new BadRequestException(`事业部编码 ${createDepartmentDto.DEPT_CODE} 已存在`);
        }

        // 2. 获取父级信息并计算层级和初始路径
        let deptLevel = 1;
        let parentPath = '';
        if (createDepartmentDto.PARENT_DEPT_ID) {
          const parentDeptResults = await manager.query(
            `SELECT DEPT_PATH, DEPT_LEVEL FROM LED_DEPARTMENT WHERE DEPT_ID = :1`,
            [createDepartmentDto.PARENT_DEPT_ID],
          );
          if (parentDeptResults.length === 0) {
            throw new BadRequestException(`父级事业部ID ${createDepartmentDto.PARENT_DEPT_ID} 不存在`);
          }
          const parentDept = parentDeptResults[0];
          deptLevel = (parentDept.DEPT_LEVEL || 1) + 1;
          parentPath = parentDept.DEPT_PATH;
        }

        // 3. 插入新事业部并获取返回的ID
        const insertQuery = `
          INSERT INTO LED_DEPARTMENT(
            DEPT_CODE, DEPT_NAME, PARENT_DEPT_ID, DEPT_LEVEL, DEPT_PATH,
            MANAGER_USER_ID, DESCRIPTION, SORT_ORDER, IS_ACTIVE, CREATED_BY, CREATION_DATE
          ) VALUES(
            :1, :2, :3, :4, :5, :6, :7, :8, 1, 'system', SYSDATE
          ) RETURNING DEPT_ID INTO :9
        `;
        
        const outParam = { dir: (this.dataSource.driver as any).oracle.BIND_OUT, type: (this.dataSource.driver as any).oracle.NUMBER };
        
        const insertResult = await manager.query(insertQuery, [
          createDepartmentDto.DEPT_CODE,
          createDepartmentDto.DEPT_NAME,
          createDepartmentDto.PARENT_DEPT_ID || null,
          deptLevel,
          parentPath, // 初始路径, 后面会更新
          createDepartmentDto.MANAGER_USER_ID || null,
          createDepartmentDto.DESCRIPTION || null,
          createDepartmentDto.SORT_ORDER || 0,
          outParam,
        ]);
        
        const newId = insertResult[0][0];

        // 4. 更新新部门的路径以包含自己的ID
        const finalPath = parentPath ? `${parentPath}/${newId}` : `/${newId}`;
        await manager.query(
          `UPDATE LED_DEPARTMENT SET DEPT_PATH = :1 WHERE DEPT_ID = :2`,
          [finalPath, newId],
        );

        // 5. 查询并返回最终创建的事业部完整信息
        return this.findOne(newId, manager);

      } catch (error) {
        if (error instanceof BadRequestException || error instanceof NotFoundException) {
          throw error;
        }
        console.error('创建事业部失败:', error);
        throw new InternalServerErrorException('创建事业部失败，操作已回滚');
      }
    });
  }

  async findAll(params: any = {}): Promise<{ items: Department[]; meta: any }> {
    try {
      console.log('🔍 部门服务 - 开始查询部门列表');
      console.log('📋 原始参数:', params);

      const page = params.page ? parseInt(params.page, 10) : 1;
      const limit = params.limit ? parseInt(params.limit, 10) : 10;
      const offset = (page - 1) * limit;
      const { search, parentId, deptLevel, isActive } = params;

      console.log('📊 解析后的参数:', { page, limit, offset, search, parentId, deptLevel, isActive });

      let whereClause = 'WHERE 1=1';
      const countQueryParams = [];

      if (search) {
        whereClause += ` AND (UPPER(d.DEPT_CODE) LIKE ? OR UPPER(d.DEPT_NAME) LIKE ?)`;
        countQueryParams.push(`%${search.toUpperCase()}%`);
        countQueryParams.push(`%${search.toUpperCase()}%`);
      }

      if (parentId !== undefined) {
        if (parentId === 'null' || parentId === null) {
          whereClause += ` AND d.PARENT_DEPT_ID IS NULL`;
        } else {
          whereClause += ` AND d.PARENT_DEPT_ID = ?`;
          countQueryParams.push(parseInt(parentId, 10));
        }
      }

      if (deptLevel) {
        whereClause += ` AND d.DEPT_LEVEL = ?`;
        countQueryParams.push(parseInt(deptLevel, 10));
      }

      if (isActive !== undefined && isActive !== '') {
        whereClause += ` AND d.IS_ACTIVE = ?`;
        countQueryParams.push(parseInt(isActive, 10));
      }

      const countQuery = `SELECT COUNT(*) AS total FROM LED_DEPARTMENT d ${whereClause}`;
      console.log('🔢 计数查询SQL:', countQuery);
      console.log('🔢 计数查询参数:', countQueryParams);

      const countResult = await this.dataSource.query(countQuery, countQueryParams);
      console.log('🔢 计数查询结果:', countResult);

      const totalItems = countResult[0] ? parseInt(countResult[0].TOTAL, 10) : 0;
      console.log('📊 总记录数:', totalItems);

      const dataQueryParams = [...countQueryParams];
      const dataQuery = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT d.DEPT_ID, d.DEPT_CODE, d.DEPT_NAME, d.PARENT_DEPT_ID,
                   d.DEPT_LEVEL, d.DEPT_PATH, d.MANAGER_USER_ID,
                   d.DESCRIPTION, d.SORT_ORDER, d.IS_ACTIVE,
                   p.DEPT_NAME as PARENT_DEPT_NAME,
                   u.USERNAME as MANAGER_USERNAME
            FROM LED_DEPARTMENT d
            LEFT JOIN LED_DEPARTMENT p ON d.PARENT_DEPT_ID = p.DEPT_ID
            LEFT JOIN LED_USERS u ON d.MANAGER_USER_ID = u.ID
            ${whereClause}
            ORDER BY d.DEPT_LEVEL, d.DEPT_PATH, d.SORT_ORDER, d.DEPT_CODE
          ) a WHERE ROWNUM <= ?
        ) WHERE rnum > ?
      `;
      dataQueryParams.push(offset + limit);
      dataQueryParams.push(offset);

      console.log('📋 数据查询SQL:', dataQuery);
      console.log('📋 数据查询参数:', dataQueryParams);

      const departments = await this.dataSource.query(dataQuery, dataQueryParams);
      console.log('📋 查询到的部门数据:', departments.length, '条记录');
      if (departments.length > 0) {
        console.log('📋 第一条数据示例:', departments[0]);
      }

      return {
        items: departments,
        meta: {
          totalItems,
          itemsPerPage: limit,
          currentPage: page,
          totalPages: Math.ceil(totalItems / limit),
        },
      };
    } catch (error) {
      console.error('获取事业部列表失败:', error);
      throw new InternalServerErrorException('获取事业部列表失败');
    }
  }

  async findOne(id: number, manager?: EntityManager): Promise<Department> {
    const queryRunner = manager || this.dataSource;
    try {
      const departments = await queryRunner.query(
        `SELECT d.*, 
                p.DEPT_NAME as PARENT_DEPT_NAME,
                u.USERNAME as MANAGER_USERNAME
         FROM LED_DEPARTMENT d
         LEFT JOIN LED_DEPARTMENT p ON d.PARENT_DEPT_ID = p.DEPT_ID
         LEFT JOIN LED_USERS u ON d.MANAGER_USER_ID = u.ID
         WHERE d.DEPT_ID = :1`,
        [id]
      );

      if (!departments || departments.length === 0) {
        throw new NotFoundException(`ID为${id}的事业部不存在`);
      }

      return departments[0];
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(`获取ID为${id}的事业部失败:`, error);
      throw new InternalServerErrorException(`获取ID为${id}的事业部失败`);
    }
  }

  async update(id: number, updateDepartmentDto: UpdateDepartmentDto): Promise<Department> {
    return this.dataSource.transaction(async (manager) => {
      try {
        const currentDept = await this.findOne(id, manager);

        if (updateDepartmentDto.DEPT_CODE && updateDepartmentDto.DEPT_CODE !== currentDept.DEPT_CODE) {
          const existingDept = await manager.query(
            `SELECT DEPT_ID FROM LED_DEPARTMENT WHERE DEPT_CODE = :1 AND DEPT_ID != :2`,
            [updateDepartmentDto.DEPT_CODE, id],
          );
          if (existingDept.length > 0) {
            throw new BadRequestException(`事业部编码 ${updateDepartmentDto.DEPT_CODE} 已被其他事业部使用`);
          }
        }

        const needsPathUpdate =
          updateDepartmentDto.PARENT_DEPT_ID !== undefined &&
          updateDepartmentDto.PARENT_DEPT_ID !== currentDept.PARENT_DEPT_ID;

        if (needsPathUpdate) {
          const newParentId = updateDepartmentDto.PARENT_DEPT_ID;
          if (newParentId) {
            if (newParentId === id) {
              throw new BadRequestException('不能将自己设置为父级事业部');
            }
            const newParentDept = await this.findOne(newParentId, manager);
            if (newParentDept.DEPT_PATH && newParentDept.DEPT_PATH.split('/').includes(id.toString())) {
              throw new BadRequestException('不能将子事业部设置为父级事业部');
            }
          }
          await this.updateChildren(id, newParentId, manager);
        }

        const updateQueryParts = [];
        const queryParams = [];
        let paramIndex = 1;

        const fieldsToUpdate: any = { ...updateDepartmentDto };
        const allowedFields = ['DEPT_CODE', 'DEPT_NAME', 'MANAGER_USER_ID', 'DESCRIPTION', 'SORT_ORDER', 'IS_ACTIVE'];

        for (const key of allowedFields) {
          if (fieldsToUpdate[key] !== undefined) {
            updateQueryParts.push(`${key} = :${paramIndex}`);
            queryParams.push(fieldsToUpdate[key]);
            paramIndex++;
          }
        }

        if (needsPathUpdate) {
          updateQueryParts.push(`PARENT_DEPT_ID = :${paramIndex}`);
          queryParams.push(updateDepartmentDto.PARENT_DEPT_ID);
          paramIndex++;
        }

        if (updateQueryParts.length > 0) {
          updateQueryParts.push(`LAST_UPDATED_BY = 'system_update'`);
          updateQueryParts.push(`LAST_UPDATE_DATE = SYSDATE`);

          const updateQuery = `UPDATE LED_DEPARTMENT SET ${updateQueryParts.join(', ')} WHERE DEPT_ID = :${paramIndex}`;
          queryParams.push(id);

          await manager.query(updateQuery, queryParams);
        }

        return this.findOne(id, manager);
      } catch (error) {
        if (error instanceof BadRequestException || error instanceof NotFoundException) {
          throw error;
        }
        console.error(`更新ID为${id}的事业部失败:`, error);
        throw new InternalServerErrorException(`更新ID为${id}的事业部失败，操作已回滚`);
      }
    });
  }

  /**
   * 递归更新子事业部的路径和层级
   */
  private async updateChildren(parentId: number, newParentDeptId: number | null, manager: EntityManager): Promise<void> {
    // 1. 获取新的父部门信息
    let newParentPath = '';
    let newParentLevel = 0;
    if (newParentDeptId) {
      const newParentDept = await this.findOne(newParentDeptId, manager);
      newParentPath = newParentDept.DEPT_PATH;
      newParentLevel = newParentDept.DEPT_LEVEL;
    }

    // 2. 更新当前部门自己的路径和层级
    const newPath = newParentPath ? `${newParentPath}/${parentId}` : `/${parentId}`;
    const newLevel = newParentLevel + 1;
    await manager.query(
        `UPDATE LED_DEPARTMENT SET DEPT_PATH = :1, DEPT_LEVEL = :2 WHERE DEPT_ID = :3`,
        [newPath, newLevel, parentId],
    );

    // 3. 查找所有直接子部门
    const children = await manager.query(
        `SELECT DEPT_ID FROM LED_DEPARTMENT WHERE PARENT_DEPT_ID = :1`,
        [parentId],
    );

    // 4. 对每个子部门，递归调用此方法
    for (const child of children) {
      await this.updateChildren(child.DEPT_ID, parentId, manager);
    }
  }

  async remove(id: number): Promise<{ success: boolean; message: string }> {
    return this.dataSource.transaction(async (manager) => {
      // 1. 检查是否存在子事业部
      const children = await manager.query(
        `SELECT DEPT_ID FROM LED_DEPARTMENT WHERE PARENT_DEPT_ID = :1`,
        [id]
      );
      if (children.length > 0) {
        throw new BadRequestException('该事业部下存在子事业部，无法删除');
      }

      // 2. 检查是否关联了用户
      const users = await manager.query(
        `SELECT ID FROM LED_USERS WHERE DEPT_ID = :1`,
        [id]
      );
      if (users.length > 0) {
        throw new BadRequestException('该事业部下存在用户，无法删除');
      }
      
      // 3. 检查是否关联了设备
      const devices = await manager.query(
        `SELECT LED_ID FROM LED_DEVICE_INFO WHERE DEPT_ID = :1`,
        [id]
      );
      if (devices.length > 0) {
        throw new BadRequestException('该事业部下存在设备，无法删除');
      }

      // 4. 执行删除操作
      const result = await manager.query(
        `DELETE FROM LED_DEPARTMENT WHERE DEPT_ID = :1`,
        [id]
      );

      if (result.affected === 0) {
        throw new NotFoundException(`ID为${id}的事业部不存在`);
      }

      return { success: true, message: '事业部删除成功' };
    });
  }

  /**
   * 获取事业部树形结构
   */
  async getTree(): Promise<Department[]> {
    try {
      const allDepartments = await this.dataSource.query(`
        SELECT d.DEPT_ID, d.DEPT_NAME, d.PARENT_DEPT_ID, d.SORT_ORDER, d.DEPT_CODE,
               d.DEPT_LEVEL, d.DESCRIPTION, d.IS_ACTIVE, d.MANAGER_USER_ID,
               u.USERNAME as MANAGER_USERNAME
        FROM LED_DEPARTMENT d
        LEFT JOIN LED_USERS u ON d.MANAGER_USER_ID = u.ID
        ORDER BY d.SORT_ORDER, d.DEPT_CODE
      `);

      const map = new Map<number, any>();
      const tree = [];

      allDepartments.forEach(dept => {
        map.set(dept.DEPT_ID, { ...dept, children: [] });
      });

      allDepartments.forEach(dept => {
        if (dept.PARENT_DEPT_ID && map.has(dept.PARENT_DEPT_ID)) {
          map.get(dept.PARENT_DEPT_ID).children.push(map.get(dept.DEPT_ID));
        } else {
          tree.push(map.get(dept.DEPT_ID));
        }
      });
      
      // 对顶层节点也进行排序
      tree.sort((a, b) => (a.SORT_ORDER || 0) - (b.SORT_ORDER || 0) || a.DEPT_CODE.localeCompare(b.DEPT_CODE));


      return tree;
    } catch (error) {
      console.error('获取事业部树形结构失败:', error);
      throw new InternalServerErrorException('获取事业部树形结构失败');
    }
  }
}
