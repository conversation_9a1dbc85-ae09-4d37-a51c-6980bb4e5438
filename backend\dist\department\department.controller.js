"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DepartmentController = void 0;
const common_1 = require("@nestjs/common");
const department_service_1 = require("./department.service");
const create_department_dto_1 = require("./dto/create-department.dto");
const update_department_dto_1 = require("./dto/update-department.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let DepartmentController = class DepartmentController {
    constructor(departmentService) {
        this.departmentService = departmentService;
    }
    async create(createDepartmentDto) {
        try {
            const department = await this.departmentService.create(createDepartmentDto);
            return {
                success: true,
                message: '事业部创建成功',
                data: department
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '事业部创建失败',
                data: null
            };
        }
    }
    async findAll(query) {
        try {
            const result = await this.departmentService.findAll(query);
            return {
                success: true,
                message: '获取事业部列表成功',
                data: result.items,
                meta: result.meta
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取事业部列表失败',
                data: [],
                meta: {
                    totalItems: 0,
                    itemsPerPage: 10,
                    currentPage: 1,
                    totalPages: 0
                }
            };
        }
    }
    async getTree(req) {
        try {
            const tree = await this.departmentService.getTree();
            return {
                success: true,
                message: '获取事业部树形结构成功',
                data: tree
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取事业部树形结构失败',
                data: []
            };
        }
    }
    async findOne(id) {
        try {
            const department = await this.departmentService.findOne(id);
            return {
                success: true,
                message: '获取事业部详情成功',
                data: department
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取事业部详情失败',
                data: null
            };
        }
    }
    async update(id, updateDepartmentDto) {
        try {
            const department = await this.departmentService.update(id, updateDepartmentDto);
            return {
                success: true,
                message: '事业部更新成功',
                data: department
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '事业部更新失败',
                data: null
            };
        }
    }
    async remove(id) {
        try {
            await this.departmentService.remove(id);
            return {
                success: true,
                message: '事业部删除成功',
                data: null
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '事业部删除失败',
                data: null
            };
        }
    }
};
exports.DepartmentController = DepartmentController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_department_dto_1.CreateDepartmentDto]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('tree'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "getTree", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_department_dto_1.UpdateDepartmentDto]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "remove", null);
exports.DepartmentController = DepartmentController = __decorate([
    (0, common_1.Controller)('departments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [department_service_1.DepartmentService])
], DepartmentController);
//# sourceMappingURL=department.controller.js.map