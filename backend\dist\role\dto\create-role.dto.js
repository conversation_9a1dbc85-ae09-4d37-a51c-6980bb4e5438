"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateRoleDto = void 0;
const class_validator_1 = require("class-validator");
class CreateRoleDto {
}
exports.CreateRoleDto = CreateRoleDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2, { message: '角色编码至少需要2个字符' }),
    (0, class_validator_1.MaxLength)(50, { message: '角色编码不能超过50个字符' }),
    __metadata("design:type", String)
], CreateRoleDto.prototype, "ROLE_CODE", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2, { message: '角色名称至少需要2个字符' }),
    (0, class_validator_1.MaxLength)(100, { message: '角色名称不能超过100个字符' }),
    __metadata("design:type", String)
], CreateRoleDto.prototype, "ROLE_NAME", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['SYSTEM', 'DEPT', 'CUSTOM'], { message: '角色类型必须是SYSTEM、DEPT或CUSTOM之一' }),
    __metadata("design:type", String)
], CreateRoleDto.prototype, "ROLE_TYPE", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '事业部ID必须是数字' }),
    __metadata("design:type", Number)
], CreateRoleDto.prototype, "DEPT_ID", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500, { message: '描述不能超过500个字符' }),
    __metadata("design:type", String)
], CreateRoleDto.prototype, "DESCRIPTION", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNumber)({}, { each: true, message: '菜单ID必须是数字' }),
    __metadata("design:type", Array)
], CreateRoleDto.prototype, "menuIds", void 0);
//# sourceMappingURL=create-role.dto.js.map