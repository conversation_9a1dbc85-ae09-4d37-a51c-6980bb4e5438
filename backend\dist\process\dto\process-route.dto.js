"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessRouteResponseDto = exports.UpdateProcessRouteDto = exports.CreateProcessRouteDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const route_operation_dto_1 = require("./route-operation.dto");
class CreateProcessRouteDto {
}
exports.CreateProcessRouteDto = CreateProcessRouteDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线编码' }),
    (0, class_validator_1.IsNotEmpty)({ message: '工艺路线编码不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProcessRouteDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线名称' }),
    (0, class_validator_1.IsNotEmpty)({ message: '工艺路线名称不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProcessRouteDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProcessRouteDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活', default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateProcessRouteDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序步骤列表', type: [route_operation_dto_1.RouteOperationDto], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CreateProcessRouteDto.prototype, "operations", void 0);
class UpdateProcessRouteDto {
}
exports.UpdateProcessRouteDto = UpdateProcessRouteDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线编码', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateProcessRouteDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线名称', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateProcessRouteDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateProcessRouteDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateProcessRouteDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序步骤列表', type: [route_operation_dto_1.RouteOperationDto], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], UpdateProcessRouteDto.prototype, "operations", void 0);
class ProcessRouteResponseDto {
}
exports.ProcessRouteResponseDto = ProcessRouteResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线ID' }),
    __metadata("design:type", Number)
], ProcessRouteResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线编码' }),
    __metadata("design:type", String)
], ProcessRouteResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线名称' }),
    __metadata("design:type", String)
], ProcessRouteResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线描述' }),
    __metadata("design:type", String)
], ProcessRouteResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活' }),
    __metadata("design:type", Number)
], ProcessRouteResponseDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人' }),
    __metadata("design:type", String)
], ProcessRouteResponseDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建日期' }),
    __metadata("design:type", Date)
], ProcessRouteResponseDto.prototype, "creationDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新人' }),
    __metadata("design:type", String)
], ProcessRouteResponseDto.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新日期' }),
    __metadata("design:type", Date)
], ProcessRouteResponseDto.prototype, "lastUpdateDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序步骤列表', type: [route_operation_dto_1.RouteOperationDto], required: false }),
    __metadata("design:type", Array)
], ProcessRouteResponseDto.prototype, "operations", void 0);
//# sourceMappingURL=process-route.dto.js.map