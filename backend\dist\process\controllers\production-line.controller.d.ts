import { ProductionLineService } from '../services/production-line.service';
import { CreateProductionLineDto, UpdateProductionLineDto } from '../dto/production-line.dto';
export declare class ProductionLineController {
    private readonly productionLineService;
    constructor(productionLineService: ProductionLineService);
    findAll(query: any): Promise<{
        items: import("../entities/production-line.entity").ProductionLine[];
        meta: any;
    }>;
    findOne(id: string): Promise<import("../entities/production-line.entity").ProductionLine>;
    create(createDto: CreateProductionLineDto, req: any): Promise<import("../entities/production-line.entity").ProductionLine>;
    update(id: string, updateDto: UpdateProductionLineDto, req: any): Promise<import("../entities/production-line.entity").ProductionLine>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
