import { SystemConfigService } from '../services/system-config.service';
import { CreateSystemConfigDto, UpdateSystemConfigDto, SystemConfigQueryDto } from '../dto/system-config.dto';
export declare class SystemConfigController {
    private readonly systemConfigService;
    private readonly logger;
    constructor(systemConfigService: SystemConfigService);
    findAll(query: SystemConfigQueryDto): Promise<{
        items: import("../entities/system-config.entity").SystemConfig[];
        meta: {
            total: number;
            currentPage?: number;
            totalPages?: number;
            itemsPerPage?: number;
        };
    }>;
    findByKey(key: string): Promise<import("../entities/system-config.entity").SystemConfig>;
    findOne(id: string): Promise<import("../entities/system-config.entity").SystemConfig>;
    create(createDto: CreateSystemConfigDto): Promise<any>;
    updateBy<PERSON><PERSON>(key: string, value: string): Promise<any>;
    update(id: string, updateDto: UpdateSystemConfigDto): Promise<any>;
    remove(id: string): Promise<any>;
}
