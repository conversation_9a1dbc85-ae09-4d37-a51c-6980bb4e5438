"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ManualDispatchService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ManualDispatchService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const led_concentrator_entity_1 = require("../system_config/entities/led-concentrator.entity");
const concentrator_led_mapping_entity_1 = require("../system_config/entities/concentrator-led-mapping.entity");
const led_data_entity_1 = require("../led_data/entities/led-data.entity");
const modbus_client_service_1 = require("../system_config/services/modbus-client.service");
const led_mapping_service_1 = require("../system_config/services/led-mapping.service");
let ManualDispatchService = ManualDispatchService_1 = class ManualDispatchService {
    constructor(ledConcentratorRepository, concentratorLedMappingRepository, ledDataRepository, dataSource, modbusClientService, ledMappingService) {
        this.ledConcentratorRepository = ledConcentratorRepository;
        this.concentratorLedMappingRepository = concentratorLedMappingRepository;
        this.ledDataRepository = ledDataRepository;
        this.dataSource = dataSource;
        this.modbusClientService = modbusClientService;
        this.ledMappingService = ledMappingService;
        this.logger = new common_1.Logger(ManualDispatchService_1.name);
    }
    async getAllActiveConcentrators() {
        try {
            const query = `
        SELECT c.*
        FROM LED_CONCENTRATOR c
        WHERE c.IS_ACTIVE = '1'
      `;
            const concentrators = await this.dataSource.query(query);
            const result = [];
            for (const concentrator of concentrators) {
                const mappingsQuery = `
          SELECT m.*
          FROM CONCENTRATOR_LED_MAPPING m
          WHERE m.CONCENTRATOR_ID = :concentratorId AND m.IS_ACTIVE = '1'
        `;
                const mappings = await this.dataSource.query(mappingsQuery, [concentrator.CONCENTRATOR_ID]);
                const formattedConcentrator = {
                    id: concentrator.CONCENTRATOR_ID,
                    code: concentrator.CONCENTRATOR_CODE,
                    name: concentrator.CONCENTRATOR_NAME,
                    ipAddress: concentrator.IP_ADDRESS,
                    portNumber: concentrator.PORT_NUMBER,
                    protocolType: concentrator.PROTOCOL_TYPE,
                    timeoutSeconds: concentrator.TIMEOUT_SECONDS || 10,
                    maxRetryCount: concentrator.MAX_RETRY_COUNT || 3,
                    ledMappings: mappings.map(m => ({
                        id: m.MAPPING_ID,
                        concentratorId: m.CONCENTRATOR_ID,
                        ledId: m.LED_ID,
                        channelNumber: m.CHANNEL_NUMBER,
                        description: m.DESCRIPTION,
                        isActive: m.IS_ACTIVE === '1'
                    }))
                };
                result.push(formattedConcentrator);
            }
            return result;
        }
        catch (error) {
            this.logger.error(`获取活跃集中器失败: ${error.message}`);
            throw new Error(`获取活跃集中器失败: ${error.message}`);
        }
    }
    async getLedMapping(ledId) {
        try {
            try {
                return await this.ledMappingService.findByLedId(ledId);
            }
            catch (error) {
                if (error instanceof common_1.NotFoundException) {
                    this.logger.error(`LED设备 ${ledId} 没有配置映射关系`);
                    throw new common_1.NotFoundException(`LED设备 "${ledId}" 没有配置映射关系。请先在"LED映射管理"页面为该设备配置集中器映射。`);
                }
                throw error;
            }
        }
        catch (error) {
            this.logger.error(`获取LED设备 ${ledId} 的映射信息失败: ${error.message}`);
            throw error;
        }
    }
    async sendDataToLed(ledId, planValue, workType = '1') {
        try {
            this.logger.log(`sendDataToLed 参数检查:`);
            this.logger.log(`  ledId: ${ledId} (类型: ${typeof ledId})`);
            this.logger.log(`  planValue: ${planValue} (类型: ${typeof planValue})`);
            this.logger.log(`  workType: ${workType} (类型: ${typeof workType})`);
            if (planValue === undefined || planValue === null || isNaN(planValue)) {
                throw new Error(`planValue 参数无效: ${planValue}`);
            }
            const mapping = await this.getLedMapping(ledId);
            const concentrator = mapping.concentrator;
            if (!concentrator) {
                throw new common_1.NotFoundException(`未找到LED设备 ${ledId} 对应的集中器`);
            }
            const ledIdNumber = parseInt(ledId);
            if (isNaN(ledIdNumber) || ledIdNumber < 1) {
                throw new Error(`LED设备ID无效: ${ledId}，LED_ID必须是从1开始的数字`);
            }
            const baseAddress = (ledIdNumber - 1) * 4;
            this.logger.log(`LED设备 ${ledId} (数字ID: ${ledIdNumber}), 寄存器基地址: ${baseAddress}`);
            const planValueSplit = this.modbusClientService.splitTo16Bit(planValue);
            const connected = await this.modbusClientService.connect(concentrator.ipAddress, concentrator.portNumber, concentrator.timeoutSeconds * 1000);
            if (!connected) {
                throw new Error(`无法连接到集中器 ${concentrator.name} (${concentrator.ipAddress}:${concentrator.portNumber})`);
            }
            try {
                const writeSuccess = await this.modbusClientService.writeRegisters(baseAddress, planValueSplit);
                if (!writeSuccess) {
                    throw new Error(`向LED设备 ${ledId} 写入寄存器失败`);
                }
                this.logger.log(`成功向LED设备 ${ledId} 发送计划数量: ${planValue}, 班次: ${workType === '1' ? '白班' : '夜班'}`);
                const savedPlanRecord = await this.savePlanToDatabase(ledId, planValue, workType);
                return {
                    success: true,
                    message: `成功向LED设备 ${ledId} 发送计划数量`,
                    details: {
                        ledId,
                        concentratorId: concentrator.id,
                        concentratorName: concentrator.name,
                        channelNumber: mapping.channelNumber,
                        planValue,
                        workType,
                        workTypeText: workType === '1' ? '白班' : '夜班',
                        timestamp: new Date(),
                        planRecordId: savedPlanRecord.id
                    }
                };
            }
            finally {
                await this.modbusClientService.close();
            }
        }
        catch (error) {
            this.logger.error(`向LED设备 ${ledId} 发送数据失败: ${error.message}`);
            throw error;
        }
    }
    async readDataFromLed(ledId) {
        try {
            const mapping = await this.getLedMapping(ledId);
            const concentrator = mapping.concentrator;
            if (!concentrator) {
                throw new common_1.NotFoundException(`未找到LED设备 ${ledId} 对应的集中器`);
            }
            const ledIdNumber = parseInt(ledId);
            if (isNaN(ledIdNumber) || ledIdNumber < 1) {
                throw new Error(`LED设备ID无效: ${ledId}，LED_ID必须是从1开始的数字`);
            }
            const baseAddress = (ledIdNumber - 1) * 4;
            this.logger.log(`读取LED设备 ${ledId} (数字ID: ${ledIdNumber}), 寄存器基地址: ${baseAddress}`);
            const connected = await this.modbusClientService.connect(concentrator.ipAddress, concentrator.portNumber, concentrator.timeoutSeconds * 1000);
            if (!connected) {
                throw new Error(`无法连接到集中器 ${concentrator.name} (${concentrator.ipAddress}:${concentrator.portNumber})`);
            }
            try {
                const registers = await this.modbusClientService.readHoldingRegisters(baseAddress, 4);
                const planValue = this.modbusClientService.mergeTo32Bit(registers[0], registers[1]);
                const actualValue = this.modbusClientService.mergeTo32Bit(registers[2], registers[3]);
                return {
                    success: true,
                    message: `成功读取LED设备 ${ledId} 的数据`,
                    data: {
                        ledId,
                        planValue,
                        actualValue,
                        timestamp: new Date()
                    }
                };
            }
            finally {
                await this.modbusClientService.close();
            }
        }
        catch (error) {
            this.logger.error(`读取LED设备 ${ledId} 的数据失败: ${error.message}`);
            throw error;
        }
    }
    async sendBatchDataToLeds(batchData) {
        const results = [];
        const errors = [];
        this.logger.log(`🔧 开始批量发送数据到 ${batchData.length} 个LED设备（真实Modbus设备模式）`);
        if (batchData.length === 0) {
            return { total: 0, success: 0, failed: 0, results: [], errors: [] };
        }
        let concentratorConnection = null;
        let isConnected = false;
        try {
            const firstMapping = await this.getLedMapping(batchData[0].ledId);
            if (!firstMapping) {
                throw new Error(`LED设备 ${batchData[0].ledId} 的映射信息不存在`);
            }
            concentratorConnection = {
                ip: firstMapping.concentrator.ipAddress,
                port: firstMapping.concentrator.portNumber,
                timeout: firstMapping.concentrator.timeoutSeconds * 1000
            };
            this.logger.log(`🔧 批量发送使用集中器: ${concentratorConnection.ip}:${concentratorConnection.port}`);
            isConnected = await this.modbusClientService.connect(concentratorConnection.ip, concentratorConnection.port, concentratorConnection.timeout);
            if (!isConnected) {
                throw new Error(`无法连接到集中器 ${concentratorConnection.ip}:${concentratorConnection.port}`);
            }
            this.logger.log(`🔧 成功连接到集中器，开始批量发送`);
            for (let i = 0; i < batchData.length; i++) {
                const data = batchData[i];
                const workType = data.workType || '1';
                this.logger.log(`🔧 [${i + 1}/${batchData.length}] 发送到LED设备 ${data.ledId}: 计划=${data.planValue}, 班次=${workType}`);
                try {
                    const result = await this.sendDataToLedDirectWrite(data.ledId, data.planValue, workType);
                    results.push({
                        ledId: data.ledId,
                        success: true,
                        result
                    });
                    this.logger.log(`🔧 [${i + 1}/${batchData.length}] 成功发送到LED设备 ${data.ledId}`);
                    if (i < batchData.length - 1) {
                        this.logger.log(`🔧 等待2秒后发送下一个设备...`);
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                }
                catch (error) {
                    this.logger.error(`🔧 [${i + 1}/${batchData.length}] 发送到LED设备 ${data.ledId} 失败: ${error.message}`);
                    errors.push({
                        ledId: data.ledId,
                        success: false,
                        error: error.message
                    });
                    if (i < batchData.length - 1) {
                        this.logger.log(`🔧 等待2秒后发送下一个设备...`);
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                }
            }
            if (isConnected && results.length > 0) {
                this.logger.log(`🔧 批量发送完成，检查所有相关寄存器状态`);
                try {
                    const ledIds = batchData.map(d => parseInt(d.ledId)).filter(id => !isNaN(id));
                    const minLedId = Math.min(...ledIds);
                    const maxLedId = Math.max(...ledIds);
                    const startAddress = (minLedId - 1) * 4;
                    const endAddress = maxLedId * 4 - 1;
                    const length = endAddress - startAddress + 1;
                    this.logger.log(`🔧 检查寄存器范围: ${startAddress}-${endAddress} (共${length}个寄存器)`);
                    const allRegisters = await this.modbusClientService.readHoldingRegisters(startAddress, length);
                    this.logger.log(`🔧 批量发送后寄存器状态:`);
                    for (let i = 0; i < allRegisters.length; i++) {
                        const regAddress = startAddress + i;
                        const value = allRegisters[i];
                        if (value !== 0) {
                            this.logger.log(`🔧   寄存器${regAddress}: ${value} ✅`);
                        }
                        else {
                            this.logger.log(`🔧   寄存器${regAddress}: ${value} ⚠️`);
                        }
                    }
                    this.logger.log(`🔧 按LED设备分组显示:`);
                    for (const data of batchData) {
                        const ledIdNumber = parseInt(data.ledId);
                        if (!isNaN(ledIdNumber)) {
                            const baseAddr = (ledIdNumber - 1) * 4;
                            const relativeAddr = baseAddr - startAddress;
                            if (relativeAddr >= 0 && relativeAddr + 1 < allRegisters.length) {
                                const planHigh = allRegisters[relativeAddr];
                                const planLow = allRegisters[relativeAddr + 1];
                                const actualHigh = allRegisters[relativeAddr + 2] || 0;
                                const actualLow = allRegisters[relativeAddr + 3] || 0;
                                const planValue = planHigh * 65536 + planLow;
                                const actualValue = actualHigh * 65536 + actualLow;
                                this.logger.log(`🔧   LED${data.ledId}: 计划=${planValue} (期望${data.planValue}), 实际=${actualValue} ${planValue === data.planValue ? '✅' : '❌'}`);
                            }
                        }
                    }
                }
                catch (error) {
                    this.logger.error(`🔧 检查寄存器状态失败: ${error.message}`);
                }
            }
        }
        finally {
            if (isConnected) {
                try {
                    await this.modbusClientService.close();
                    this.logger.log(`🔧 批量发送完成，已关闭集中器连接`);
                }
                catch (error) {
                    this.logger.error(`🔧 关闭集中器连接失败: ${error.message}`);
                }
            }
        }
        const summary = {
            total: batchData.length,
            success: results.length,
            failed: errors.length,
            results,
            errors
        };
        this.logger.log(`🔧 批量发送完成: 总计${summary.total}个, 成功${summary.success}个, 失败${summary.failed}个`);
        return summary;
    }
    async sendDataToLedDirectWrite(ledId, planValue, workType = '1') {
        try {
            this.logger.log(`🔧 sendDataToLedDirectWrite 参数检查:`);
            this.logger.log(`🔧   ledId: ${ledId} (类型: ${typeof ledId})`);
            this.logger.log(`🔧   planValue: ${planValue} (类型: ${typeof planValue})`);
            this.logger.log(`🔧   workType: ${workType} (类型: ${typeof workType})`);
            if (planValue === undefined || planValue === null || isNaN(planValue)) {
                throw new Error(`planValue 参数无效: ${planValue}`);
            }
            const ledIdNumber = parseInt(ledId);
            if (isNaN(ledIdNumber) || ledIdNumber < 1) {
                throw new Error(`LED设备ID无效: ${ledId}，LED_ID必须是从1开始的数字`);
            }
            const baseAddress = (ledIdNumber - 1) * 4;
            this.logger.log(`🔧 LED设备 ${ledId} (数字ID: ${ledIdNumber}), 寄存器基地址: ${baseAddress}`);
            const planValueSplit = this.modbusClientService.splitTo16Bit(planValue);
            this.logger.log(`🔧 计划数量 ${planValue} 拆分为: [${planValueSplit[0]}, ${planValueSplit[1]}]`);
            const writeSuccess = await this.modbusClientService.writeRegisters(baseAddress, planValueSplit);
            if (!writeSuccess) {
                throw new Error(`向LED设备 ${ledId} 写入寄存器失败`);
            }
            this.logger.log(`🔧 成功向LED设备 ${ledId} 写入寄存器 ${baseAddress}-${baseAddress + 1}: [${planValueSplit[0]}, ${planValueSplit[1]}]`);
            try {
                this.logger.log(`🔧 验证写入结果，读取寄存器 ${baseAddress}-${baseAddress + 1}`);
                const readResult = await this.modbusClientService.readHoldingRegisters(baseAddress, 2);
                this.logger.log(`🔧 读取结果: [${readResult[0]}, ${readResult[1]}]`);
                if (readResult[0] !== planValueSplit[0] || readResult[1] !== planValueSplit[1]) {
                    this.logger.warn(`🔧 ⚠️ 写入验证失败！期望: [${planValueSplit[0]}, ${planValueSplit[1]}], 实际: [${readResult[0]}, ${readResult[1]}]`);
                }
                else {
                    this.logger.log(`🔧 ✅ 写入验证成功`);
                }
            }
            catch (error) {
                this.logger.error(`🔧 读取验证失败: ${error.message}`);
            }
            const dbResult = await this.savePlanToDatabase(ledId, planValue, workType, '1');
            return {
                success: true,
                ledId,
                planValue,
                workType,
                baseAddress,
                planValueSplit,
                dbResult,
                message: `成功向LED设备 ${ledId} 发送计划数量 ${planValue}`
            };
        }
        catch (error) {
            this.logger.error(`🔧 直接写入LED设备 ${ledId} 失败: ${error.message}`);
            throw error;
        }
    }
    async debugBatchSend(batchData) {
        const results = [];
        const errors = [];
        this.logger.log(`🔍 开始调试批量发送数据到 ${batchData.length} 个LED设备`);
        for (let i = 0; i < batchData.length; i++) {
            const data = batchData[i];
            const workType = data.workType || '1';
            this.logger.log(`🔍 [${i + 1}/${batchData.length}] 开始发送到LED设备 ${data.ledId}: 计划=${data.planValue}, 班次=${workType}`);
            try {
                const result = await this.sendDataToLedSingle(data.ledId, data.planValue, workType);
                results.push({
                    ledId: data.ledId,
                    success: true,
                    result
                });
                this.logger.log(`🔍 [${i + 1}/${batchData.length}] 成功发送到LED设备 ${data.ledId}`);
                if (i < batchData.length - 1) {
                    this.logger.log(`🔍 等待2秒后发送下一个设备...`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
            catch (error) {
                this.logger.error(`🔍 [${i + 1}/${batchData.length}] 发送到LED设备 ${data.ledId} 失败: ${error.message}`);
                errors.push({
                    ledId: data.ledId,
                    success: false,
                    error: error.message
                });
                if (i < batchData.length - 1) {
                    this.logger.log(`🔍 等待2秒后发送下一个设备...`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
        }
        const summary = {
            total: batchData.length,
            success: results.length,
            failed: errors.length,
            results,
            errors
        };
        this.logger.log(`🔍 调试批量发送完成: 总计${summary.total}个, 成功${summary.success}个, 失败${summary.failed}个`);
        return summary;
    }
    async sendDataToLedSingle(ledId, planValue, workType = '1') {
        try {
            this.logger.log(`sendDataToLedSingle 参数检查:`);
            this.logger.log(`  ledId: ${ledId} (类型: ${typeof ledId})`);
            this.logger.log(`  planValue: ${planValue} (类型: ${typeof planValue})`);
            this.logger.log(`  workType: ${workType} (类型: ${typeof workType})`);
            if (planValue === undefined || planValue === null || isNaN(planValue)) {
                throw new Error(`planValue 参数无效: ${planValue}`);
            }
            const mapping = await this.getLedMapping(ledId);
            if (!mapping) {
                throw new Error(`LED设备 ${ledId} 的映射信息不存在`);
            }
            this.logger.log(`LED设备 ${ledId} 映射信息:`, mapping);
            const connected = await this.modbusClientService.connect(mapping.concentrator.ipAddress, mapping.concentrator.portNumber, mapping.concentrator.timeoutSeconds * 1000);
            if (!connected) {
                throw new Error(`无法连接到集中器 ${mapping.concentrator.ipAddress}:${mapping.concentrator.portNumber}`);
            }
            try {
                const ledIdNumber = parseInt(ledId);
                if (isNaN(ledIdNumber) || ledIdNumber < 1) {
                    throw new Error(`LED设备ID无效: ${ledId}，LED_ID必须是从1开始的数字`);
                }
                const baseAddress = (ledIdNumber - 1) * 4;
                this.logger.log(`LED设备 ${ledId} (数字ID: ${ledIdNumber}), 寄存器基地址: ${baseAddress}`);
                const planValueSplit = this.modbusClientService.splitTo16Bit(planValue);
                const writeSuccess = await this.modbusClientService.writeRegisters(baseAddress, planValueSplit);
                if (!writeSuccess) {
                    throw new Error(`向LED设备 ${ledId} 写入寄存器失败`);
                }
                this.logger.log(`成功向LED设备 ${ledId} 发送计划数量: ${planValue}, 班次: ${workType === '1' ? '白班' : '夜班'}`);
                const dbResult = await this.savePlanToDatabase(ledId, planValue, workType, '1');
                return {
                    success: true,
                    ledId,
                    planValue,
                    workType,
                    mapping,
                    dbResult,
                    message: `成功向LED设备 ${ledId} 发送计划数量 ${planValue}`
                };
            }
            finally {
                await this.modbusClientService.close();
            }
        }
        catch (error) {
            this.logger.error(`单独发送到LED设备 ${ledId} 失败: ${error.message}`);
            throw error;
        }
    }
    async sendDataToLedBatchDirect(ledId, planValue, workType = '1') {
        try {
            this.logger.log(`sendDataToLedBatchDirect 参数检查:`);
            this.logger.log(`  ledId: ${ledId} (类型: ${typeof ledId})`);
            this.logger.log(`  planValue: ${planValue} (类型: ${typeof planValue})`);
            this.logger.log(`  workType: ${workType} (类型: ${typeof workType})`);
            if (planValue === undefined || planValue === null || isNaN(planValue)) {
                throw new Error(`planValue 参数无效: ${planValue}`);
            }
            const ledIdNumber = parseInt(ledId);
            if (isNaN(ledIdNumber) || ledIdNumber < 1) {
                throw new Error(`LED设备ID无效: ${ledId}，LED_ID必须是从1开始的数字`);
            }
            const baseAddress = (ledIdNumber - 1) * 4;
            this.logger.log(`LED设备 ${ledId} (数字ID: ${ledIdNumber}), 寄存器基地址: ${baseAddress}`);
            const planValueSplit = this.modbusClientService.splitTo16Bit(planValue);
            const writeSuccess = await this.modbusClientService.writeRegisters(baseAddress, planValueSplit);
            if (!writeSuccess) {
                throw new Error(`向LED设备 ${ledId} 写入寄存器失败`);
            }
            this.logger.log(`成功向LED设备 ${ledId} 发送计划数量: ${planValue}, 班次: ${workType === '1' ? '白班' : '夜班'}`);
            const dbResult = await this.savePlanToDatabase(ledId, planValue, workType, '1');
            return {
                success: true,
                ledId,
                planValue,
                workType,
                dbResult,
                message: `成功向LED设备 ${ledId} 发送计划数量 ${planValue}`
            };
        }
        catch (error) {
            this.logger.error(`直接发送到LED设备 ${ledId} 失败: ${error.message}`);
            throw error;
        }
    }
    async testAllRegisters() {
        try {
            this.logger.log(`开始测试寄存器状态`);
            const connected = await this.modbusClientService.connect('127.0.0.1', 502, 5000);
            if (!connected) {
                throw new Error('无法连接到模拟集中器');
            }
            try {
                const registers = await this.modbusClientService.readHoldingRegisters(0, 20);
                this.logger.log(`当前寄存器状态:`);
                for (let i = 0; i < registers.length; i++) {
                    this.logger.log(`  寄存器${i}: ${registers[i]}`);
                }
                const ledDevices = [];
                for (let ledId = 1; ledId <= 5; ledId++) {
                    const baseAddress = (ledId - 1) * 4;
                    if (baseAddress + 3 < registers.length) {
                        const planValue = this.modbusClientService.mergeTo32Bit(registers[baseAddress], registers[baseAddress + 1]);
                        const actualValue = this.modbusClientService.mergeTo32Bit(registers[baseAddress + 2], registers[baseAddress + 3]);
                        ledDevices.push({
                            ledId,
                            baseAddress,
                            planValue,
                            actualValue,
                            registers: [
                                registers[baseAddress],
                                registers[baseAddress + 1],
                                registers[baseAddress + 2],
                                registers[baseAddress + 3]
                            ]
                        });
                    }
                }
                this.logger.log(`LED设备数据解析:`);
                ledDevices.forEach(device => {
                    this.logger.log(`  LED${device.ledId}: 计划=${device.planValue}, 实际=${device.actualValue}, 寄存器=[${device.registers.join(',')}]`);
                });
                return {
                    success: true,
                    rawRegisters: registers,
                    ledDevices,
                    message: '寄存器状态读取成功'
                };
            }
            finally {
                await this.modbusClientService.close();
            }
        }
        catch (error) {
            this.logger.error(`测试寄存器状态失败: ${error.message}`);
            throw error;
        }
    }
    async sendDataToLedBatch(ledId, planValue, workType = '1') {
        try {
            this.logger.log(`sendDataToLedBatch 参数检查:`);
            this.logger.log(`  ledId: ${ledId} (类型: ${typeof ledId})`);
            this.logger.log(`  planValue: ${planValue} (类型: ${typeof planValue})`);
            this.logger.log(`  workType: ${workType} (类型: ${typeof workType})`);
            if (planValue === undefined || planValue === null || isNaN(planValue)) {
                throw new Error(`planValue 参数无效: ${planValue}`);
            }
            const mapping = await this.getLedMapping(ledId);
            if (!mapping) {
                throw new Error(`LED设备 ${ledId} 的映射信息不存在`);
            }
            this.logger.log(`LED设备 ${ledId} 映射信息:`, mapping);
            const connected = await this.modbusClientService.connect(mapping.concentrator.ipAddress, mapping.concentrator.portNumber, mapping.concentrator.timeoutSeconds * 1000);
            if (!connected) {
                throw new Error(`无法连接到集中器 ${mapping.concentrator.ipAddress}:${mapping.concentrator.portNumber}`);
            }
            try {
                const ledIdNumber = parseInt(ledId);
                if (isNaN(ledIdNumber) || ledIdNumber < 1) {
                    throw new Error(`LED设备ID无效: ${ledId}，LED_ID必须是从1开始的数字`);
                }
                const baseAddress = (ledIdNumber - 1) * 4;
                this.logger.log(`LED设备 ${ledId} (数字ID: ${ledIdNumber}), 寄存器基地址: ${baseAddress}`);
                const planValueSplit = this.modbusClientService.splitTo16Bit(planValue);
                const writeSuccess = await this.modbusClientService.writeRegisters(baseAddress, planValueSplit);
                if (!writeSuccess) {
                    throw new Error(`向LED设备 ${ledId} 写入寄存器失败`);
                }
                this.logger.log(`成功向LED设备 ${ledId} 发送计划数量: ${planValue}, 班次: ${workType === '1' ? '白班' : '夜班'}`);
                const dbResult = await this.savePlanToDatabase(ledId, planValue, workType, '1');
                return {
                    success: true,
                    ledId,
                    planValue,
                    workType,
                    mapping,
                    dbResult,
                    message: `成功向LED设备 ${ledId} 发送计划数量 ${planValue}`
                };
            }
            finally {
                await this.modbusClientService.close();
            }
        }
        catch (error) {
            this.logger.error(`批量发送到LED设备 ${ledId} 失败: ${error.message}`);
            throw error;
        }
    }
    async getAllLedDataByConcentrator(concentratorId) {
        try {
            this.logger.log(`获取集中器 ${concentratorId} 下所有LED设备的数据`);
            const mappings = await this.dataSource.query(`
        SELECT
          m.MAPPING_ID as "id",
          m.LED_ID as "ledId",
          m.CONCENTRATOR_ID as "concentratorId",
          m.CHANNEL_NUMBER as "channelNumber",
          m.DESCRIPTION as "description",
          m.IS_ACTIVE as "isActive",
          c.CONCENTRATOR_NAME as "concentratorName",
          c.IP_ADDRESS as "ipAddress",
          c.PORT_NUMBER as "portNumber",
          c.TIMEOUT_SECONDS as "timeoutSeconds",
          d.LED_NAME as "ledName"
        FROM CONCENTRATOR_LED_MAPPING m
        LEFT JOIN LED_CONCENTRATOR c ON m.CONCENTRATOR_ID = c.CONCENTRATOR_ID
        LEFT JOIN LED_DEVICE_INFO d ON m.LED_ID = d.LED_ID
        WHERE m.CONCENTRATOR_ID = :1 AND m.IS_ACTIVE = '1'
        ORDER BY CAST(m.CHANNEL_NUMBER AS NUMBER)
      `, [concentratorId]);
            if (!mappings || mappings.length === 0) {
                return {
                    concentratorId,
                    ledDevices: [],
                    message: '该集中器下没有活跃的LED设备映射'
                };
            }
            const ledDataList = [];
            for (const mapping of mappings) {
                try {
                    const ledData = await this.readDataFromLed(mapping.ledId);
                    ledDataList.push({
                        ...mapping,
                        currentData: ledData.data,
                        status: 'success'
                    });
                }
                catch (error) {
                    this.logger.error(`读取LED设备 ${mapping.ledId} 数据失败: ${error.message}`);
                    ledDataList.push({
                        ...mapping,
                        currentData: null,
                        status: 'error',
                        error: error.message
                    });
                }
            }
            return {
                concentratorId,
                concentratorName: mappings[0].concentratorName,
                ledDevices: ledDataList,
                summary: {
                    total: ledDataList.length,
                    success: ledDataList.filter(d => d.status === 'success').length,
                    failed: ledDataList.filter(d => d.status === 'error').length
                }
            };
        }
        catch (error) {
            this.logger.error(`获取集中器 ${concentratorId} 下LED设备数据失败: ${error.message}`);
            throw error;
        }
    }
    async savePlanToDatabase(ledId, planValue, workType, planType = '2') {
        try {
            this.logger.log(`savePlanToDatabase 参数检查:`);
            this.logger.log(`  ledId: ${ledId} (类型: ${typeof ledId})`);
            this.logger.log(`  planValue: ${planValue} (类型: ${typeof planValue})`);
            this.logger.log(`  workType: ${workType} (类型: ${typeof workType})`);
            if (planValue === undefined || planValue === null) {
                throw new Error(`planValue 参数无效: ${planValue}`);
            }
            this.logger.log(`保存LED设备 ${ledId} 的计划数据到数据库: 计划=${planValue}, 班次=${workType}`);
            const now = new Date();
            const currentDate = now.getFullYear() + '-' +
                String(now.getMonth() + 1).padStart(2, '0') + '-' +
                String(now.getDate()).padStart(2, '0');
            this.logger.log(`当前时间: ${now.toLocaleString('zh-CN')}`);
            this.logger.log(`格式化日期: ${currentDate}`);
            this.logger.log(`准备插入数据: LED_ID=${ledId}, PLAN_DATA=${planValue}, DATE=${currentDate}, WORK_TYPE=${workType}`);
            try {
                const seqTest = await this.dataSource.query(`SELECT SQL_LED_PLAN_INFO_ID.NEXTVAL FROM DUAL`);
                this.logger.log(`序列测试成功:`, seqTest);
            }
            catch (seqError) {
                this.logger.error(`序列 SQL_LED_PLAN_INFO_ID 不存在，尝试使用其他方式生成ID`);
                const sequences = await this.dataSource.query(`
          SELECT SEQUENCE_NAME FROM USER_SEQUENCES WHERE SEQUENCE_NAME LIKE '%LED_PLAN%'
        `);
                this.logger.log(`找到的序列:`, sequences);
            }
            try {
                const tableStructure = await this.dataSource.query(`
          SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, NULLABLE
          FROM USER_TAB_COLUMNS
          WHERE TABLE_NAME = 'LED_PLAN_INFO'
          ORDER BY COLUMN_ID
        `);
                this.logger.log(`LED_PLAN_INFO表结构:`, tableStructure);
            }
            catch (structError) {
                this.logger.error(`获取表结构失败:`, structError);
            }
            this.logger.log(`使用计划类型: ${planType} (${planType === '1' ? '批量派发' : '手动派发'})`);
            const insertSQL = `INSERT INTO LED_PLAN_INFO(
        ID, LED_ID, LED_PLAN_DATA, PLAN_DATE, PLAN_TYPE, WORK_TYPE, PUSH_STATUS
      ) VALUES(
        SQL_LED_PLAN_INFO_ID.NEXTVAL, :1, :2, TO_DATE(:3, 'YYYY-MM-DD'), :4, :5, '1'
      )`;
            this.logger.log(`执行插入SQL:`, insertSQL);
            this.logger.log(`参数类型检查:`);
            this.logger.log(`  ledId: ${ledId} (类型: ${typeof ledId})`);
            this.logger.log(`  planValue: ${planValue} (类型: ${typeof planValue})`);
            this.logger.log(`  currentDate: ${currentDate} (类型: ${typeof currentDate})`);
            this.logger.log(`  planType: ${planType} (类型: ${typeof planType}) - 2=手动派发`);
            this.logger.log(`  workType: ${workType} (类型: ${typeof workType})`);
            this.logger.log(`参数数组:`, [ledId, planValue.toString(), currentDate, planType, workType]);
            let result;
            try {
                this.logger.log(`尝试方法1: 所有参数作为字符串`);
                result = await this.dataSource.query(insertSQL, [ledId, planValue.toString(), currentDate, planType, workType]);
            }
            catch (firstError) {
                this.logger.error(`方法1失败:`, firstError.message);
                try {
                    this.logger.log(`尝试方法2: planValue作为数字`);
                    result = await this.dataSource.query(insertSQL, [ledId, planValue, currentDate, planType, workType]);
                }
                catch (secondError) {
                    this.logger.error(`方法2失败:`, secondError.message);
                    try {
                        const insertSQL2 = `INSERT INTO LED_PLAN_INFO(
              ID, LED_ID, LED_PLAN_DATA, PLAN_DATE, PLAN_TYPE, WORK_TYPE, PUSH_STATUS
            ) VALUES(
              SQL_LED_PLAN_INFO_ID.NEXTVAL, '${ledId}', ${planValue}, TO_DATE('${currentDate}', 'YYYY-MM-DD'), ${planType}, '${workType}', '1'
            )`;
                        this.logger.log(`尝试方法3: 直接拼接SQL:`, insertSQL2);
                        result = await this.dataSource.query(insertSQL2);
                    }
                    catch (thirdError) {
                        this.logger.error(`方法3失败:`, thirdError.message);
                        throw thirdError;
                    }
                }
            }
            this.logger.log(`插入结果:`, result);
            const insertedRecord = await this.dataSource.query(`SELECT * FROM (
          SELECT ID, LED_ID, LED_PLAN_DATA,
                 TO_CHAR(PLAN_DATE, 'YYYY-MM-DD') AS PLAN_DATE,
                 PLAN_TYPE, WORK_TYPE, PUSH_STATUS
          FROM LED_PLAN_INFO
          WHERE LED_ID = :1
          ORDER BY ID DESC
        ) WHERE ROWNUM <= 1`, [ledId]);
            if (insertedRecord && insertedRecord.length > 0) {
                const record = insertedRecord[0];
                this.logger.log(`成功保存计划记录，ID: ${record.ID}`);
                return {
                    id: record.ID,
                    ledId: record.LED_ID,
                    planValue: record.LED_PLAN_DATA,
                    planDate: record.PLAN_DATE,
                    planType: record.PLAN_TYPE,
                    workType: record.WORK_TYPE,
                    pushStatus: record.PUSH_STATUS
                };
            }
            else {
                throw new Error('无法获取插入的计划记录');
            }
        }
        catch (error) {
            this.logger.error(`保存LED设备 ${ledId} 计划数据到数据库失败: ${error.message}`);
            this.logger.error(`错误详情:`, error);
            this.logger.error(`错误堆栈:`, error.stack);
            throw new Error(`数据库保存失败: ${error.message}`);
        }
    }
    async saveDataToDatabase(ledId, planValue, actualValue) {
        try {
            this.logger.log(`保存LED设备 ${ledId} 的数据到数据库: 计划=${planValue}, 实际=${actualValue}`);
            const result = await this.dataSource.query(`INSERT INTO LED_DATA_INFO(
          ID, LED_ID, LED_PLAN_DATA, LED_REAL_DATA, CREATE_TIME
        ) VALUES(
          LED_DATA_INFO_SEQ.NEXTVAL, :1, :2, :3, SYSDATE
        )`, [ledId, planValue, actualValue]);
            const insertedRecord = await this.dataSource.query(`SELECT * FROM (
          SELECT ID, LED_ID, LED_PLAN_DATA, LED_REAL_DATA,
                 TO_CHAR(CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') AS CREATE_TIME
          FROM LED_DATA_INFO
          WHERE LED_ID = :1
          ORDER BY ID DESC
        ) WHERE ROWNUM <= 1`, [ledId]);
            if (insertedRecord && insertedRecord.length > 0) {
                const record = insertedRecord[0];
                this.logger.log(`成功保存数据记录，ID: ${record.ID}`);
                return {
                    id: record.ID,
                    ledId: record.LED_ID,
                    planValue: record.LED_PLAN_DATA,
                    actualValue: record.LED_REAL_DATA,
                    createTime: record.CREATE_TIME
                };
            }
            else {
                throw new Error('无法获取插入的数据记录');
            }
        }
        catch (error) {
            this.logger.error(`保存LED设备 ${ledId} 数据到数据库失败: ${error.message}`);
            return {
                id: null,
                ledId,
                planValue,
                actualValue,
                createTime: new Date().toISOString(),
                error: error.message
            };
        }
    }
    async getLedDataHistory(ledId, page = 1, limit = 10) {
        try {
            this.logger.log(`获取LED设备 ${ledId} 的计划派发历史，页码: ${page}, 每页: ${limit}`);
            const offset = (page - 1) * limit;
            this.logger.log(`执行计数查询: LED_ID = ${ledId}`);
            const countResult = await this.dataSource.query(`SELECT COUNT(*) AS total FROM LED_PLAN_INFO WHERE LED_ID = :1 AND PUSH_STATUS = '1'`, [ledId]);
            this.logger.log(`计数查询结果:`, countResult);
            const totalItems = parseInt(countResult[0].TOTAL || countResult[0].total || 0);
            const dataQuery = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT
              p.ID,
              p.LED_ID,
              p.LED_PLAN_DATA,
              TO_CHAR(p.PLAN_DATE, 'YYYY-MM-DD') AS PLAN_DATE,
              p.PLAN_TYPE,
              CASE
                WHEN p.PLAN_TYPE = '1' THEN '批量派发'
                WHEN p.PLAN_TYPE = '2' THEN '手动派发'
                ELSE '未知类型'
              END AS PLAN_TYPE_TEXT,
              p.WORK_TYPE,
              CASE
                WHEN p.WORK_TYPE = '1' THEN '白班'
                WHEN p.WORK_TYPE = '2' THEN '夜班'
                ELSE '未知班次'
              END AS WORK_TYPE_TEXT,
              p.PUSH_STATUS,
              '已推送' AS PUSH_STATUS_TEXT
            FROM LED_PLAN_INFO p
            WHERE p.LED_ID = :1 AND p.PUSH_STATUS = '1'
            ORDER BY p.ID DESC
          ) a WHERE ROWNUM <= :2
        ) WHERE rnum > :3
      `;
            this.logger.log(`执行分页查询: LED_ID = ${ledId}, offset = ${offset}, limit = ${limit}`);
            const data = await this.dataSource.query(dataQuery, [ledId, offset + limit, offset]);
            this.logger.log(`查询结果数量: ${data.length}`);
            if (data.length > 0) {
                this.logger.log(`第一条记录示例:`, JSON.stringify(data[0], null, 2));
            }
            return {
                items: data,
                pagination: {
                    page,
                    limit,
                    totalItems,
                    totalPages: Math.ceil(totalItems / limit)
                }
            };
        }
        catch (error) {
            this.logger.error(`获取LED设备 ${ledId} 计划派发历史失败: ${error.message}`);
            this.logger.error(`错误详情:`, error);
            throw error;
        }
    }
    async getRecentDispatchData(page = 1, limit = 20) {
        try {
            this.logger.log(`获取最近的计划派发记录，页码: ${page}, 每页: ${limit}`);
            const offset = (page - 1) * limit;
            const countResult = await this.dataSource.query(`SELECT COUNT(*) AS total FROM LED_PLAN_INFO WHERE PUSH_STATUS = '1'`);
            const totalItems = parseInt(countResult[0].TOTAL);
            const dataQuery = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT
              p.ID,
              p.LED_ID,
              d.LED_NAME,
              p.LED_PLAN_DATA,
              TO_CHAR(p.PLAN_DATE, 'YYYY-MM-DD') AS PLAN_DATE,
              p.PLAN_TYPE,
              CASE
                WHEN p.PLAN_TYPE = '1' THEN '批量派发'
                WHEN p.PLAN_TYPE = '2' THEN '手动派发'
                ELSE '未知类型'
              END AS PLAN_TYPE_TEXT,
              p.WORK_TYPE,
              CASE
                WHEN p.WORK_TYPE = '1' THEN '白班'
                WHEN p.WORK_TYPE = '2' THEN '夜班'
                ELSE '未知班次'
              END AS WORK_TYPE_TEXT,
              p.PUSH_STATUS,
              '已推送' AS PUSH_STATUS_TEXT
            FROM LED_PLAN_INFO p
            LEFT JOIN LED_DEVICE_INFO d ON p.LED_ID = d.LED_ID
            WHERE p.PUSH_STATUS = '1'
            ORDER BY p.ID DESC
          ) a WHERE ROWNUM <= :1
        ) WHERE rnum > :2
      `;
            const data = await this.dataSource.query(dataQuery, [offset + limit, offset]);
            this.logger.log(`最近派发记录查询结果数量: ${data.length}`);
            if (data.length > 0) {
                this.logger.log(`最近派发记录第一条示例:`, JSON.stringify(data[0], null, 2));
            }
            return {
                items: data,
                pagination: {
                    page,
                    limit,
                    totalItems,
                    totalPages: Math.ceil(totalItems / limit)
                }
            };
        }
        catch (error) {
            this.logger.error(`获取最近计划派发记录失败: ${error.message}`);
            throw error;
        }
    }
    async getModbusStatus() {
        try {
            const isRealMode = process.env.MODBUS_REAL_MODE === 'true';
            return {
                mode: isRealMode ? 'real' : 'mock',
                description: isRealMode ? '真实ModbusTCP模式' : '模拟ModbusTCP模式',
                isConnected: this.modbusClientService['client']?.isOpen || false,
                environment: process.env.NODE_ENV || 'development'
            };
        }
        catch (error) {
            this.logger.error(`获取ModbusTCP状态失败: ${error.message}`);
            throw error;
        }
    }
    async testModbusConnection(ip, port) {
        try {
            this.logger.log(`开始测试ModbusTCP连接: ${ip}:${port}`);
            const startTime = Date.now();
            const connected = await this.modbusClientService.connect(ip, port, 5000);
            if (!connected) {
                return {
                    success: false,
                    message: `无法连接到 ${ip}:${port}`,
                    duration: Date.now() - startTime
                };
            }
            try {
                const testResult = await this.modbusClientService.readHoldingRegisters(0, 1);
                const duration = Date.now() - startTime;
                return {
                    success: true,
                    message: `成功连接到 ${ip}:${port}`,
                    duration,
                    testData: testResult,
                    mode: process.env.MODBUS_REAL_MODE === 'true' ? 'real' : 'mock'
                };
            }
            finally {
                await this.modbusClientService.close();
            }
        }
        catch (error) {
            this.logger.error(`测试ModbusTCP连接失败: ${error.message}`);
            return {
                success: false,
                message: `连接测试失败: ${error.message}`,
                duration: Date.now() - (Date.now() - 5000),
                error: error.message
            };
        }
    }
    async testPlanTable() {
        try {
            this.logger.log('开始测试LED_PLAN_INFO表');
            const tableTest = await this.dataSource.query(`SELECT COUNT(*) AS total FROM LED_PLAN_INFO WHERE ROWNUM <= 1`);
            this.logger.log('表存在性测试结果:', tableTest);
            const structureTest = await this.dataSource.query(`SELECT COLUMN_NAME, DATA_TYPE FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'LED_PLAN_INFO' ORDER BY COLUMN_ID`);
            this.logger.log('表结构:', structureTest);
            const sampleData = await this.dataSource.query(`SELECT * FROM LED_PLAN_INFO WHERE ROWNUM <= 5`);
            this.logger.log('示例数据:', sampleData);
            return {
                tableExists: true,
                totalRecords: tableTest[0].TOTAL || tableTest[0].total || 0,
                structure: structureTest,
                sampleData: sampleData
            };
        }
        catch (error) {
            this.logger.error(`测试LED_PLAN_INFO表失败: ${error.message}`);
            return {
                tableExists: false,
                error: error.message
            };
        }
    }
    async testInsertPlan(ledId, planValue, workType) {
        try {
            this.logger.log(`测试插入计划数据: LED_ID=${ledId}, PLAN_VALUE=${planValue}, WORK_TYPE=${workType}`);
            const result = await this.savePlanToDatabase(ledId, planValue, workType);
            return {
                success: true,
                message: '测试插入成功',
                result
            };
        }
        catch (error) {
            this.logger.error(`测试插入失败: ${error.message}`);
            return {
                success: false,
                message: '测试插入失败',
                error: error.message
            };
        }
    }
};
exports.ManualDispatchService = ManualDispatchService;
exports.ManualDispatchService = ManualDispatchService = ManualDispatchService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(led_concentrator_entity_1.LedConcentrator)),
    __param(1, (0, typeorm_1.InjectRepository)(concentrator_led_mapping_entity_1.ConcentratorLedMapping)),
    __param(2, (0, typeorm_1.InjectRepository)(led_data_entity_1.LedData)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource,
        modbus_client_service_1.ModbusClientService,
        led_mapping_service_1.LedMappingService])
], ManualDispatchService);
//# sourceMappingURL=manual-dispatch.service.js.map