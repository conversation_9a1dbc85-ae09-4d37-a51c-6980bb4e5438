"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedLedConcentrators = seedLedConcentrators;
const led_concentrator_entity_1 = require("../../system_config/entities/led-concentrator.entity");
const common_1 = require("@nestjs/common");
async function seedLedConcentrators(dataSource) {
    const logger = new common_1.Logger('LedConcentratorSeed');
    try {
        const existingCount = await dataSource
            .getRepository(led_concentrator_entity_1.LedConcentrator)
            .count();
        if (existingCount > 0) {
            logger.log(`已存在 ${existingCount} 条集中器数据，跳过种子数据创建`);
            return;
        }
        const concentrators = [
            {
                code: 'CONC-001',
                name: '主车间集中器',
                ipAddress: '*************',
                portNumber: 502,
                protocolType: 'MODBUS_TCP',
                timeoutSeconds: 10,
                maxRetryCount: 3,
                isActive: true,
            },
            {
                code: 'CONC-002',
                name: '辅助车间集中器',
                ipAddress: '*************',
                portNumber: 502,
                protocolType: 'MODBUS_TCP',
                timeoutSeconds: 10,
                maxRetryCount: 3,
                isActive: true,
            },
        ];
        const queryRunner = dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            for (const concentrator of concentrators) {
                await queryRunner.query(`
          INSERT INTO LED_CONCENTRATOR (
            CONCENTRATOR_ID, 
            CONCENTRATOR_CODE, 
            CONCENTRATOR_NAME, 
            IP_ADDRESS, 
            PORT_NUMBER, 
            PROTOCOL_TYPE, 
            TIMEOUT_SECONDS, 
            MAX_RETRY_COUNT, 
            IS_ACTIVE,
            CREATED_BY, 
            CREATION_DATE
          ) VALUES (
            LED_CONCENTRATOR_SEQ.NEXTVAL, 
            '${concentrator.code}', 
            '${concentrator.name}', 
            '${concentrator.ipAddress}', 
            ${concentrator.portNumber}, 
            '${concentrator.protocolType}', 
            ${concentrator.timeoutSeconds}, 
            ${concentrator.maxRetryCount}, 
            '${concentrator.isActive ? '1' : '0'}',
            'SYSTEM', 
            SYSDATE
          )
        `);
                logger.log(`成功创建集中器 ${concentrator.code} - ${concentrator.name}`);
            }
            await queryRunner.commitTransaction();
            logger.log(`成功创建 ${concentrators.length} 条集中器数据`);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            logger.error(`创建集中器数据失败: ${error.message}`);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    catch (error) {
        logger.error(`创建集中器种子数据失败: ${error.message}`);
        throw error;
    }
}
//# sourceMappingURL=led-concentrator.seed.js.map