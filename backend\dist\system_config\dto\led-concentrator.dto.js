"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConcentratorQueryDto = exports.UpdateConcentratorDto = exports.CreateConcentratorDto = exports.LedMappingForConcentratorDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class LedMappingForConcentratorDto {
    constructor() {
        this.isActive = true;
    }
}
exports.LedMappingForConcentratorDto = LedMappingForConcentratorDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '映射ID必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], LedMappingForConcentratorDto.prototype, "id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '集中器ID必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], LedMappingForConcentratorDto.prototype, "concentratorId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'LED设备ID不能为空' }),
    (0, class_validator_1.IsString)({ message: 'LED设备ID必须为字符串' }),
    __metadata("design:type", String)
], LedMappingForConcentratorDto.prototype, "ledId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '通道号必须为字符串' }),
    __metadata("design:type", String)
], LedMappingForConcentratorDto.prototype, "channelNumber", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '描述必须为字符串' }),
    __metadata("design:type", String)
], LedMappingForConcentratorDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: '启用状态必须为布尔值' }),
    __metadata("design:type", Boolean)
], LedMappingForConcentratorDto.prototype, "isActive", void 0);
class CreateConcentratorDto {
    constructor() {
        this.isActive = true;
    }
}
exports.CreateConcentratorDto = CreateConcentratorDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '集中器代码不能为空' }),
    (0, class_validator_1.IsString)({ message: '集中器代码必须为字符串' }),
    __metadata("design:type", String)
], CreateConcentratorDto.prototype, "code", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '集中器名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '集中器名称必须为字符串' }),
    __metadata("design:type", String)
], CreateConcentratorDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'IP地址不能为空' }),
    (0, class_validator_1.IsIP)(undefined, { message: 'IP地址格式不正确' }),
    __metadata("design:type", String)
], CreateConcentratorDto.prototype, "ipAddress", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '端口号不能为空' }),
    (0, class_validator_1.IsInt)({ message: '端口号必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateConcentratorDto.prototype, "portNumber", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '协议类型不能为空' }),
    (0, class_validator_1.IsString)({ message: '协议类型必须为字符串' }),
    __metadata("design:type", String)
], CreateConcentratorDto.prototype, "protocolType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '超时秒数必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateConcentratorDto.prototype, "timeoutSeconds", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '最大重试次数必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateConcentratorDto.prototype, "maxRetryCount", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '区域代码必须为字符串' }),
    __metadata("design:type", String)
], CreateConcentratorDto.prototype, "areaCode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '描述必须为字符串' }),
    __metadata("design:type", String)
], CreateConcentratorDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: '启用状态必须为布尔值' }),
    __metadata("design:type", Boolean)
], CreateConcentratorDto.prototype, "isActive", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'LED映射必须为数组' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => LedMappingForConcentratorDto),
    __metadata("design:type", Array)
], CreateConcentratorDto.prototype, "ledMappings", void 0);
class UpdateConcentratorDto {
}
exports.UpdateConcentratorDto = UpdateConcentratorDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '集中器代码必须为字符串' }),
    __metadata("design:type", String)
], UpdateConcentratorDto.prototype, "code", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '集中器名称必须为字符串' }),
    __metadata("design:type", String)
], UpdateConcentratorDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIP)(undefined, { message: 'IP地址格式不正确' }),
    __metadata("design:type", String)
], UpdateConcentratorDto.prototype, "ipAddress", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '端口号必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UpdateConcentratorDto.prototype, "portNumber", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '协议类型必须为字符串' }),
    __metadata("design:type", String)
], UpdateConcentratorDto.prototype, "protocolType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '超时秒数必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UpdateConcentratorDto.prototype, "timeoutSeconds", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '最大重试次数必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UpdateConcentratorDto.prototype, "maxRetryCount", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '区域代码必须为字符串' }),
    __metadata("design:type", String)
], UpdateConcentratorDto.prototype, "areaCode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '描述必须为字符串' }),
    __metadata("design:type", String)
], UpdateConcentratorDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: '启用状态必须为布尔值' }),
    __metadata("design:type", Boolean)
], UpdateConcentratorDto.prototype, "isActive", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'LED映射必须为数组' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => LedMappingForConcentratorDto),
    __metadata("design:type", Array)
], UpdateConcentratorDto.prototype, "ledMappings", void 0);
class ConcentratorQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
exports.ConcentratorQueryDto = ConcentratorQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '页码必须为整数' }),
    (0, class_validator_1.Min)(1, { message: '页码必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ConcentratorQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '每页数量必须为整数' }),
    (0, class_validator_1.Min)(1, { message: '每页数量必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], ConcentratorQueryDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '搜索关键词必须为字符串' }),
    __metadata("design:type", String)
], ConcentratorQueryDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '集中器代码必须为字符串' }),
    __metadata("design:type", String)
], ConcentratorQueryDto.prototype, "code", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '集中器名称必须为字符串' }),
    __metadata("design:type", String)
], ConcentratorQueryDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'IP地址必须为字符串' }),
    __metadata("design:type", String)
], ConcentratorQueryDto.prototype, "ipAddress", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '区域代码必须为字符串' }),
    __metadata("design:type", String)
], ConcentratorQueryDto.prototype, "areaCode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true')
            return true;
        if (value === 'false')
            return false;
        return value;
    }),
    (0, class_validator_1.IsBoolean)({ message: '启用状态必须为布尔值' }),
    __metadata("design:type", Boolean)
], ConcentratorQueryDto.prototype, "isActive", void 0);
//# sourceMappingURL=led-concentrator.dto.js.map