# 部门管理模块

## 功能概述

部门管理模块提供了完整的部门信息管理功能，支持层级结构的部门组织架构管理。

## 主要功能

### 1. 部门列表管理
- 分页显示部门列表
- 支持按部门编码、名称搜索
- 支持按层级、状态筛选
- 显示部门层级关系和上级部门信息

### 2. 部门树形视图
- 使用 react-organizational-chart 插件展示组织架构
- 可视化显示部门层级关系
- 支持在树形视图中直接进行编辑、删除、添加子部门操作

### 3. 部门CRUD操作
- **创建部门**: 支持设置部门编码、名称、上级部门、负责人、描述等信息
- **编辑部门**: 支持修改部门信息，自动处理层级路径更新
- **删除部门**: 删除前检查是否有子部门、关联用户或设备
- **查看部门**: 显示部门详细信息

### 4. 数据验证
- 部门编码唯一性验证
- 部门名称长度验证
- 上级部门循环引用检查
- 表单字段必填验证

## 文件结构

```
frontend/src/features/department/
├── pages/
│   └── DepartmentManagementPage.jsx    # 主页面组件
├── components/
│   ├── DepartmentForm.jsx              # 部门表单组件
│   └── DepartmentTreeView.jsx          # 树形视图组件
├── services/
│   └── departmentService.js            # API服务
└── README.md                           # 说明文档
```

## 组件说明

### DepartmentManagementPage
主页面组件，包含：
- 页面标题和操作栏
- 视图模式切换（列表/树形）
- 搜索和筛选功能
- 数据表格和树形视图
- 表单弹窗和删除确认弹窗

### DepartmentForm
部门表单组件，支持：
- 新建和编辑模式
- 表单验证
- 上级部门选择（过滤循环引用）
- 状态设置

### DepartmentTreeView
树形视图组件，特点：
- 使用 react-organizational-chart 渲染
- 支持多层级展示
- 每个节点显示部门信息和操作按钮
- 响应式设计

## API接口

### 获取部门列表
```
GET /api/departments
参数: page, limit, search, parentId, deptLevel, isActive
```

### 获取部门树形结构
```
GET /api/departments/tree
```

### 创建部门
```
POST /api/departments
Body: { DEPT_CODE, DEPT_NAME, PARENT_DEPT_ID, ... }
```

### 更新部门
```
PATCH /api/departments/:id
Body: { DEPT_CODE, DEPT_NAME, PARENT_DEPT_ID, ... }
```

### 删除部门
```
DELETE /api/departments/:id
```

### 获取部门详情
```
GET /api/departments/:id
```

## 数据库表结构

### LED_DEPARTMENT 表
- DEPT_ID: 部门ID（主键）
- DEPT_CODE: 部门编码（唯一）
- DEPT_NAME: 部门名称
- PARENT_DEPT_ID: 上级部门ID
- DEPT_LEVEL: 部门层级
- DEPT_PATH: 部门路径
- MANAGER_USER_ID: 负责人用户ID
- DESCRIPTION: 描述
- SORT_ORDER: 排序
- IS_ACTIVE: 是否启用
- 创建和更新时间字段

## 使用说明

1. **访问路径**: `/departments`
2. **权限要求**: 需要相应的菜单访问权限
3. **操作流程**:
   - 进入部门管理页面
   - 选择列表视图或树形视图
   - 使用搜索和筛选功能查找部门
   - 点击"添加部门"创建新部门
   - 点击编辑按钮修改部门信息
   - 点击删除按钮删除部门（需确认）

## 注意事项

1. 删除部门前会检查是否有子部门、关联用户或设备
2. 修改上级部门时会自动更新所有子部门的路径和层级
3. 部门编码必须唯一，不能重复
4. 不能将部门设置为自己的子部门（防止循环引用）
5. 树形视图在移动端会自动调整布局以适应小屏幕

## 技术特点

- 使用 React Query 进行数据管理和缓存
- 支持服务器端分页和搜索
- 响应式设计，支持PC和移动端
- 使用 Tailwind CSS 进行样式设计
- 集成了完整的错误处理和用户反馈机制
