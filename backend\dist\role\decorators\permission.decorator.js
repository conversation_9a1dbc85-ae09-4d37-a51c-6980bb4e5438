"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequirePermissions = exports.RequireDevicePermission = exports.RequireMenu = void 0;
const common_1 = require("@nestjs/common");
const RequireMenu = (menuCode) => (0, common_1.SetMetadata)('menuCode', menuCode);
exports.RequireMenu = RequireMenu;
const RequireDevicePermission = (permissionType) => (0, common_1.SetMetadata)('devicePermission', permissionType);
exports.RequireDevicePermission = RequireDevicePermission;
const RequirePermissions = (menuCode, devicePermission) => {
    return (target, propertyKey, descriptor) => {
        if (menuCode) {
            (0, common_1.SetMetadata)('menuCode', menuCode)(target, propertyKey, descriptor);
        }
        if (devicePermission) {
            (0, common_1.SetMetadata)('devicePermission', devicePermission)(target, propertyKey, descriptor);
        }
    };
};
exports.RequirePermissions = RequirePermissions;
//# sourceMappingURL=permission.decorator.js.map