import { ProcessRouteService } from '../services/process-route.service';
import { CreateProcessRouteDto, UpdateProcessRouteDto } from '../dto/process-route.dto';
export declare class ProcessRouteController {
    private readonly processRouteService;
    constructor(processRouteService: ProcessRouteService);
    findAll(query: any): Promise<{
        items: import("../entities/process-route.entity").ProcessRoute[];
        meta: any;
    }>;
    getRouteOptions(): Promise<{
        items: any[];
    }>;
    findOne(id: string): Promise<import("../entities/process-route.entity").ProcessRoute>;
    create(createDto: CreateProcessRouteDto, req: any): Promise<import("../entities/process-route.entity").ProcessRoute>;
    update(id: string, updateDto: UpdateProcessRouteDto, req: any): Promise<import("../entities/process-route.entity").ProcessRoute>;
    remove(id: string): Promise<void>;
}
