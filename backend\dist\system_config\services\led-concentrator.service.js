"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LedConcentratorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedConcentratorService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const led_concentrator_entity_1 = require("../entities/led-concentrator.entity");
const concentrator_led_mapping_entity_1 = require("../entities/concentrator-led-mapping.entity");
let LedConcentratorService = LedConcentratorService_1 = class LedConcentratorService {
    constructor(ledConcentratorRepository, concentratorLedMappingRepository, dataSource) {
        this.ledConcentratorRepository = ledConcentratorRepository;
        this.concentratorLedMappingRepository = concentratorLedMappingRepository;
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(LedConcentratorService_1.name);
    }
    async findAll(query) {
        this.logger.log(`查询LED集中器，参数: ${JSON.stringify(query)}`);
        try {
            let whereClause = '';
            const params = [];
            let paramIndex = 1;
            if (query.search) {
                whereClause += ' WHERE (CONCENTRATOR_CODE LIKE :1 OR CONCENTRATOR_NAME LIKE :1 OR IP_ADDRESS LIKE :1 OR AREA_CODE LIKE :1)';
                params.push(`%${query.search}%`);
                paramIndex++;
            }
            if (query.code) {
                whereClause += whereClause ? ` AND ` : ` WHERE `;
                whereClause += `CONCENTRATOR_CODE LIKE :${paramIndex}`;
                params.push(`%${query.code}%`);
                paramIndex++;
            }
            if (query.name) {
                whereClause += whereClause ? ` AND ` : ` WHERE `;
                whereClause += `CONCENTRATOR_NAME LIKE :${paramIndex}`;
                params.push(`%${query.name}%`);
                paramIndex++;
            }
            if (query.ipAddress) {
                whereClause += whereClause ? ` AND ` : ` WHERE `;
                whereClause += `IP_ADDRESS LIKE :${paramIndex}`;
                params.push(`%${query.ipAddress}%`);
                paramIndex++;
            }
            if (query.areaCode) {
                whereClause += whereClause ? ` AND ` : ` WHERE `;
                whereClause += `AREA_CODE LIKE :${paramIndex}`;
                params.push(`%${query.areaCode}%`);
                paramIndex++;
            }
            if (query.isActive !== undefined) {
                whereClause += whereClause ? ` AND ` : ` WHERE `;
                whereClause += `IS_ACTIVE = :${paramIndex}`;
                params.push(query.isActive ? 1 : 0);
                paramIndex++;
            }
            const countQuery = `SELECT COUNT(*) AS TOTAL FROM LED_CONCENTRATOR${whereClause}`;
            const countResult = await this.dataSource.query(countQuery, params);
            const total = parseInt(countResult[0].TOTAL);
            const page = query.page || 1;
            const limit = query.limit || 10;
            const offset = (page - 1) * limit;
            const selectQuery = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT * FROM LED_CONCENTRATOR${whereClause}
            ORDER BY CONCENTRATOR_ID DESC
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const concentrators = await this.dataSource.query(selectQuery, params);
            const concentratorIds = concentrators.map(c => c.CONCENTRATOR_ID);
            let mappingData = [];
            if (concentratorIds.length > 0) {
                const mappingQuery = `
          SELECT * FROM CONCENTRATOR_LED_MAPPING 
          WHERE CONCENTRATOR_ID IN (${concentratorIds.join(',')})
        `;
                mappingData = await this.dataSource.query(mappingQuery);
            }
            const items = concentrators.map(concentrator => {
                const ledMappings = mappingData.filter(mapping => mapping.CONCENTRATOR_ID === concentrator.CONCENTRATOR_ID);
                return {
                    id: concentrator.CONCENTRATOR_ID,
                    code: concentrator.CONCENTRATOR_CODE,
                    name: concentrator.CONCENTRATOR_NAME,
                    ipAddress: concentrator.IP_ADDRESS,
                    portNumber: concentrator.PORT_NUMBER,
                    protocolType: concentrator.PROTOCOL_TYPE,
                    authKey: concentrator.AUTH_KEY,
                    refreshInterval: concentrator.REFRESH_INTERVAL,
                    timeoutSeconds: concentrator.TIMEOUT_SECONDS,
                    maxRetryCount: concentrator.MAX_RETRY_COUNT,
                    areaCode: concentrator.AREA_CODE,
                    description: concentrator.DESCRIPTION,
                    isActive: concentrator.IS_ACTIVE === 1,
                    lastHeartbeatTime: concentrator.LAST_HEARTBEAT_TIME,
                    connectionStatus: concentrator.CONNECTION_STATUS,
                    createdBy: concentrator.CREATED_BY,
                    creationDate: concentrator.CREATION_DATE,
                    lastUpdatedBy: concentrator.LAST_UPDATED_BY,
                    lastUpdateDate: concentrator.LAST_UPDATE_DATE,
                    ledMappings: ledMappings.map(mapping => ({
                        id: mapping.MAPPING_ID,
                        concentratorId: mapping.CONCENTRATOR_ID,
                        ledId: mapping.LED_ID,
                        channelNumber: mapping.CHANNEL_NUMBER,
                        description: mapping.DESCRIPTION,
                        isActive: mapping.IS_ACTIVE === 1,
                        createdBy: mapping.CREATED_BY,
                        creationDate: mapping.CREATION_DATE,
                        lastUpdatedBy: mapping.LAST_UPDATED_BY,
                        lastUpdateDate: mapping.LAST_UPDATE_DATE
                    }))
                };
            });
            this.logger.log(`查询结果：找到 ${items.length} 条记录，总共 ${total} 条`);
            return {
                items,
                meta: {
                    total,
                    currentPage: page,
                    totalPages: Math.ceil(total / limit),
                    itemsPerPage: limit
                }
            };
        }
        catch (error) {
            this.logger.error(`查询LED集中器失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findOne(id) {
        try {
            this.logger.log(`查询ID为 ${id} 的集中器`);
            const concentratorQuery = `SELECT * FROM LED_CONCENTRATOR WHERE CONCENTRATOR_ID = :1`;
            const concentrators = await this.dataSource.query(concentratorQuery, [id]);
            if (!concentrators || concentrators.length === 0) {
                throw new common_1.NotFoundException(`LED Concentrator with ID ${id} not found`);
            }
            const concentratorData = concentrators[0];
            const mappingQuery = `SELECT * FROM CONCENTRATOR_LED_MAPPING WHERE CONCENTRATOR_ID = :1`;
            const mappings = await this.dataSource.query(mappingQuery, [id]);
            const concentrator = {
                id: concentratorData.CONCENTRATOR_ID,
                code: concentratorData.CONCENTRATOR_CODE,
                name: concentratorData.CONCENTRATOR_NAME,
                ipAddress: concentratorData.IP_ADDRESS,
                portNumber: concentratorData.PORT_NUMBER,
                protocolType: concentratorData.PROTOCOL_TYPE,
                authKey: concentratorData.AUTH_KEY,
                refreshInterval: concentratorData.REFRESH_INTERVAL,
                timeoutSeconds: concentratorData.TIMEOUT_SECONDS,
                maxRetryCount: concentratorData.MAX_RETRY_COUNT,
                areaCode: concentratorData.AREA_CODE,
                description: concentratorData.DESCRIPTION,
                isActive: concentratorData.IS_ACTIVE === 1,
                lastHeartbeatTime: concentratorData.LAST_HEARTBEAT_TIME,
                connectionStatus: concentratorData.CONNECTION_STATUS,
                createdBy: concentratorData.CREATED_BY,
                creationDate: concentratorData.CREATION_DATE,
                lastUpdatedBy: concentratorData.LAST_UPDATED_BY,
                lastUpdateDate: concentratorData.LAST_UPDATE_DATE,
                ledMappings: mappings.map(mapping => ({
                    id: mapping.MAPPING_ID,
                    concentratorId: mapping.CONCENTRATOR_ID,
                    ledId: mapping.LED_ID,
                    channelNumber: mapping.CHANNEL_NUMBER,
                    description: mapping.DESCRIPTION,
                    isActive: mapping.IS_ACTIVE === 1,
                    createdBy: mapping.CREATED_BY,
                    creationDate: mapping.CREATION_DATE,
                    lastUpdatedBy: mapping.LAST_UPDATED_BY,
                    lastUpdateDate: mapping.LAST_UPDATE_DATE
                }))
            };
            return concentrator;
        }
        catch (error) {
            this.logger.error(`查询ID为 ${id} 的集中器失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async create(createConcentratorDto) {
        const { ledMappings, ...concentratorData } = createConcentratorDto;
        this.logger.log(`创建LED集中器: ${JSON.stringify(concentratorData)}`);
        const existingConcentrator = await this.dataSource.query(`SELECT COUNT(*) AS count FROM LED_CONCENTRATOR WHERE CONCENTRATOR_CODE = :1`, [concentratorData.code]);
        if (parseInt(existingConcentrator[0].COUNT) > 0) {
            throw new Error(`集中器代码 "${concentratorData.code}" 已存在，请使用不同的代码`);
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            await queryRunner.query(`INSERT INTO LED_CONCENTRATOR(
          CONCENTRATOR_CODE,
          CONCENTRATOR_NAME,
          IP_ADDRESS,
          PORT_NUMBER,
          PROTOCOL_TYPE,
          TIMEOUT_SECONDS,
          MAX_RETRY_COUNT,
          AREA_CODE,
          DESCRIPTION,
          IS_ACTIVE,
          CREATED_BY,
          CREATION_DATE
        ) VALUES(
          :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, 'SYSTEM', SYSDATE
        )`, [
                concentratorData.code,
                concentratorData.name,
                concentratorData.ipAddress,
                concentratorData.portNumber,
                concentratorData.protocolType,
                concentratorData.timeoutSeconds || 10,
                concentratorData.maxRetryCount || 3,
                concentratorData.areaCode || null,
                concentratorData.description || null,
                concentratorData.isActive ? '1' : '0'
            ]);
            const newRecord = await queryRunner.query(`SELECT CONCENTRATOR_ID FROM LED_CONCENTRATOR WHERE CONCENTRATOR_CODE = :1`, [concentratorData.code]);
            const newId = newRecord[0].CONCENTRATOR_ID;
            this.logger.log(`新创建的集中器ID: ${newId}`);
            if (ledMappings && ledMappings.length > 0) {
                for (const mapping of ledMappings) {
                    const existingMapping = await queryRunner.query(`SELECT COUNT(*) AS count FROM CONCENTRATOR_LED_MAPPING WHERE CONCENTRATOR_ID = :1 AND LED_ID = :2`, [newId, mapping.ledId]);
                    if (parseInt(existingMapping[0].COUNT) > 0) {
                        throw new Error(`LED设备 "${mapping.ledId}" 已经映射到此集中器，不能重复映射`);
                    }
                    await queryRunner.query(`INSERT INTO CONCENTRATOR_LED_MAPPING(
              MAPPING_ID,
              CONCENTRATOR_ID,
              LED_ID,
              CHANNEL_NUMBER,
              DESCRIPTION,
              IS_ACTIVE,
              CREATED_BY,
              CREATION_DATE
            ) VALUES(
              CONCENTRATOR_LED_MAPPING_SEQ.NEXTVAL,
              :1, :2, :3, :4, :5, 'SYSTEM', SYSDATE
            )`, [
                        newId,
                        mapping.ledId,
                        mapping.channelNumber,
                        mapping.description || null,
                        mapping.isActive ? '1' : '0'
                    ]);
                }
            }
            await queryRunner.commitTransaction();
            return this.findOne(newId);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`创建LED集中器失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`创建LED集中器失败: ${error.message}`);
        }
        finally {
            await queryRunner.release();
        }
    }
    async update(id, updateConcentratorDto) {
        const { ledMappings, ...concentratorData } = updateConcentratorDto;
        const concentrator = await this.findOne(id);
        if (concentratorData.code !== undefined) {
            const existingConcentrator = await this.dataSource.query(`SELECT COUNT(*) AS count FROM LED_CONCENTRATOR WHERE CONCENTRATOR_CODE = :1 AND CONCENTRATOR_ID != :2`, [concentratorData.code, id]);
            if (parseInt(existingConcentrator[0].COUNT) > 0) {
                throw new Error(`集中器代码 "${concentratorData.code}" 已被其他集中器使用，请使用不同的代码`);
            }
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            let updateQuery = 'UPDATE LED_CONCENTRATOR SET ';
            const updateValues = [];
            let paramIndex = 1;
            if (concentratorData.code !== undefined) {
                updateQuery += `CONCENTRATOR_CODE = :${paramIndex}, `;
                updateValues.push(concentratorData.code);
                paramIndex++;
            }
            if (concentratorData.name !== undefined) {
                updateQuery += `CONCENTRATOR_NAME = :${paramIndex}, `;
                updateValues.push(concentratorData.name);
                paramIndex++;
            }
            if (concentratorData.ipAddress !== undefined) {
                updateQuery += `IP_ADDRESS = :${paramIndex}, `;
                updateValues.push(concentratorData.ipAddress);
                paramIndex++;
            }
            if (concentratorData.portNumber !== undefined) {
                updateQuery += `PORT_NUMBER = :${paramIndex}, `;
                updateValues.push(concentratorData.portNumber);
                paramIndex++;
            }
            if (concentratorData.protocolType !== undefined) {
                updateQuery += `PROTOCOL_TYPE = :${paramIndex}, `;
                updateValues.push(concentratorData.protocolType);
                paramIndex++;
            }
            if (concentratorData.timeoutSeconds !== undefined) {
                updateQuery += `TIMEOUT_SECONDS = :${paramIndex}, `;
                updateValues.push(concentratorData.timeoutSeconds);
                paramIndex++;
            }
            if (concentratorData.maxRetryCount !== undefined) {
                updateQuery += `MAX_RETRY_COUNT = :${paramIndex}, `;
                updateValues.push(concentratorData.maxRetryCount);
                paramIndex++;
            }
            if (concentratorData.areaCode !== undefined) {
                updateQuery += `AREA_CODE = :${paramIndex}, `;
                updateValues.push(concentratorData.areaCode);
                paramIndex++;
            }
            if (concentratorData.description !== undefined) {
                updateQuery += `DESCRIPTION = :${paramIndex}, `;
                updateValues.push(concentratorData.description);
                paramIndex++;
            }
            if (concentratorData.isActive !== undefined) {
                updateQuery += `IS_ACTIVE = :${paramIndex}, `;
                updateValues.push(concentratorData.isActive ? '1' : '0');
                paramIndex++;
            }
            updateQuery += `LAST_UPDATED_BY = 'SYSTEM', LAST_UPDATE_DATE = SYSDATE `;
            updateQuery += `WHERE CONCENTRATOR_ID = :${paramIndex}`;
            updateValues.push(id);
            if (updateValues.length > 1) {
                await queryRunner.query(updateQuery, updateValues);
            }
            if (ledMappings && ledMappings.length > 0) {
                await queryRunner.query(`DELETE FROM CONCENTRATOR_LED_MAPPING WHERE CONCENTRATOR_ID = :1`, [id]);
                for (const mapping of ledMappings) {
                    await queryRunner.query(`INSERT INTO CONCENTRATOR_LED_MAPPING(
              MAPPING_ID, 
              CONCENTRATOR_ID, 
              LED_ID, 
              CHANNEL_NUMBER, 
              DESCRIPTION, 
              IS_ACTIVE,
              CREATED_BY, 
              CREATION_DATE
            ) VALUES(
              CONCENTRATOR_LED_MAPPING_SEQ.NEXTVAL, 
              :1, :2, :3, :4, :5, 'SYSTEM', SYSDATE
            )`, [
                        id,
                        mapping.ledId,
                        mapping.channelNumber,
                        mapping.description || null,
                        mapping.isActive ? '1' : '0'
                    ]);
                }
            }
            await queryRunner.commitTransaction();
            return this.findOne(id);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`更新LED集中器 ${id} 失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`更新LED集中器失败: ${error.message}`);
        }
        finally {
            await queryRunner.release();
        }
    }
    async remove(id) {
        const concentrator = await this.findOne(id);
        if (!concentrator) {
            throw new common_1.NotFoundException(`LED Concentrator with ID ${id} not found`);
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            console.log(`【调试】开始删除ID为 ${id} 的集中器`);
            await queryRunner.manager.query(`DELETE FROM CONCENTRATOR_LED_MAPPING WHERE CONCENTRATOR_ID = :1`, [id]);
            console.log(`【调试】已删除集中器的映射关系`);
            await queryRunner.manager.query(`DELETE FROM LED_CONCENTRATOR WHERE CONCENTRATOR_ID = :1`, [id]);
            console.log(`【调试】已删除集中器记录`);
            await queryRunner.commitTransaction();
            console.log(`【调试】事务已提交，删除操作完成`);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            console.error(`【错误】删除ID为 ${id} 的集中器失败:`, error);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async testConnection(id) {
        const concentrator = await this.findOne(id);
        try {
            const success = Math.random() > 0.2;
            concentrator.connectionStatus = success ? 'CONNECTED' : 'FAILED';
            concentrator.lastHeartbeatTime = success ? new Date() : concentrator.lastHeartbeatTime;
            await this.ledConcentratorRepository.save(concentrator);
            return {
                success,
                message: success
                    ? `成功连接到集中器 ${concentrator.name} (${concentrator.ipAddress}:${concentrator.portNumber})`
                    : `连接集中器 ${concentrator.name} 失败，请检查IP地址和端口号是否正确`
            };
        }
        catch (error) {
            concentrator.connectionStatus = 'ERROR';
            await this.ledConcentratorRepository.save(concentrator);
            return {
                success: false,
                message: `连接集中器时发生错误: ${error.message}`
            };
        }
    }
};
exports.LedConcentratorService = LedConcentratorService;
exports.LedConcentratorService = LedConcentratorService = LedConcentratorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(led_concentrator_entity_1.LedConcentrator)),
    __param(1, (0, typeorm_1.InjectRepository)(concentrator_led_mapping_entity_1.ConcentratorLedMapping)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], LedConcentratorService);
//# sourceMappingURL=led-concentrator.service.js.map