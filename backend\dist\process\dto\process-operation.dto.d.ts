export declare class CreateProcessOperationDto {
    code: string;
    name: string;
    description?: string;
    standardTime?: number;
    type?: string;
    isActive?: number;
}
export declare class UpdateProcessOperationDto {
    code?: string;
    name?: string;
    description?: string;
    standardTime?: number;
    type?: string;
    isActive?: number;
}
export declare class ProcessOperationResponseDto {
    id: number;
    code: string;
    name: string;
    description: string;
    standardTime: number;
    type: string;
    isActive: number;
    createdBy: string;
    creationDate: Date;
    lastUpdatedBy: string;
    lastUpdateDate: Date;
}
