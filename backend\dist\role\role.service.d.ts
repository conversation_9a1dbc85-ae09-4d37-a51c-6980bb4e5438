import { DataSource } from 'typeorm';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { AssignMenuDto } from './dto/assign-menu.dto';
import { Role } from './entities/role.entity';
export declare class RoleService {
    private dataSource;
    constructor(dataSource: DataSource);
    create(createRoleDto: CreateRoleDto): Promise<Role>;
    findAll(params?: any): Promise<{
        items: Role[];
        meta: any;
    }>;
    findOne(id: number): Promise<Role>;
    update(id: number, updateRoleDto: UpdateRoleDto): Promise<Role>;
    remove(id: number): Promise<void>;
    assignMenus(roleId: number, assignMenuDto: AssignMenuDto): Promise<void>;
    getRoleMenus(roleId: number): Promise<any[]>;
    getRoleTypes(): Promise<any[]>;
}
