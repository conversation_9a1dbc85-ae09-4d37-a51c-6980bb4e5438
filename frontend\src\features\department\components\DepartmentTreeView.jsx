import React from 'react';
import { Tree, TreeNode } from 'react-organizational-chart';
import {
  BuildingOfficeIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
  UserIcon
} from '@heroicons/react/24/outline';

const DepartmentTreeView = ({ data, loading, onEdit, onDelete, onAddChild }) => {

  // 渲染部门节点 - 优化颜色对比度
  const renderDepartmentNode = (department) => {
    const isActive = department.IS_ACTIVE === 1;

    return (
      <div
        key={department.DEPT_ID}
        className={`backdrop-blur-sm border rounded-xl p-4 min-w-[220px] max-w-[260px] shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] ${
          isActive
            ? 'bg-white/95 border-blue-300 shadow-blue-500/20'
            : 'bg-gray-100/95 border-gray-300 shadow-gray-500/20'
        }`}
      >
        {/* 卡片头部 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center shadow-md ${
              isActive
                ? 'bg-gradient-to-br from-blue-500 to-blue-600'
                : 'bg-gradient-to-br from-gray-500 to-gray-600'
            }`}>
              <BuildingOfficeIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className={`font-semibold ${isActive ? 'text-gray-800' : 'text-gray-700'}`}>
                {department.DEPT_NAME}
              </div>
              <div className={`text-sm ${isActive ? 'text-gray-600' : 'text-gray-500'}`}>
                编码: {department.DEPT_CODE}
              </div>
            </div>
          </div>

          {/* 状态徽章 */}
          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
            isActive
              ? 'bg-green-100 text-green-700 border border-green-300'
              : 'bg-red-100 text-red-700 border border-red-300'
          }`}>
            {isActive ? '启用' : '禁用'}
          </div>
        </div>

        {/* 卡片内容 */}
        <div className="space-y-3 mb-4">
          <div className="grid grid-cols-2 gap-4">
            <div className={`rounded-lg p-3 ${isActive ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 border border-gray-200'}`}>
              <div className={`text-xs mb-1 ${isActive ? 'text-blue-600' : 'text-gray-600'}`}>部门层级</div>
              <div className={`text-sm font-medium ${isActive ? 'text-purple-700' : 'text-gray-700'}`}>
                第{department.DEPT_LEVEL || 1}级
              </div>
            </div>
            <div className={`rounded-lg p-3 ${isActive ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 border border-gray-200'}`}>
              <div className={`text-xs mb-1 ${isActive ? 'text-blue-600' : 'text-gray-600'}`}>排序</div>
              <div className={`text-sm font-medium ${isActive ? 'text-blue-700' : 'text-gray-700'}`}>
                {department.SORT_ORDER || 0}
              </div>
            </div>
          </div>

          {department.MANAGER_USERNAME && (
            <div className={`rounded-lg p-3 ${isActive ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 border border-gray-200'}`}>
              <div className={`text-xs mb-1 ${isActive ? 'text-blue-600' : 'text-gray-600'}`}>负责人</div>
              <div className={`text-sm font-medium flex items-center ${isActive ? 'text-gray-800' : 'text-gray-700'}`}>
                <UserIcon className="h-4 w-4 mr-1" />
                {department.MANAGER_USERNAME}
              </div>
            </div>
          )}

          {department.DESCRIPTION && (
            <div className={`rounded-lg p-3 ${isActive ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 border border-gray-200'}`}>
              <div className={`text-xs mb-1 ${isActive ? 'text-blue-600' : 'text-gray-600'}`}>描述</div>
              <div className={`text-sm truncate ${isActive ? 'text-gray-700' : 'text-gray-600'}`} title={department.DESCRIPTION}>
                {department.DESCRIPTION.length > 40
                  ? `${department.DESCRIPTION.substring(0, 40)}...`
                  : department.DESCRIPTION
                }
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className={`flex items-center justify-end space-x-2 pt-3 border-t ${isActive ? 'border-blue-200' : 'border-gray-200'}`}>
          <button
            onClick={() => onAddChild(department)}
            className="p-2 text-green-600 hover:text-green-700 hover:bg-green-100 rounded-lg transition-all duration-200"
            title="添加子部门"
          >
            <PlusIcon className="h-4 w-4" />
          </button>

          <button
            onClick={() => onEdit(department)}
            className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-100 rounded-lg transition-all duration-200"
            title="编辑部门"
          >
            <PencilIcon className="h-4 w-4" />
          </button>

          <button
            onClick={() => onDelete(department)}
            className="p-2 text-red-600 hover:text-red-700 hover:bg-red-100 rounded-lg transition-all duration-200"
            title="删除部门"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  };

  // 递归渲染树形结构
  const renderTreeNode = (department) => {
    const hasChildren = department.children && department.children.length > 0;
    
    return (
      <TreeNode 
        key={department.DEPT_ID}
        label={renderDepartmentNode(department)}
      >
        {hasChildren && department.children.map(child => renderTreeNode(child))}
      </TreeNode>
    );
  };

  // 加载状态 - 与计划管理页面样式匹配
  if (loading) {
    return (
      <div className="bg-tech-dark-600/30 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl shadow-2xl overflow-hidden">
        <div className="flex items-center justify-center py-16">
          <div className="text-center">
            <div className="w-16 h-16 rounded-xl bg-tech-blue-gradient flex items-center justify-center shadow-lg mb-4 animate-pulse">
              <BuildingOfficeIcon className="h-8 w-8 text-white" />
            </div>
            <div className="space-y-2">
              <p className="text-lg font-semibold text-tech-blue-400">加载组织架构中</p>
              <p className="text-tech-gray-400">正在构建部门树形结构...</p>
            </div>
            <div className="mt-6 flex justify-center space-x-1">
              <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 空数据状态 - 与计划管理页面样式匹配
  if (!data || data.length === 0) {
    return (
      <div className="bg-tech-dark-600/30 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl shadow-2xl overflow-hidden">
        <div className="flex flex-col items-center justify-center py-16">
          <div className="text-center">
            <div className="w-20 h-20 rounded-xl bg-tech-gray-600 flex items-center justify-center shadow-lg mb-6">
              <BuildingOfficeIcon className="h-10 w-10 text-white" />
            </div>
            <div className="space-y-3">
              <p className="text-xl font-semibold text-tech-gray-300">暂无部门数据</p>
              <p className="text-tech-gray-400 max-w-md">
                还没有创建任何部门，请先添加部门信息来构建组织架构
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* 精美的标题区域 - 与计划管理页面样式匹配 */}
      <div className="mb-6">
        <div className="bg-tech-dark-600/40 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-tech-blue-gradient rounded-xl flex items-center justify-center shadow-md">
              <BuildingOfficeIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="text-xl font-bold text-white">组织架构图</div>
              <div className="text-sm text-tech-gray-400">
                共 {getTotalDepartmentCount(data)} 个部门 • 树形结构展示
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 树形图容器 - 重新优化分支线对齐 */}
      <div className="overflow-x-auto bg-tech-dark-800/20 rounded-xl p-8">
        <div className="min-w-full flex justify-center">
          <div className="tree-container">
            <Tree
              lineWidth="3px"
              lineColor="#3b82f6"
              lineBorderRadius="6px"
              label={<div className="tree-root-spacer"></div>}
            >
              {data.map(department => renderTreeNode(department))}
            </Tree>
          </div>
        </div>
      </div>

      {/* 重新优化的分支线样式 - 精确对齐 */}
      <style jsx>{`
        .tree-container {
          position: relative;
          padding: 20px;
        }

        .tree-root-spacer {
          height: 0px;
          width: 100%;
        }

        /* 重置所有默认样式 */
        :global(.rst__tree),
        :global(.rst__node),
        :global(.rst__nodeContent),
        :global(.rst__children),
        :global(.rst__lineChildren) {
          margin: 0 !important;
          padding: 0 !important;
        }

        /* 树形结构根容器 */
        :global(.rst__tree) {
          display: flex !important;
          justify-content: center !important;
          align-items: flex-start !important;
          width: 100% !important;
        }

        /* 每个节点的容器 */
        :global(.rst__node) {
          position: relative !important;
          display: flex !important;
          flex-direction: column !important;
          align-items: center !important;
          justify-content: flex-start !important;
        }

        /* 节点内容区域 */
        :global(.rst__nodeContent) {
          position: relative !important;
          z-index: 10 !important;
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
        }

        /* 子节点容器 */
        :global(.rst__children) {
          display: flex !important;
          justify-content: center !important;
          align-items: flex-start !important;
          margin-top: 40px !important;
          position: relative !important;
          gap: 30px !important;
        }

        /* 连接线的主容器 */
        :global(.rst__lineChildren) {
          position: absolute !important;
          top: -40px !important;
          left: 0 !important;
          right: 0 !important;
          height: 40px !important;
          pointer-events: none !important;
          z-index: 1 !important;
        }

        /* 从父节点向下的垂直线 */
        :global(.rst__lineHalfVerticalTop) {
          position: absolute !important;
          left: 50% !important;
          top: 0 !important;
          width: 3px !important;
          height: 20px !important;
          background: #3b82f6 !important;
          transform: translateX(-50%) !important;
          border-radius: 1.5px !important;
        }

        /* 连接到子节点的垂直线 */
        :global(.rst__lineHalfVerticalBottom) {
          position: absolute !important;
          left: 50% !important;
          bottom: 0 !important;
          width: 3px !important;
          height: 20px !important;
          background: #3b82f6 !important;
          transform: translateX(-50%) !important;
          border-radius: 1.5px !important;
        }

        /* 完整的垂直连接线 */
        :global(.rst__lineFullVertical) {
          position: absolute !important;
          left: 50% !important;
          top: 0 !important;
          width: 3px !important;
          height: 100% !important;
          background: #3b82f6 !important;
          transform: translateX(-50%) !important;
          border-radius: 1.5px !important;
        }

        /* 水平连接线 - 右半部分 */
        :global(.rst__lineHalfHorizontalRight) {
          position: absolute !important;
          top: 20px !important;
          left: 50% !important;
          right: 0 !important;
          height: 3px !important;
          background: #3b82f6 !important;
          border-radius: 1.5px !important;
        }

        /* 水平连接线 - 左半部分 */
        :global(.rst__lineHalfHorizontalLeft) {
          position: absolute !important;
          top: 20px !important;
          left: 0 !important;
          right: 50% !important;
          height: 3px !important;
          background: #3b82f6 !important;
          border-radius: 1.5px !important;
        }

        /* 完整的水平连接线 */
        :global(.rst__lineFullHorizontal) {
          position: absolute !important;
          top: 20px !important;
          left: 0 !important;
          right: 0 !important;
          height: 3px !important;
          background: #3b82f6 !important;
          border-radius: 1.5px !important;
        }

        /* 隐藏折叠按钮 */
        :global(.rst__collapseButton) {
          display: none !important;
        }

        /* 确保节点卡片居中对齐 */
        :global(.rst__nodeContent > div) {
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          position: relative !important;
        }

        /* 完全重新设计连接线系统 */

        /* 隐藏所有默认连接线 */
        :global(.rst__lineHalfVerticalTop),
        :global(.rst__lineHalfVerticalBottom),
        :global(.rst__lineFullVertical),
        :global(.rst__lineHalfHorizontalRight),
        :global(.rst__lineHalfHorizontalLeft),
        :global(.rst__lineFullHorizontal) {
          display: none !important;
        }

        /* 自定义连接线 - 使用伪元素 */
        :global(.rst__node) {
          position: relative !important;
        }

        /* 为有子节点的节点添加向下的连接线 */
        :global(.rst__node:has(.rst__children) > .rst__nodeContent)::after {
          content: '' !important;
          position: absolute !important;
          bottom: -20px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          width: 3px !important;
          height: 20px !important;
          background: #3b82f6 !important;
          border-radius: 1.5px !important;
          z-index: 5 !important;
        }

        /* 为非根节点添加向上的连接线 */
        :global(.rst__node:not(.rst__tree > .rst__node) > .rst__nodeContent)::before {
          content: '' !important;
          position: absolute !important;
          top: -20px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          width: 3px !important;
          height: 20px !important;
          background: #3b82f6 !important;
          border-radius: 1.5px !important;
          z-index: 5 !important;
        }

        /* 为有兄弟节点的节点添加水平连接线 */
        :global(.rst__children > .rst__node:not(:only-child))::before {
          content: '' !important;
          position: absolute !important;
          top: -20px !important;
          left: 0 !important;
          right: 0 !important;
          height: 3px !important;
          background: #3b82f6 !important;
          border-radius: 1.5px !important;
          z-index: 4 !important;
        }

        /* 第一个子节点的水平线只显示右半部分 */
        :global(.rst__children > .rst__node:first-child:not(:only-child))::before {
          left: 50% !important;
        }

        /* 最后一个子节点的水平线只显示左半部分 */
        :global(.rst__children > .rst__node:last-child:not(:only-child))::before {
          right: 50% !important;
        }

        /* 中间子节点的水平线显示完整 */
        :global(.rst__children > .rst__node:not(:first-child):not(:last-child))::before {
          left: 0 !important;
          right: 0 !important;
        }
      `}</style>


    </div>
  );

  // 计算总部门数量
  function getTotalDepartmentCount(departments) {
    let count = 0;
    
    function countRecursive(depts) {
      depts.forEach(dept => {
        count++;
        if (dept.children && dept.children.length > 0) {
          countRecursive(dept.children);
        }
      });
    }
    
    countRecursive(departments);
    return count;
  }
};

export default DepartmentTreeView;
