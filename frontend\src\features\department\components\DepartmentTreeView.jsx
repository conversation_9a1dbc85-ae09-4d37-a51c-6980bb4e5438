import React from 'react';
import { Tree, TreeNode } from 'react-organizational-chart';
import {
  BuildingOfficeIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
  UserIcon
} from '@heroicons/react/24/outline';

const DepartmentTreeView = ({ data, loading, onEdit, onDelete, onAddChild }) => {

  // 渲染部门节点 - 与计划管理页面样式匹配
  const renderDepartmentNode = (department) => {
    const isActive = department.IS_ACTIVE === 1;

    return (
      <div
        key={department.DEPT_ID}
        className="bg-tech-dark-700/50 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-4 min-w-[220px] max-w-[260px] shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"
      >
        {/* 卡片头部 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center shadow-md ${
              isActive
                ? 'bg-tech-blue-gradient'
                : 'bg-tech-gray-600'
            }`}>
              <BuildingOfficeIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="font-semibold text-tech-gray-200">{department.DEPT_NAME}</div>
              <div className="text-sm text-tech-gray-400">编码: {department.DEPT_CODE}</div>
            </div>
          </div>

          {/* 状态徽章 */}
          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
            isActive
              ? 'bg-tech-green-900/30 text-tech-green-300 border border-tech-green-500/30'
              : 'bg-tech-red-900/30 text-tech-red-300 border border-tech-red-500/30'
          }`}>
            {isActive ? '启用' : '禁用'}
          </div>
        </div>

        {/* 卡片内容 */}
        <div className="space-y-3 mb-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-tech-dark-800/30 rounded-lg p-3">
              <div className="text-xs text-tech-gray-400 mb-1">部门层级</div>
              <div className="text-sm font-medium text-tech-purple-300">
                第{department.DEPT_LEVEL || 1}级
              </div>
            </div>
            <div className="bg-tech-dark-800/30 rounded-lg p-3">
              <div className="text-xs text-tech-gray-400 mb-1">排序</div>
              <div className="text-sm font-medium text-tech-blue-300">
                {department.SORT_ORDER || 0}
              </div>
            </div>
          </div>

          {department.MANAGER_USERNAME && (
            <div className="bg-tech-dark-800/30 rounded-lg p-3">
              <div className="text-xs text-tech-gray-400 mb-1">负责人</div>
              <div className="text-sm font-medium text-tech-gray-200 flex items-center">
                <UserIcon className="h-4 w-4 mr-1" />
                {department.MANAGER_USERNAME}
              </div>
            </div>
          )}

          {department.DESCRIPTION && (
            <div className="bg-tech-dark-800/30 rounded-lg p-3">
              <div className="text-xs text-tech-gray-400 mb-1">描述</div>
              <div className="text-sm text-tech-gray-300 truncate" title={department.DESCRIPTION}>
                {department.DESCRIPTION.length > 40
                  ? `${department.DESCRIPTION.substring(0, 40)}...`
                  : department.DESCRIPTION
                }
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-2 pt-3 border-t border-tech-dark-500/50">
          <button
            onClick={() => onAddChild(department)}
            className="p-2 text-tech-green-400 hover:text-tech-green-300 hover:bg-tech-green-900/30 rounded-lg transition-all duration-200"
            title="添加子部门"
          >
            <PlusIcon className="h-4 w-4" />
          </button>

          <button
            onClick={() => onEdit(department)}
            className="p-2 text-tech-blue-400 hover:text-tech-blue-300 hover:bg-tech-blue-900/30 rounded-lg transition-all duration-200"
            title="编辑部门"
          >
            <PencilIcon className="h-4 w-4" />
          </button>

          <button
            onClick={() => onDelete(department)}
            className="p-2 text-tech-red-400 hover:text-tech-red-300 hover:bg-tech-red-900/30 rounded-lg transition-all duration-200"
            title="删除部门"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  };

  // 递归渲染树形结构
  const renderTreeNode = (department) => {
    const hasChildren = department.children && department.children.length > 0;
    
    return (
      <TreeNode 
        key={department.DEPT_ID}
        label={renderDepartmentNode(department)}
      >
        {hasChildren && department.children.map(child => renderTreeNode(child))}
      </TreeNode>
    );
  };

  // 加载状态 - 与计划管理页面样式匹配
  if (loading) {
    return (
      <div className="bg-tech-dark-600/30 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl shadow-2xl overflow-hidden">
        <div className="flex items-center justify-center py-16">
          <div className="text-center">
            <div className="w-16 h-16 rounded-xl bg-tech-blue-gradient flex items-center justify-center shadow-lg mb-4 animate-pulse">
              <BuildingOfficeIcon className="h-8 w-8 text-white" />
            </div>
            <div className="space-y-2">
              <p className="text-lg font-semibold text-tech-blue-400">加载组织架构中</p>
              <p className="text-tech-gray-400">正在构建部门树形结构...</p>
            </div>
            <div className="mt-6 flex justify-center space-x-1">
              <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 空数据状态 - 与计划管理页面样式匹配
  if (!data || data.length === 0) {
    return (
      <div className="bg-tech-dark-600/30 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl shadow-2xl overflow-hidden">
        <div className="flex flex-col items-center justify-center py-16">
          <div className="text-center">
            <div className="w-20 h-20 rounded-xl bg-tech-gray-600 flex items-center justify-center shadow-lg mb-6">
              <BuildingOfficeIcon className="h-10 w-10 text-white" />
            </div>
            <div className="space-y-3">
              <p className="text-xl font-semibold text-tech-gray-300">暂无部门数据</p>
              <p className="text-tech-gray-400 max-w-md">
                还没有创建任何部门，请先添加部门信息来构建组织架构
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* 精美的标题区域 - 与计划管理页面样式匹配 */}
      <div className="mb-6">
        <div className="bg-tech-dark-600/40 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-tech-blue-gradient rounded-xl flex items-center justify-center shadow-md">
              <BuildingOfficeIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="text-xl font-bold text-white">组织架构图</div>
              <div className="text-sm text-tech-gray-400">
                共 {getTotalDepartmentCount(data)} 个部门 • 树形结构展示
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 树形图容器 - 修复对齐问题 */}
      <div className="overflow-x-auto">
        <div className="min-w-full flex justify-center py-6">
          <div className="inline-block">
            <div style={{
              '--tree-line-width': '2px',
              '--tree-line-color': 'rgba(59, 130, 246, 0.6)',
              '--tree-line-border-radius': '8px'
            }}>
              <Tree
                lineWidth="2px"
                lineColor="rgba(59, 130, 246, 0.6)"
                lineBorderRadius="8px"
                label={<div style={{ height: '20px' }}></div>}
              >
                {data.map(department => renderTreeNode(department))}
              </Tree>
            </div>
          </div>
        </div>
      </div>

      {/* 添加自定义样式来修复对齐问题 */}
      <style jsx>{`
        :global(.rst__tree) {
          display: flex !important;
          justify-content: center !important;
        }

        :global(.rst__node) {
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
        }

        :global(.rst__lineChildren) {
          display: flex !important;
          justify-content: center !important;
          align-items: flex-start !important;
        }

        :global(.rst__lineHalfHorizontalRight) {
          border-bottom: 2px solid rgba(59, 130, 246, 0.6) !important;
          border-radius: 0 0 8px 0 !important;
        }

        :global(.rst__lineHalfVerticalTop) {
          border-left: 2px solid rgba(59, 130, 246, 0.6) !important;
        }

        :global(.rst__lineHalfVerticalBottom) {
          border-left: 2px solid rgba(59, 130, 246, 0.6) !important;
        }

        :global(.rst__lineFullVertical) {
          border-left: 2px solid rgba(59, 130, 246, 0.6) !important;
        }

        :global(.rst__collapseButton) {
          display: none !important;
        }
      `}</style>

      {/* 精美的操作说明 - 与计划管理页面样式匹配 */}
      <div className="mt-8 pt-6 border-t border-tech-dark-600">
        <div className="bg-tech-dark-600/40 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl p-6 shadow-lg">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-tech-purple-gradient rounded-lg flex items-center justify-center">
              <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-tech-gray-200">操作指南</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3 p-4 bg-tech-dark-800/30 rounded-xl">
              <div className="w-10 h-10 bg-tech-green-gradient rounded-lg flex items-center justify-center shadow-lg">
                <PlusIcon className="h-5 w-5 text-white" />
              </div>
              <div>
                <div className="font-medium text-tech-gray-200">添加子部门</div>
                <div className="text-xs text-tech-gray-400">在当前部门下创建子部门</div>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-4 bg-tech-dark-800/30 rounded-xl">
              <div className="w-10 h-10 bg-tech-blue-gradient rounded-lg flex items-center justify-center shadow-lg">
                <PencilIcon className="h-5 w-5 text-white" />
              </div>
              <div>
                <div className="font-medium text-tech-gray-200">编辑部门</div>
                <div className="text-xs text-tech-gray-400">修改部门信息和配置</div>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-4 bg-tech-dark-800/30 rounded-xl">
              <div className="w-10 h-10 bg-tech-red-gradient rounded-lg flex items-center justify-center shadow-lg">
                <TrashIcon className="h-5 w-5 text-white" />
              </div>
              <div>
                <div className="font-medium text-tech-gray-200">删除部门</div>
                <div className="text-xs text-tech-gray-400">移除部门及其关联数据</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 计算总部门数量
  function getTotalDepartmentCount(departments) {
    let count = 0;
    
    function countRecursive(depts) {
      depts.forEach(dept => {
        count++;
        if (dept.children && dept.children.length > 0) {
          countRecursive(dept.children);
        }
      });
    }
    
    countRecursive(departments);
    return count;
  }
};

export default DepartmentTreeView;
