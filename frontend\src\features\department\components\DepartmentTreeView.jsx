import React from 'react';
import { Tree, TreeNode } from 'react-organizational-chart';
import {
  BuildingOfficeIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
  UserIcon
} from '@heroicons/react/24/outline';

const DepartmentTreeView = ({ data, loading, onEdit, onDelete, onAddChild }) => {
  const [expandedNodes, setExpandedNodes] = React.useState(new Set());

  // 渲染部门节点 - 简洁美观的设计
  const renderDepartmentNode = (department) => {
    const isActive = department.IS_ACTIVE === 1;

    return (
      <div
        key={department.DEPT_ID}
        className={`relative border rounded-xl p-5 min-w-[280px] max-w-[320px] shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] ${
          isActive
            ? 'bg-white border-blue-200 shadow-blue-100'
            : 'bg-gray-50 border-gray-200 shadow-gray-100'
        }`}
      >
        {/* 顶部状态条 */}
        <div className={`absolute top-0 left-0 right-0 h-1 rounded-t-xl ${
          isActive
            ? 'bg-gradient-to-r from-blue-500 to-purple-500'
            : 'bg-gradient-to-r from-gray-400 to-gray-500'
        }`} />

        {/* 卡片头部 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            {/* 图标容器 */}
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center shadow-md ${
              isActive
                ? 'bg-gradient-to-br from-blue-500 to-blue-600'
                : 'bg-gradient-to-br from-gray-500 to-gray-600'
            }`}>
              <BuildingOfficeIcon className="w-6 h-6 text-white" />
            </div>

            {/* 部门信息 */}
            <div className="flex-1">
              <div className={`font-bold text-lg ${
                isActive ? 'text-gray-800' : 'text-gray-600'
              }`}>
                {department.DEPT_NAME}
              </div>
              <div className={`text-sm ${
                isActive ? 'text-blue-600' : 'text-gray-500'
              }`}>
                {department.DEPT_CODE}
              </div>
            </div>
          </div>

          {/* 状态徽章 */}
          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
            isActive
              ? 'bg-green-100 text-green-700 border border-green-200'
              : 'bg-red-100 text-red-700 border border-red-200'
          }`}>
            {isActive ? '启用' : '禁用'}
          </div>
        </div>

        {/* 卡片内容 */}
        <div className="space-y-3 mb-4">
          {/* 统计信息 */}
          <div className="grid grid-cols-2 gap-3">
            <div className={`rounded-lg p-3 ${
              isActive
                ? 'bg-blue-50 border border-blue-100'
                : 'bg-gray-100 border border-gray-200'
            }`}>
              <div className={`text-xs mb-1 ${
                isActive ? 'text-blue-600' : 'text-gray-600'
              }`}>
                层级
              </div>
              <div className={`text-sm font-bold ${
                isActive ? 'text-blue-800' : 'text-gray-700'
              }`}>
                第 {department.DEPT_LEVEL || 1} 级
              </div>
            </div>

            <div className={`rounded-lg p-3 ${
              isActive
                ? 'bg-purple-50 border border-purple-100'
                : 'bg-gray-100 border border-gray-200'
            }`}>
              <div className={`text-xs mb-1 ${
                isActive ? 'text-purple-600' : 'text-gray-600'
              }`}>
                排序
              </div>
              <div className={`text-sm font-bold ${
                isActive ? 'text-purple-800' : 'text-gray-700'
              }`}>
                {department.SORT_ORDER || 0}
              </div>
            </div>
          </div>

          {/* 负责人信息 */}
          {department.MANAGER_USERNAME && (
            <div className={`rounded-lg p-3 ${
              isActive
                ? 'bg-emerald-50 border border-emerald-100'
                : 'bg-gray-100 border border-gray-200'
            }`}>
              <div className={`text-xs mb-1 ${
                isActive ? 'text-emerald-600' : 'text-gray-600'
              }`}>
                负责人
              </div>
              <div className={`text-sm font-bold flex items-center ${
                isActive ? 'text-emerald-800' : 'text-gray-700'
              }`}>
                <UserIcon className="h-4 w-4 mr-1" />
                {department.MANAGER_USERNAME}
              </div>
            </div>
          )}

          {/* 描述信息 */}
          {department.DESCRIPTION && (
            <div className={`rounded-lg p-3 ${
              isActive
                ? 'bg-amber-50 border border-amber-100'
                : 'bg-gray-100 border border-gray-200'
            }`}>
              <div className={`text-xs mb-1 ${
                isActive ? 'text-amber-600' : 'text-gray-600'
              }`}>
                描述
              </div>
              <div
                className={`text-sm ${
                  isActive ? 'text-amber-800' : 'text-gray-700'
                }`}
                title={department.DESCRIPTION}
              >
                {department.DESCRIPTION.length > 30
                  ? `${department.DESCRIPTION.substring(0, 30)}...`
                  : department.DESCRIPTION
                }
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮组 */}
        <div className={`flex items-center justify-end space-x-2 pt-3 border-t ${
          isActive ? 'border-blue-100' : 'border-gray-200'
        }`}>
          <button
            onClick={() => onAddChild(department)}
            className="p-2 text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50 rounded-lg transition-all duration-200"
            title="添加子部门"
          >
            <PlusIcon className="h-4 w-4" />
          </button>

          <button
            onClick={() => onEdit(department)}
            className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200"
            title="编辑部门"
          >
            <PencilIcon className="h-4 w-4" />
          </button>

          <button
            onClick={() => onDelete(department)}
            className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200"
            title="删除部门"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  };

  // 递归渲染树形结构
  const renderTreeNode = (department) => {
    const hasChildren = department.children && department.children.length > 0;

    return (
      <TreeNode
        key={department.DEPT_ID}
        label={renderDepartmentNode(department)}
      >
        {hasChildren && department.children.map(child => renderTreeNode(child))}
      </TreeNode>
    );
  };

  // 加载状态 - 与计划管理页面样式匹配
  if (loading) {
    return (
      <div className="bg-tech-dark-600/30 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl shadow-2xl overflow-hidden">
        <div className="flex items-center justify-center py-16">
          <div className="text-center">
            <div className="w-16 h-16 rounded-xl bg-tech-blue-gradient flex items-center justify-center shadow-lg mb-4 animate-pulse">
              <BuildingOfficeIcon className="h-8 w-8 text-white" />
            </div>
            <div className="space-y-2">
              <p className="text-lg font-semibold text-tech-blue-400">加载组织架构中</p>
              <p className="text-tech-gray-400">正在构建部门树形结构...</p>
            </div>
            <div className="mt-6 flex justify-center space-x-1">
              <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 空数据状态 - 与计划管理页面样式匹配
  if (!data || data.length === 0) {
    return (
      <div className="bg-tech-dark-600/30 backdrop-blur-sm border border-tech-dark-500/50 rounded-xl shadow-2xl overflow-hidden">
        <div className="flex flex-col items-center justify-center py-16">
          <div className="text-center">
            <div className="w-20 h-20 rounded-xl bg-tech-gray-600 flex items-center justify-center shadow-lg mb-6">
              <BuildingOfficeIcon className="h-10 w-10 text-white" />
            </div>
            <div className="space-y-3">
              <p className="text-xl font-semibold text-tech-gray-300">暂无部门数据</p>
              <p className="text-tech-gray-400 max-w-md">
                还没有创建任何部门，请先添加部门信息来构建组织架构
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* 精美的标题区域 */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-blue-600/20 backdrop-blur-sm border border-blue-500/30 rounded-2xl p-6 shadow-2xl hover:shadow-3xl transition-all duration-500">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative w-14 h-14 bg-gradient-to-br from-blue-500 via-purple-600 to-blue-700 rounded-2xl flex items-center justify-center shadow-lg">
                <BuildingOfficeIcon className="w-7 h-7 text-white" />
                <div className="absolute inset-0 bg-blue-400/30 rounded-2xl blur-sm" />
              </div>
              <div>
                <div className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  组织架构图
                </div>
                <div className="text-sm text-blue-300/80 font-medium">
                  共 {getTotalDepartmentCount(data)} 个部门 • 可展开/折叠的树形结构
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 树形图容器 - 使用 react-organizational-chart */}
      <div className="overflow-x-auto bg-gradient-to-br from-gray-50/50 to-blue-50/50 backdrop-blur-sm border border-blue-200/30 rounded-2xl p-8 shadow-xl">
        <div className="min-w-full flex justify-center">
          <div className="tree-container">
            <Tree
              lineWidth="2px"
              lineColor="#3b82f6"
              lineBorderRadius="4px"
              label={<div className="tree-root-spacer"></div>}
            >
              {data.map(department => renderTreeNode(department))}
            </Tree>
          </div>
        </div>
      </div>

      {/* 精美的树形样式 - 优化连接线对齐 */}
      <style jsx>{`
        .tree-container {
          position: relative;
          padding: 20px;
        }

        .tree-root-spacer {
          height: 0px;
          width: 100%;
        }

        /* 重置所有默认样式 */
        :global(.rst__tree),
        :global(.rst__node),
        :global(.rst__nodeContent),
        :global(.rst__children),
        :global(.rst__lineChildren) {
          margin: 0 !important;
          padding: 0 !important;
        }

        /* 树形结构根容器 */
        :global(.rst__tree) {
          display: flex !important;
          justify-content: center !important;
          align-items: flex-start !important;
          width: 100% !important;
        }

        /* 每个节点的容器 */
        :global(.rst__node) {
          position: relative !important;
          display: flex !important;
          flex-direction: column !important;
          align-items: center !important;
          justify-content: flex-start !important;
        }

        /* 节点内容区域 - 确保居中对齐 */
        :global(.rst__nodeContent) {
          position: relative !important;
          z-index: 10 !important;
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          width: 100% !important;
        }

        /* 子节点容器 */
        :global(.rst__children) {
          display: flex !important;
          justify-content: center !important;
          align-items: flex-start !important;
          margin-top: 40px !important;
          position: relative !important;
          gap: 30px !important;
        }

        /* 连接线的主容器 */
        :global(.rst__lineChildren) {
          position: absolute !important;
          top: -40px !important;
          left: 0 !important;
          right: 0 !important;
          height: 40px !important;
          pointer-events: none !important;
          z-index: 1 !important;
        }

        /* 优化的连接线样式 - 精确对齐 */
        :global(.rst__lineHalfVerticalTop) {
          position: absolute !important;
          left: 50% !important;
          top: 0 !important;
          width: 2px !important;
          height: 20px !important;
          background: #3b82f6 !important;
          transform: translateX(-50%) !important;
          border-radius: 1px !important;
        }

        :global(.rst__lineHalfVerticalBottom) {
          position: absolute !important;
          left: 50% !important;
          bottom: 0 !important;
          width: 2px !important;
          height: 20px !important;
          background: #3b82f6 !important;
          transform: translateX(-50%) !important;
          border-radius: 1px !important;
        }

        :global(.rst__lineFullVertical) {
          position: absolute !important;
          left: 50% !important;
          top: 0 !important;
          width: 2px !important;
          height: 100% !important;
          background: #3b82f6 !important;
          transform: translateX(-50%) !important;
          border-radius: 1px !important;
        }

        :global(.rst__lineHalfHorizontalRight) {
          position: absolute !important;
          top: 20px !important;
          left: 50% !important;
          right: 0 !important;
          height: 2px !important;
          background: #3b82f6 !important;
          border-radius: 1px !important;
        }

        :global(.rst__lineHalfHorizontalLeft) {
          position: absolute !important;
          top: 20px !important;
          left: 0 !important;
          right: 50% !important;
          height: 2px !important;
          background: #3b82f6 !important;
          border-radius: 1px !important;
        }

        :global(.rst__lineFullHorizontal) {
          position: absolute !important;
          top: 20px !important;
          left: 0 !important;
          right: 0 !important;
          height: 2px !important;
          background: #3b82f6 !important;
          border-radius: 1px !important;
        }

        /* 隐藏折叠按钮 */
        :global(.rst__collapseButton) {
          display: none !important;
        }

        /* 确保节点卡片居中对齐 */
        :global(.rst__nodeContent > div) {
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          position: relative !important;
          margin: 0 auto !important;
        }

        /* 美化滚动条 */
        ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        ::-webkit-scrollbar-track {
          background: rgba(148, 163, 184, 0.1);
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
          background: linear-gradient(45deg, #3b82f6, #8b5cf6);
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(45deg, #2563eb, #7c3aed);
        }

        /* 连接线悬停效果 */
        :global(.rst__lineHalfVerticalTop):hover,
        :global(.rst__lineHalfVerticalBottom):hover,
        :global(.rst__lineFullVertical):hover,
        :global(.rst__lineHalfHorizontalRight):hover,
        :global(.rst__lineHalfHorizontalLeft):hover,
        :global(.rst__lineFullHorizontal):hover {
          background: #2563eb !important;
          box-shadow: 0 0 4px rgba(59, 130, 246, 0.5) !important;
        }
      `}</style>


    </div>
  );

  // 计算总部门数量
  function getTotalDepartmentCount(departments) {
    let count = 0;
    
    function countRecursive(depts) {
      depts.forEach(dept => {
        count++;
        if (dept.children && dept.children.length > 0) {
          countRecursive(dept.children);
        }
      });
    }
    
    countRecursive(departments);
    return count;
  }
};

export default DepartmentTreeView;
