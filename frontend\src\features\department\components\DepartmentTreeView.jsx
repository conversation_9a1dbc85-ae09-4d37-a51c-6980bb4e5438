import React from 'react';
import { Tree, TreeNode } from 'react-organizational-chart';
import {
  BuildingOfficeIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
  UserIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

const DepartmentTreeView = ({ data, loading, onEdit, onDelete, onAddChild }) => {

  // 渲染部门节点
  const renderDepartmentNode = (department) => {
    const isActive = department.IS_ACTIVE === 1;

    return (
      <div
        key={department.DEPT_ID}
        className={`group relative bg-gradient-to-br from-tech-dark-700/90 to-tech-dark-800/90 backdrop-blur-sm border rounded-2xl p-6 min-w-[280px] max-w-[320px] shadow-2xl transition-all duration-500 hover:scale-105 hover:shadow-3xl ${
          isActive
            ? 'border-tech-blue-500/40 shadow-tech-blue-500/20'
            : 'border-tech-gray-600/30 shadow-tech-gray-500/10'
        }`}
      >
        {/* 背景装饰 */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-tech-blue-500/5 to-tech-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        {/* 顶部装饰线 */}
        <div className={`absolute top-0 left-1/2 transform -translate-x-1/2 w-16 h-1 rounded-full ${
          isActive ? 'bg-tech-blue-gradient' : 'bg-tech-gray-gradient'
        }`}></div>

        {/* 部门图标和基本信息 */}
        <div className="relative z-10 text-center mb-4">
          <div className="flex items-center justify-center mb-3">
            <div className={`w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg transition-all duration-300 group-hover:scale-110 ${
              isActive
                ? 'bg-tech-blue-gradient shadow-tech-blue-500/30'
                : 'bg-tech-gray-gradient shadow-tech-gray-500/30'
            }`}>
              <BuildingOfficeIcon className="h-8 w-8 text-white" />
            </div>
          </div>

          <div className="space-y-2">
            <h3 className={`font-bold text-lg leading-tight ${
              isActive ? 'text-tech-gray-100' : 'text-tech-gray-400'
            }`}>
              {department.DEPT_NAME}
            </h3>

            <div className="flex items-center justify-center">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                isActive
                  ? 'bg-tech-blue-900/40 text-tech-blue-300 border border-tech-blue-500/40'
                  : 'bg-tech-gray-900/40 text-tech-gray-400 border border-tech-gray-500/40'
              }`}>
                {department.DEPT_CODE}
              </span>
            </div>

            {department.MANAGER_USERNAME && (
              <div className="flex items-center justify-center space-x-1 text-xs text-tech-gray-400">
                <UserIcon className="h-4 w-4" />
                <span>{department.MANAGER_USERNAME}</span>
              </div>
            )}
          </div>
        </div>

        {/* 部门详细信息 */}
        <div className="relative z-10 space-y-3 mb-4">
          {/* 层级信息 */}
          <div className="flex items-center justify-between text-xs">
            <span className="text-tech-gray-400">层级</span>
            <span className={`font-medium ${isActive ? 'text-tech-purple-300' : 'text-tech-gray-500'}`}>
              第 {department.DEPT_LEVEL || 1} 级
            </span>
          </div>

          {/* 排序信息 */}
          <div className="flex items-center justify-between text-xs">
            <span className="text-tech-gray-400">排序</span>
            <span className={`font-medium ${isActive ? 'text-tech-green-300' : 'text-tech-gray-500'}`}>
              {department.SORT_ORDER || 0}
            </span>
          </div>
        </div>

        {/* 状态标识 */}
        <div className="relative z-10 flex justify-center mb-4">
          <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium shadow-lg ${
            isActive
              ? 'bg-tech-green-gradient text-white shadow-tech-green-500/30'
              : 'bg-tech-red-gradient text-white shadow-tech-red-500/30'
          }`}>
            <span className={`w-2 h-2 rounded-full mr-2 animate-pulse ${
              isActive ? 'bg-white' : 'bg-white/80'
            }`}></span>
            {isActive ? '启用中' : '已禁用'}
          </span>
        </div>

        {/* 描述信息 */}
        {department.DESCRIPTION && (
          <div className="relative z-10 mb-4">
            <div className="bg-tech-dark-800/50 rounded-xl p-3 border border-tech-dark-600/50">
              <div className="text-xs text-tech-gray-400 mb-1 flex items-center">
                <SparklesIcon className="h-3 w-3 mr-1" />
                描述
              </div>
              <div className="text-sm text-tech-gray-300 leading-relaxed" title={department.DESCRIPTION}>
                {department.DESCRIPTION.length > 50
                  ? `${department.DESCRIPTION.substring(0, 50)}...`
                  : department.DESCRIPTION
                }
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="relative z-10 flex justify-center space-x-2">
          <button
            onClick={() => onAddChild(department)}
            className="group/btn flex items-center justify-center w-10 h-10 bg-tech-green-gradient hover:shadow-lg hover:shadow-tech-green-500/30 text-white rounded-xl transition-all duration-300 transform hover:scale-110 hover:-translate-y-1"
            title="添加子部门"
          >
            <PlusIcon className="h-4 w-4 group-hover/btn:scale-125 transition-transform duration-200" />
          </button>

          <button
            onClick={() => onEdit(department)}
            className="group/btn flex items-center justify-center w-10 h-10 bg-tech-blue-gradient hover:shadow-lg hover:shadow-tech-blue-500/30 text-white rounded-xl transition-all duration-300 transform hover:scale-110 hover:-translate-y-1"
            title="编辑部门"
          >
            <PencilIcon className="h-4 w-4 group-hover/btn:scale-125 transition-transform duration-200" />
          </button>

          <button
            onClick={() => onDelete(department)}
            className="group/btn flex items-center justify-center w-10 h-10 bg-tech-red-gradient hover:shadow-lg hover:shadow-tech-red-500/30 text-white rounded-xl transition-all duration-300 transform hover:scale-110 hover:-translate-y-1"
            title="删除部门"
          >
            <TrashIcon className="h-4 w-4 group-hover/btn:scale-125 transition-transform duration-200" />
          </button>
        </div>

        {/* 悬浮效果装饰 */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-tech-blue-500/10 via-tech-purple-500/10 to-tech-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
      </div>
    );
  };

  // 递归渲染树形结构
  const renderTreeNode = (department) => {
    const hasChildren = department.children && department.children.length > 0;
    
    return (
      <TreeNode 
        key={department.DEPT_ID}
        label={renderDepartmentNode(department)}
      >
        {hasChildren && department.children.map(child => renderTreeNode(child))}
      </TreeNode>
    );
  };

  // 加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-center">
          <div className="relative mb-8">
            <div className="w-20 h-20 rounded-3xl bg-tech-blue-gradient flex items-center justify-center shadow-2xl shadow-tech-blue-500/30 animate-pulse">
              <BuildingOfficeIcon className="h-10 w-10 text-white" />
            </div>
            <div className="absolute inset-0 rounded-3xl bg-tech-blue-gradient opacity-30 animate-ping"></div>
          </div>
          <div className="space-y-2">
            <p className="text-xl font-semibold text-tech-blue-400">加载组织架构中</p>
            <p className="text-tech-gray-400">正在构建部门树形结构...</p>
          </div>
          <div className="mt-6 flex justify-center space-x-1">
            <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-2 h-2 bg-tech-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
          </div>
        </div>
      </div>
    );
  }

  // 空数据状态
  if (!data || data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <div className="text-center">
          <div className="relative mb-8">
            <div className="w-24 h-24 rounded-3xl bg-tech-gray-gradient flex items-center justify-center shadow-2xl shadow-tech-gray-500/20">
              <BuildingOfficeIcon className="h-12 w-12 text-white" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-tech-yellow-gradient rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          <div className="space-y-3">
            <p className="text-xl font-semibold text-tech-gray-300">暂无部门数据</p>
            <p className="text-tech-gray-400 max-w-md">
              还没有创建任何部门，请先添加部门信息来构建组织架构
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* 精美的标题区域 */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center space-x-3 bg-tech-dark-700/50 backdrop-blur-sm border border-tech-dark-500/50 rounded-2xl px-6 py-4 shadow-xl">
          <div className="w-12 h-12 bg-tech-blue-gradient rounded-xl flex items-center justify-center shadow-lg">
            <BuildingOfficeIcon className="h-6 w-6 text-white" />
          </div>
          <div className="text-left">
            <h2 className="text-xl font-bold text-gradient-blue">组织架构图</h2>
            <p className="text-sm text-tech-gray-400">
              共 {getTotalDepartmentCount(data)} 个部门 • 树形结构展示
            </p>
          </div>
        </div>
      </div>

      {/* 树形图容器 */}
      <div className="overflow-x-auto">
        <div className="min-w-full pb-8">
          <Tree
            lineWidth="3px"
            lineColor="rgba(59, 130, 246, 0.3)"
            lineBorderRadius="12px"
            label={<div className="mb-8"></div>}
          >
            {data.map(department => renderTreeNode(department))}
          </Tree>
        </div>
      </div>

      {/* 精美的操作说明 */}
      <div className="mt-12 pt-8 border-t border-tech-dark-600/50">
        <div className="bg-tech-dark-700/30 backdrop-blur-sm border border-tech-dark-500/50 rounded-2xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-tech-purple-gradient rounded-lg flex items-center justify-center">
              <SparklesIcon className="h-4 w-4 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-tech-gray-200">操作指南</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3 p-3 bg-tech-dark-800/30 rounded-xl">
              <div className="w-10 h-10 bg-tech-green-gradient rounded-lg flex items-center justify-center shadow-lg">
                <PlusIcon className="h-5 w-5 text-white" />
              </div>
              <div>
                <div className="font-medium text-tech-gray-200">添加子部门</div>
                <div className="text-xs text-tech-gray-400">在当前部门下创建子部门</div>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-tech-dark-800/30 rounded-xl">
              <div className="w-10 h-10 bg-tech-blue-gradient rounded-lg flex items-center justify-center shadow-lg">
                <PencilIcon className="h-5 w-5 text-white" />
              </div>
              <div>
                <div className="font-medium text-tech-gray-200">编辑部门</div>
                <div className="text-xs text-tech-gray-400">修改部门信息和配置</div>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-tech-dark-800/30 rounded-xl">
              <div className="w-10 h-10 bg-tech-red-gradient rounded-lg flex items-center justify-center shadow-lg">
                <TrashIcon className="h-5 w-5 text-white" />
              </div>
              <div>
                <div className="font-medium text-tech-gray-200">删除部门</div>
                <div className="text-xs text-tech-gray-400">移除部门及其关联数据</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 计算总部门数量
  function getTotalDepartmentCount(departments) {
    let count = 0;
    
    function countRecursive(depts) {
      depts.forEach(dept => {
        count++;
        if (dept.children && dept.children.length > 0) {
          countRecursive(dept.children);
        }
      });
    }
    
    countRecursive(departments);
    return count;
  }
};

export default DepartmentTreeView;
