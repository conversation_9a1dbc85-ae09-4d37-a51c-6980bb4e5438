import React from 'react';
import { Tree, TreeNode } from 'react-organizational-chart';
import { 
  BuildingOfficeIcon, 
  PencilIcon, 
  TrashIcon, 
  PlusIcon,
  UserIcon
} from '@heroicons/react/24/outline';

const DepartmentTreeView = ({ data, loading, onEdit, onDelete, onAddChild }) => {
  
  // 渲染部门节点
  const renderDepartmentNode = (department) => {
    const isActive = department.IS_ACTIVE === 1;
    
    return (
      <div 
        key={department.DEPT_ID}
        className={`relative bg-tech-dark-700 border rounded-lg p-4 min-w-[200px] max-w-[250px] ${
          isActive 
            ? 'border-tech-blue-500/30 shadow-tech-glow' 
            : 'border-tech-gray-600/30'
        }`}
      >
        {/* 部门信息 */}
        <div className="text-center mb-3">
          <div className="flex items-center justify-center mb-2">
            <BuildingOfficeIcon className={`h-6 w-6 ${
              isActive ? 'text-tech-blue-400' : 'text-tech-gray-500'
            }`} />
          </div>
          
          <div className="space-y-1">
            <h3 className={`font-semibold text-sm ${
              isActive ? 'text-tech-gray-200' : 'text-tech-gray-400'
            }`}>
              {department.DEPT_NAME}
            </h3>
            
            <div className={`text-xs ${
              isActive ? 'text-tech-gray-400' : 'text-tech-gray-500'
            }`}>
              {department.DEPT_CODE}
            </div>
            
            {department.MANAGER_USERNAME && (
              <div className="flex items-center justify-center text-xs text-tech-gray-400">
                <UserIcon className="h-3 w-3 mr-1" />
                {department.MANAGER_USERNAME}
              </div>
            )}
          </div>
        </div>

        {/* 状态标识 */}
        <div className="flex justify-center mb-3">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            isActive
              ? 'bg-green-900/30 text-green-300 border border-green-500/30'
              : 'bg-red-900/30 text-red-300 border border-red-500/30'
          }`}>
            {isActive ? '启用' : '禁用'}
          </span>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-center space-x-1">
          <button
            onClick={() => onAddChild(department)}
            className="p-1.5 bg-tech-green-600/20 hover:bg-tech-green-600/30 text-tech-green-400 rounded transition-colors"
            title="添加子部门"
          >
            <PlusIcon className="h-3 w-3" />
          </button>
          
          <button
            onClick={() => onEdit(department)}
            className="p-1.5 bg-tech-blue-600/20 hover:bg-tech-blue-600/30 text-tech-blue-400 rounded transition-colors"
            title="编辑部门"
          >
            <PencilIcon className="h-3 w-3" />
          </button>
          
          <button
            onClick={() => onDelete(department)}
            className="p-1.5 bg-red-600/20 hover:bg-red-600/30 text-red-400 rounded transition-colors"
            title="删除部门"
          >
            <TrashIcon className="h-3 w-3" />
          </button>
        </div>

        {/* 描述信息（如果有） */}
        {department.DESCRIPTION && (
          <div className="mt-2 pt-2 border-t border-tech-dark-600">
            <div className="text-xs text-tech-gray-500 text-center truncate" title={department.DESCRIPTION}>
              {department.DESCRIPTION}
            </div>
          </div>
        )}
      </div>
    );
  };

  // 递归渲染树形结构
  const renderTreeNode = (department) => {
    const hasChildren = department.children && department.children.length > 0;
    
    return (
      <TreeNode 
        key={department.DEPT_ID}
        label={renderDepartmentNode(department)}
      >
        {hasChildren && department.children.map(child => renderTreeNode(child))}
      </TreeNode>
    );
  };

  // 加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-pulse-slow flex flex-col items-center">
          <div className="w-12 h-12 rounded-full bg-tech-blue-gradient flex items-center justify-center shadow-tech-glow mb-4">
            <BuildingOfficeIcon className="h-6 w-6 text-white" />
          </div>
          <p className="text-tech-blue-400 font-medium">加载部门树形结构中...</p>
        </div>
      </div>
    );
  }

  // 空数据状态
  if (!data || data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <BuildingOfficeIcon className="h-16 w-16 text-tech-gray-500 mb-4" />
        <p className="text-tech-gray-400 text-lg font-medium mb-2">暂无部门数据</p>
        <p className="text-tech-gray-500 text-sm">请先添加部门信息</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* 树形图容器 */}
      <div className="overflow-x-auto">
        <div className="min-w-full">
          <Tree
            lineWidth="2px"
            lineColor="#374151"
            lineBorderRadius="8px"
            label={
              <div className="text-center mb-6">
                <h2 className="text-lg font-semibold text-tech-gray-200 mb-2">
                  部门组织架构
                </h2>
                <p className="text-sm text-tech-gray-400">
                  共 {getTotalDepartmentCount(data)} 个部门
                </p>
              </div>
            }
          >
            {data.map(department => renderTreeNode(department))}
          </Tree>
        </div>
      </div>

      {/* 图例说明 */}
      <div className="mt-8 pt-6 border-t border-tech-dark-600">
        <h3 className="text-sm font-medium text-tech-gray-300 mb-3">操作说明</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-xs">
          <div className="flex items-center space-x-2">
            <div className="p-1 bg-tech-green-600/20 text-tech-green-400 rounded">
              <PlusIcon className="h-3 w-3" />
            </div>
            <span className="text-tech-gray-400">添加子部门</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="p-1 bg-tech-blue-600/20 text-tech-blue-400 rounded">
              <PencilIcon className="h-3 w-3" />
            </div>
            <span className="text-tech-gray-400">编辑部门</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="p-1 bg-red-600/20 text-red-400 rounded">
              <TrashIcon className="h-3 w-3" />
            </div>
            <span className="text-tech-gray-400">删除部门</span>
          </div>
        </div>
      </div>
    </div>
  );

  // 计算总部门数量
  function getTotalDepartmentCount(departments) {
    let count = 0;
    
    function countRecursive(depts) {
      depts.forEach(dept => {
        count++;
        if (dept.children && dept.children.length > 0) {
          countRecursive(dept.children);
        }
      });
    }
    
    countRecursive(departments);
    return count;
  }
};

export default DepartmentTreeView;
