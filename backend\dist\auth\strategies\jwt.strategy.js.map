{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,kDAA8C;AAIvC,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACzD,YACU,aAA4B,EAC5B,WAAwB;QAEhC,KAAK,CAAC;YACJ,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC;YACpD,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;SACxB,CAAC,CAAC;QAPK,kBAAa,GAAb,aAAa,CAAe;QAC5B,gBAAW,GAAX,WAAW,CAAa;IAOlC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAA2C;QACxD,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;QAClC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE3D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,8BAAqB,EAAE,CAAC;QACpC,CAAC;QAGD,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,IAAI,GAAG;YAClB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SAExB,CAAC;IACJ,CAAC;CACF,CAAA;AA3BY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGc,sBAAa;QACf,0BAAW;GAHvB,WAAW,CA2BvB"}