import { DataSource } from 'typeorm';
import { AssignUserRoleDto } from './dto/assign-user-role.dto';
import { AssignDevicePermissionDto } from './dto/assign-device-permission.dto';
export declare class UserPermissionService {
    private dataSource;
    constructor(dataSource: DataSource);
    assignUserRoles(userId: number, assignUserRoleDto: AssignUserRoleDto): Promise<void>;
    getUserRoles(userId: number): Promise<any[]>;
    assignDevicePermissions(userId: number, assignDevicePermissionDto: AssignDevicePermissionDto): Promise<void>;
    getUserDevicePermissions(userId: number): Promise<any[]>;
    getUserAccessibleDevices(userId: number, permissionType?: string): Promise<any[]>;
    checkUserDevicePermission(userId: number, ledId: string, permissionType: string): Promise<boolean>;
    getUserPermissionSummary(userId: number): Promise<any>;
    getPermissionTypes(): Promise<any[]>;
}
