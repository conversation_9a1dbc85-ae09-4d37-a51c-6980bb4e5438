import { Repository, DataSource } from 'typeorm';
import { ProductionLine } from '../entities/production-line.entity';
import { CreateProductionLineDto, UpdateProductionLineDto } from '../dto/production-line.dto';
export declare class ProductionLineService {
    private productionLineRepository;
    private dataSource;
    constructor(productionLineRepository: Repository<ProductionLine>, dataSource: DataSource);
    findAll(query?: any): Promise<{
        items: ProductionLine[];
        meta: any;
    }>;
    findOne(id: number): Promise<ProductionLine>;
    create(createDto: CreateProductionLineDto, user?: any): Promise<ProductionLine>;
    update(id: number, updateDto: UpdateProductionLineDto, user?: any): Promise<ProductionLine>;
    remove(id: number): Promise<void>;
}
