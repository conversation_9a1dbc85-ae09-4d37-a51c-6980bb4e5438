"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConcentratorLedMapping = void 0;
const typeorm_1 = require("typeorm");
const led_concentrator_entity_1 = require("./led-concentrator.entity");
let ConcentratorLedMapping = class ConcentratorLedMapping {
};
exports.ConcentratorLedMapping = ConcentratorLedMapping;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'MAPPING_ID' }),
    __metadata("design:type", Number)
], ConcentratorLedMapping.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONCENTRATOR_ID' }),
    __metadata("design:type", Number)
], ConcentratorLedMapping.prototype, "concentratorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_ID' }),
    __metadata("design:type", String)
], ConcentratorLedMapping.prototype, "ledId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CHANNEL_NUMBER', nullable: true }),
    __metadata("design:type", String)
], ConcentratorLedMapping.prototype, "channelNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRIPTION', nullable: true }),
    __metadata("design:type", String)
], ConcentratorLedMapping.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', default: true }),
    __metadata("design:type", Boolean)
], ConcentratorLedMapping.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', nullable: true }),
    __metadata("design:type", String)
], ConcentratorLedMapping.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'CREATION_DATE' }),
    __metadata("design:type", Date)
], ConcentratorLedMapping.prototype, "creationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', nullable: true }),
    __metadata("design:type", String)
], ConcentratorLedMapping.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'LAST_UPDATE_DATE', nullable: true }),
    __metadata("design:type", Date)
], ConcentratorLedMapping.prototype, "lastUpdateDate", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => led_concentrator_entity_1.LedConcentrator, concentrator => concentrator.ledMappings),
    (0, typeorm_1.JoinColumn)({ name: 'CONCENTRATOR_ID' }),
    __metadata("design:type", led_concentrator_entity_1.LedConcentrator)
], ConcentratorLedMapping.prototype, "concentrator", void 0);
exports.ConcentratorLedMapping = ConcentratorLedMapping = __decorate([
    (0, typeorm_1.Entity)('CONCENTRATOR_LED_MAPPING')
], ConcentratorLedMapping);
//# sourceMappingURL=concentrator-led-mapping.entity.js.map