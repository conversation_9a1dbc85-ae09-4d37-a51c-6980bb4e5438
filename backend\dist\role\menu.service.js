"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MenuService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
let MenuService = class MenuService {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async findAll(params = {}) {
        try {
            const page = params.page ? parseInt(params.page) : 1;
            const limit = params.limit ? parseInt(params.limit) : 50;
            const offset = (page - 1) * limit;
            const search = params.search;
            const menuType = params.menuType;
            const parentId = params.parentId;
            let whereClause = 'WHERE IS_ACTIVE = 1';
            const queryParams = [];
            let paramIndex = 1;
            if (search) {
                whereClause += ` AND (MENU_CODE LIKE :${paramIndex} OR MENU_NAME LIKE :${paramIndex})`;
                queryParams.push(`%${search}%`);
                paramIndex++;
            }
            if (menuType) {
                whereClause += ` AND MENU_TYPE = :${paramIndex}`;
                queryParams.push(menuType);
                paramIndex++;
            }
            if (parentId !== undefined) {
                if (parentId === 'null' || parentId === null) {
                    whereClause += ` AND PARENT_MENU_ID IS NULL`;
                }
                else {
                    whereClause += ` AND PARENT_MENU_ID = :${paramIndex}`;
                    queryParams.push(parentId);
                    paramIndex++;
                }
            }
            const countQuery = `SELECT COUNT(*) AS total FROM LED_MENU ${whereClause}`;
            const countResult = await this.dataSource.query(countQuery, queryParams);
            const totalItems = parseInt(countResult[0].TOTAL);
            const query = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT m.*, 
                   p.MENU_NAME as PARENT_MENU_NAME
            FROM LED_MENU m
            LEFT JOIN LED_MENU p ON m.PARENT_MENU_ID = p.MENU_ID
            ${whereClause}
            ORDER BY m.SORT_ORDER, m.MENU_CODE
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const menus = await this.dataSource.query(query, queryParams);
            return {
                items: menus,
                meta: {
                    totalItems,
                    itemsPerPage: limit,
                    currentPage: page,
                    totalPages: Math.ceil(totalItems / limit)
                }
            };
        }
        catch (error) {
            console.error('获取菜单列表失败:', error);
            throw new common_1.InternalServerErrorException('获取菜单列表失败');
        }
    }
    async getTree() {
        try {
            const query = `
        SELECT m.*, 
               p.MENU_NAME as PARENT_MENU_NAME
        FROM LED_MENU m
        LEFT JOIN LED_MENU p ON m.PARENT_MENU_ID = p.MENU_ID
        WHERE m.IS_ACTIVE = 1
        ORDER BY m.SORT_ORDER, m.MENU_CODE
      `;
            const menus = await this.dataSource.query(query);
            const menuMap = new Map();
            const rootMenus = [];
            menus.forEach((menu) => {
                menu.children = [];
                menuMap.set(menu.MENU_ID, menu);
            });
            menus.forEach((menu) => {
                if (menu.PARENT_MENU_ID) {
                    const parent = menuMap.get(menu.PARENT_MENU_ID);
                    if (parent) {
                        parent.children.push(menu);
                    }
                }
                else {
                    rootMenus.push(menu);
                }
            });
            return rootMenus;
        }
        catch (error) {
            console.error('获取菜单树形结构失败:', error);
            throw new common_1.InternalServerErrorException('获取菜单树形结构失败');
        }
    }
    async findOne(id) {
        try {
            const menus = await this.dataSource.query(`SELECT m.*, 
                p.MENU_NAME as PARENT_MENU_NAME
         FROM LED_MENU m
         LEFT JOIN LED_MENU p ON m.PARENT_MENU_ID = p.MENU_ID
         WHERE m.MENU_ID = :1`, [id]);
            if (!menus || menus.length === 0) {
                throw new common_1.NotFoundException(`ID为${id}的菜单不存在`);
            }
            return menus[0];
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取ID为${id}的菜单失败:`, error);
            throw new common_1.InternalServerErrorException(`获取ID为${id}的菜单失败`);
        }
    }
    async getUserMenus(userId) {
        try {
            const query = `
        SELECT DISTINCT m.*, 
               p.MENU_NAME as PARENT_MENU_NAME
        FROM LED_MENU m
        LEFT JOIN LED_MENU p ON m.PARENT_MENU_ID = p.MENU_ID
        JOIN LED_ROLE_MENU rm ON m.MENU_ID = rm.MENU_ID
        JOIN LED_USER_ROLE ur ON rm.ROLE_ID = ur.ROLE_ID
        WHERE ur.USER_ID = :1 
        AND m.IS_ACTIVE = 1 
        AND rm.IS_ACTIVE = 1 
        AND ur.IS_ACTIVE = 1
        ORDER BY m.SORT_ORDER, m.MENU_CODE
      `;
            const menus = await this.dataSource.query(query, [userId]);
            const menuMap = new Map();
            const rootMenus = [];
            menus.forEach((menu) => {
                menu.children = [];
                menuMap.set(menu.MENU_ID, menu);
            });
            menus.forEach((menu) => {
                if (menu.PARENT_MENU_ID) {
                    const parent = menuMap.get(menu.PARENT_MENU_ID);
                    if (parent) {
                        parent.children.push(menu);
                    }
                }
                else {
                    rootMenus.push(menu);
                }
            });
            return rootMenus;
        }
        catch (error) {
            console.error(`获取用户${userId}的菜单权限失败:`, error);
            throw new common_1.InternalServerErrorException('获取用户菜单权限失败');
        }
    }
    async checkUserMenuPermission(userId, menuCode) {
        try {
            const result = await this.dataSource.query(`SELECT COUNT(*) AS count
         FROM LED_MENU m
         JOIN LED_ROLE_MENU rm ON m.MENU_ID = rm.MENU_ID
         JOIN LED_USER_ROLE ur ON rm.ROLE_ID = ur.ROLE_ID
         WHERE ur.USER_ID = :1 
         AND m.MENU_CODE = :2
         AND m.IS_ACTIVE = 1 
         AND rm.IS_ACTIVE = 1 
         AND ur.IS_ACTIVE = 1`, [userId, menuCode]);
            return parseInt(result[0].COUNT) > 0;
        }
        catch (error) {
            console.error(`检查用户${userId}对菜单${menuCode}的权限失败:`, error);
            return false;
        }
    }
    async getMenuTypes() {
        return [
            { value: 'MENU', label: '菜单', description: '导航菜单项' },
            { value: 'BUTTON', label: '按钮', description: '页面操作按钮' }
        ];
    }
};
exports.MenuService = MenuService;
exports.MenuService = MenuService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], MenuService);
//# sourceMappingURL=menu.service.js.map