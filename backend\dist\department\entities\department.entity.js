"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Department = void 0;
const typeorm_1 = require("typeorm");
let Department = class Department {
};
exports.Department = Department;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'DEPT_ID' }),
    __metadata("design:type", Number)
], Department.prototype, "DEPT_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DEPT_CODE', unique: true, nullable: false }),
    __metadata("design:type", String)
], Department.prototype, "DEPT_CODE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DEPT_NAME', nullable: false }),
    __metadata("design:type", String)
], Department.prototype, "DEPT_NAME", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PARENT_DEPT_ID', nullable: true }),
    __metadata("design:type", Number)
], Department.prototype, "PARENT_DEPT_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DEPT_LEVEL', default: 1 }),
    __metadata("design:type", Number)
], Department.prototype, "DEPT_LEVEL", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DEPT_PATH', nullable: true }),
    __metadata("design:type", String)
], Department.prototype, "DEPT_PATH", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'MANAGER_USER_ID', nullable: true }),
    __metadata("design:type", Number)
], Department.prototype, "MANAGER_USER_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRIPTION', nullable: true }),
    __metadata("design:type", String)
], Department.prototype, "DESCRIPTION", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'SORT_ORDER', default: 0 }),
    __metadata("design:type", Number)
], Department.prototype, "SORT_ORDER", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', default: 1 }),
    __metadata("design:type", Number)
], Department.prototype, "IS_ACTIVE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', nullable: true }),
    __metadata("design:type", String)
], Department.prototype, "CREATED_BY", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATION_DATE', type: 'date', default: () => 'SYSDATE' }),
    __metadata("design:type", Date)
], Department.prototype, "CREATION_DATE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', nullable: true }),
    __metadata("design:type", String)
], Department.prototype, "LAST_UPDATED_BY", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATE_DATE', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Department.prototype, "LAST_UPDATE_DATE", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Department, department => department.children),
    (0, typeorm_1.JoinColumn)({ name: 'PARENT_DEPT_ID' }),
    __metadata("design:type", Department)
], Department.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Department, department => department.parent),
    __metadata("design:type", Array)
], Department.prototype, "children", void 0);
exports.Department = Department = __decorate([
    (0, typeorm_1.Entity)('LED_DEPARTMENT')
], Department);
//# sourceMappingURL=department.entity.js.map