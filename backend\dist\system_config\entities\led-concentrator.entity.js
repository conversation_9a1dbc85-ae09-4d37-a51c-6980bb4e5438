"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedConcentrator = void 0;
const typeorm_1 = require("typeorm");
const concentrator_led_mapping_entity_1 = require("./concentrator-led-mapping.entity");
let LedConcentrator = class LedConcentrator {
};
exports.LedConcentrator = LedConcentrator;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'CONCENTRATOR_ID' }),
    __metadata("design:type", Number)
], LedConcentrator.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONCENTRATOR_CODE', unique: true }),
    __metadata("design:type", String)
], LedConcentrator.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONCENTRATOR_NAME' }),
    __metadata("design:type", String)
], LedConcentrator.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IP_ADDRESS' }),
    __metadata("design:type", String)
], LedConcentrator.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PORT_NUMBER' }),
    __metadata("design:type", Number)
], LedConcentrator.prototype, "portNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PROTOCOL_TYPE', default: 'TCP/IP' }),
    __metadata("design:type", String)
], LedConcentrator.prototype, "protocolType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'AUTH_KEY', nullable: true }),
    __metadata("design:type", String)
], LedConcentrator.prototype, "authKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'REFRESH_INTERVAL', default: 30 }),
    __metadata("design:type", Number)
], LedConcentrator.prototype, "refreshInterval", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'TIMEOUT_SECONDS', default: 10 }),
    __metadata("design:type", Number)
], LedConcentrator.prototype, "timeoutSeconds", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'MAX_RETRY_COUNT', default: 3 }),
    __metadata("design:type", Number)
], LedConcentrator.prototype, "maxRetryCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'AREA_CODE', nullable: true }),
    __metadata("design:type", String)
], LedConcentrator.prototype, "areaCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRIPTION', nullable: true }),
    __metadata("design:type", String)
], LedConcentrator.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', default: true }),
    __metadata("design:type", Boolean)
], LedConcentrator.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_HEARTBEAT_TIME', nullable: true }),
    __metadata("design:type", Date)
], LedConcentrator.prototype, "lastHeartbeatTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONNECTION_STATUS', nullable: true }),
    __metadata("design:type", String)
], LedConcentrator.prototype, "connectionStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', nullable: true }),
    __metadata("design:type", String)
], LedConcentrator.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'CREATION_DATE' }),
    __metadata("design:type", Date)
], LedConcentrator.prototype, "creationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', nullable: true }),
    __metadata("design:type", String)
], LedConcentrator.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'LAST_UPDATE_DATE', nullable: true }),
    __metadata("design:type", Date)
], LedConcentrator.prototype, "lastUpdateDate", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => concentrator_led_mapping_entity_1.ConcentratorLedMapping, mapping => mapping.concentrator),
    __metadata("design:type", Array)
], LedConcentrator.prototype, "ledMappings", void 0);
exports.LedConcentrator = LedConcentrator = __decorate([
    (0, typeorm_1.Entity)('LED_CONCENTRATOR')
], LedConcentrator);
//# sourceMappingURL=led-concentrator.entity.js.map