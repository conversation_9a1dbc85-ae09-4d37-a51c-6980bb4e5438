"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedDeviceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
let LedDeviceService = class LedDeviceService {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async create(createDeviceDto) {
        try {
            const result = await this.dataSource.query(`INSERT INTO LED_DEVICE_INFO(
          ID, LED_ID, LED_NAME, DEP_NAME, DEP_ID, DEP_ID_OA, GONGXU, CHANXIAN
        ) VALUES(
          SEQ_LED_DEVICE_INFO.NEXTVAL, :1, :2, :3, :4, :5, :6, :7
        ) RETURNING ID INTO :8`, [
                createDeviceDto.LED_ID,
                createDeviceDto.LED_NAME,
                createDeviceDto.DEP_NAME || null,
                createDeviceDto.DEP_ID || null,
                createDeviceDto.DEP_ID_OA || null,
                createDeviceDto.GONGXU || null,
                createDeviceDto.CHANXIAN || null,
                { dir: this.dataSource.driver.oracle.BIND_OUT, type: this.dataSource.driver.oracle.NUMBER }
            ]);
            const newId = result[0];
            const devices = await this.dataSource.query(`SELECT * FROM LED_DEVICE_INFO WHERE ID = ${newId}`);
            if (!devices || devices.length === 0) {
                throw new common_1.NotFoundException(`ID为${newId}的LED设备不存在`);
            }
            return devices[0];
        }
        catch (error) {
            console.error('创建LED设备失败:', error);
            throw new common_1.InternalServerErrorException('创建LED设备失败');
        }
    }
    async findAll(params = {}) {
        try {
            const page = params.page ? parseInt(params.page) : 1;
            const limit = params.limit ? parseInt(params.limit) : 10;
            const offset = (page - 1) * limit;
            const search = params.search;
            let whereClause = '';
            if (search) {
                whereClause = ` WHERE LED_ID LIKE '%${search}%' OR LED_NAME LIKE '%${search}%'`;
            }
            const countQuery = `SELECT COUNT(*) AS total FROM LED_DEVICE_INFO${whereClause}`;
            const countResult = await this.dataSource.query(countQuery);
            const totalItems = parseInt(countResult[0].TOTAL);
            const query = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT * FROM LED_DEVICE_INFO${whereClause}
            ORDER BY ID DESC
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const devices = await this.dataSource.query(query);
            return {
                items: devices,
                meta: {
                    totalItems,
                    itemsPerPage: limit,
                    currentPage: page,
                    totalPages: Math.ceil(totalItems / limit)
                }
            };
        }
        catch (error) {
            console.error('获取LED设备列表失败:', error);
            throw new common_1.InternalServerErrorException('获取LED设备列表失败');
        }
    }
    async findOne(id) {
        try {
            const devices = await this.dataSource.query(`SELECT * FROM LED_DEVICE_INFO WHERE ID = ${id}`);
            if (!devices || devices.length === 0) {
                throw new common_1.NotFoundException(`ID为${id}的LED设备不存在`);
            }
            return devices[0];
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取ID为${id}的LED设备失败:`, error);
            throw new common_1.InternalServerErrorException(`获取ID为${id}的LED设备失败`);
        }
    }
    async update(id, updateDeviceDto) {
        try {
            await this.findOne(id);
            let updateQuery = 'UPDATE LED_DEVICE_INFO SET ';
            const updateValues = [];
            let paramIndex = 1;
            if (updateDeviceDto.LED_ID !== undefined) {
                updateQuery += `LED_ID = :${paramIndex}, `;
                updateValues.push(updateDeviceDto.LED_ID);
                paramIndex++;
            }
            if (updateDeviceDto.LED_NAME !== undefined) {
                updateQuery += `LED_NAME = :${paramIndex}, `;
                updateValues.push(updateDeviceDto.LED_NAME);
                paramIndex++;
            }
            if (updateDeviceDto.DEP_NAME !== undefined) {
                updateQuery += `DEP_NAME = :${paramIndex}, `;
                updateValues.push(updateDeviceDto.DEP_NAME);
                paramIndex++;
            }
            if (updateDeviceDto.DEP_ID !== undefined) {
                updateQuery += `DEP_ID = :${paramIndex}, `;
                updateValues.push(updateDeviceDto.DEP_ID);
                paramIndex++;
            }
            if (updateDeviceDto.DEP_ID_OA !== undefined) {
                updateQuery += `DEP_ID_OA = :${paramIndex}, `;
                updateValues.push(updateDeviceDto.DEP_ID_OA);
                paramIndex++;
            }
            if (updateDeviceDto.GONGXU !== undefined) {
                updateQuery += `GONGXU = :${paramIndex}, `;
                updateValues.push(updateDeviceDto.GONGXU);
                paramIndex++;
            }
            if (updateDeviceDto.CHANXIAN !== undefined) {
                updateQuery += `CHANXIAN = :${paramIndex}, `;
                updateValues.push(updateDeviceDto.CHANXIAN);
                paramIndex++;
            }
            updateQuery = updateQuery.slice(0, -2);
            updateQuery += ` WHERE ID = :${paramIndex}`;
            updateValues.push(id);
            await this.dataSource.query(updateQuery, updateValues);
            return this.findOne(id);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`更新ID为${id}的LED设备失败:`, error);
            throw new common_1.InternalServerErrorException(`更新ID为${id}的LED设备失败`);
        }
    }
    async remove(id) {
        try {
            await this.findOne(id);
            await this.dataSource.query(`DELETE FROM LED_DEVICE_INFO WHERE ID = ${id}`);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`删除ID为${id}的LED设备失败:`, error);
            throw new common_1.InternalServerErrorException(`删除ID为${id}的LED设备失败`);
        }
    }
};
exports.LedDeviceService = LedDeviceService;
exports.LedDeviceService = LedDeviceService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], LedDeviceService);
//# sourceMappingURL=led_device.service.js.map