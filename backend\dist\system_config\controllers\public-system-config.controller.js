"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PublicSystemConfigController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublicSystemConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const system_config_service_1 = require("../services/system-config.service");
let PublicSystemConfigController = PublicSystemConfigController_1 = class PublicSystemConfigController {
    constructor(systemConfigService) {
        this.systemConfigService = systemConfigService;
        this.logger = new common_1.Logger(PublicSystemConfigController_1.name);
    }
    async getPublicConfig(key) {
        this.logger.log(`获取公开配置: ${key}`);
        const allowedKeys = ['PLATFORM_NAME', 'LOGO_NAME', 'COMPANY_NAME', 'COPYRIGHT'];
        if (!allowedKeys.includes(key)) {
            this.logger.warn(`尝试获取非公开配置: ${key}`);
            return {
                key,
                value: '',
                description: '该配置不是公开配置'
            };
        }
        try {
            const config = await this.systemConfigService.findByKey(key);
            return config;
        }
        catch (error) {
            this.logger.error(`获取公开配置 ${key} 失败:`, error);
            return this.getDefaultConfig(key);
        }
    }
    async getPublicConfigs(keys) {
        this.logger.log(`批量获取公开配置: ${JSON.stringify(keys)}`);
        const allowedKeys = ['PLATFORM_NAME', 'LOGO_NAME', 'COMPANY_NAME', 'COPYRIGHT'];
        const result = {};
        for (const key of keys) {
            if (!allowedKeys.includes(key)) {
                this.logger.warn(`尝试获取非公开配置: ${key}`);
                result[key] = {
                    key,
                    value: '',
                    description: '该配置不是公开配置'
                };
                continue;
            }
            try {
                const config = await this.systemConfigService.findByKey(key);
                result[key] = config;
            }
            catch (error) {
                this.logger.error(`获取公开配置 ${key} 失败:`, error);
                result[key] = this.getDefaultConfig(key);
            }
        }
        return result;
    }
    getDefaultConfig(key) {
        const defaults = {
            'PLATFORM_NAME': { key, value: 'KL电子看板监控管理平台', description: '平台名称' },
            'LOGO_NAME': { key, value: 'KL看板', description: 'Logo名称' },
            'COMPANY_NAME': { key, value: 'KL科技有限公司', description: '公司名称' },
            'COPYRIGHT': { key, value: '© 2024 KL科技有限公司. 保留所有权利.', description: '版权信息' }
        };
        return defaults[key] || { key, value: '', description: '未知配置' };
    }
};
exports.PublicSystemConfigController = PublicSystemConfigController;
__decorate([
    (0, common_1.Get)(':key'),
    (0, swagger_1.ApiOperation)({ summary: '获取公开的系统配置（不需要认证）' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功获取系统配置' }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PublicSystemConfigController.prototype, "getPublicConfig", null);
__decorate([
    (0, common_1.Post)('batch'),
    (0, swagger_1.ApiOperation)({ summary: '批量获取公开的系统配置' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功获取系统配置' }),
    __param(0, (0, common_1.Body)('keys')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], PublicSystemConfigController.prototype, "getPublicConfigs", null);
exports.PublicSystemConfigController = PublicSystemConfigController = PublicSystemConfigController_1 = __decorate([
    (0, swagger_1.ApiTags)('公开系统配置'),
    (0, common_1.Controller)('system-config/public'),
    __metadata("design:paramtypes", [system_config_service_1.SystemConfigService])
], PublicSystemConfigController);
//# sourceMappingURL=public-system-config.controller.js.map