{"version": 3, "file": "user-permission.controller.js", "sourceRoot": "", "sources": ["../../src/role/user-permission.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAWwB;AACxB,uEAAkE;AAClE,qEAA+D;AAC/D,qFAA+E;AAGxE,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACnC,YAA6B,qBAA4C;QAA5C,0BAAqB,GAArB,qBAAqB,CAAuB;IAAG,CAAC;IAOvE,AAAN,KAAK,CAAC,eAAe,CACY,MAAc,EACvB,iBAAoC;QAE1D,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;YAC5E,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE,IAAI;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,UAAU;gBACpC,IAAI,EAAE,IAAI;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAMK,AAAN,KAAK,CAAC,YAAY,CAAgC,MAAc;QAC9D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACpE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE,KAAK;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,UAAU;gBACpC,IAAI,EAAE,EAAE;aACT,CAAC;QACJ,CAAC;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,uBAAuB,CACI,MAAc,EACvB,yBAAoD;QAE1E,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAAC;YAC5F,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE,IAAI;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,UAAU;gBACpC,IAAI,EAAE,IAAI;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAMK,AAAN,KAAK,CAAC,wBAAwB,CAAgC,MAAc;QAC1E,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;YACtF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE,WAAW;aAClB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,YAAY;gBACtC,IAAI,EAAE,EAAE;aACT,CAAC;QACJ,CAAC;IACH,CAAC;IAMK,AAAN,KAAK,CAAC,wBAAwB,CACG,MAAc,EACpB,cAAuB;QAEhD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAClG,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE,OAAO;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,aAAa;gBACvC,IAAI,EAAE,EAAE;aACT,CAAC;QACJ,CAAC;IACH,CAAC;IAMK,AAAN,KAAK,CAAC,wBAAwB,CAAgC,MAAc;QAC1E,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;YAClF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE,OAAO;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,YAAY;gBACtC,IAAI,EAAE,IAAI;aACX,CAAC;QACJ,CAAC;IACH,CAAC;IAMK,AAAN,KAAK,CAAC,yBAAyB,CACE,MAAc,EAC7B,KAAa,EACJ,cAAsB;QAE/C,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAC9E,MAAM,EACN,KAAK,EACL,cAAc,CACf,CAAC;YACF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,MAAM;oBACN,KAAK;oBACL,cAAc;oBACd,aAAa;iBACd;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,QAAQ;gBAClC,IAAI,EAAE;oBACJ,MAAM;oBACN,KAAK;oBACL,cAAc;oBACd,aAAa,EAAE,KAAK;iBACrB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAMK,AAAN,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,CAAC;YACpE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE,KAAK;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,UAAU;gBACpC,IAAI,EAAE,EAAE;aACT,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAvMY,4DAAwB;AAQ7B;IAFL,IAAA,aAAI,EAAC,eAAe,CAAC;IACrB,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IAErB,WAAA,IAAA,cAAK,EAAC,QAAQ,EAAE,qBAAY,CAAC,CAAA;IAC7B,WAAA,IAAA,aAAI,EAAC,uBAAc,CAAC,CAAA;;6CAAoB,wCAAiB;;+DAgB3D;AAMK;IADL,IAAA,YAAG,EAAC,eAAe,CAAC;IACD,WAAA,IAAA,cAAK,EAAC,QAAQ,EAAE,qBAAY,CAAC,CAAA;;;;4DAehD;AAOK;IAFL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IACvB,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IAErB,WAAA,IAAA,cAAK,EAAC,QAAQ,EAAE,qBAAY,CAAC,CAAA;IAC7B,WAAA,IAAA,aAAI,EAAC,uBAAc,CAAC,CAAA;;6CAA4B,wDAAyB;;uEAgB3E;AAMK;IADL,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACS,WAAA,IAAA,cAAK,EAAC,QAAQ,EAAE,qBAAY,CAAC,CAAA;;;;wEAe5D;AAMK;IADL,IAAA,YAAG,EAAC,4BAA4B,CAAC;IAE/B,WAAA,IAAA,cAAK,EAAC,QAAQ,EAAE,qBAAY,CAAC,CAAA;IAC7B,WAAA,IAAA,cAAK,EAAC,gBAAgB,CAAC,CAAA;;;;wEAgBzB;AAMK;IADL,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACS,WAAA,IAAA,cAAK,EAAC,QAAQ,EAAE,qBAAY,CAAC,CAAA;;;;wEAe5D;AAMK;IADL,IAAA,YAAG,EAAC,6CAA6C,CAAC;IAEhD,WAAA,IAAA,cAAK,EAAC,QAAQ,EAAE,qBAAY,CAAC,CAAA;IAC7B,WAAA,IAAA,cAAK,EAAC,OAAO,CAAC,CAAA;IACd,WAAA,IAAA,cAAK,EAAC,gBAAgB,CAAC,CAAA;;;;yEA8BzB;AAMK;IADL,IAAA,YAAG,EAAC,kBAAkB,CAAC;;;;kEAgBvB;mCAtMU,wBAAwB;IADpC,IAAA,mBAAU,EAAC,kBAAkB,CAAC;qCAEuB,+CAAqB;GAD9D,wBAAwB,CAuMpC"}