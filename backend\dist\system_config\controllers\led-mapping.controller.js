"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LedMappingController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedMappingController = void 0;
const common_1 = require("@nestjs/common");
const led_mapping_service_1 = require("../services/led-mapping.service");
const led_mapping_dto_1 = require("../dto/led-mapping.dto");
let LedMappingController = LedMappingController_1 = class LedMappingController {
    constructor(ledMappingService) {
        this.ledMappingService = ledMappingService;
        this.logger = new common_1.Logger(LedMappingController_1.name);
    }
    create(createLedMappingDto) {
        this.logger.log(`创建LED映射: ${JSON.stringify(createLedMappingDto)}`);
        return this.ledMappingService.create(createLedMappingDto);
    }
    findAll(query) {
        return this.ledMappingService.findAll(query);
    }
    findOne(id) {
        return this.ledMappingService.findOne(+id);
    }
    findByLedId(ledId) {
        return this.ledMappingService.findByLedId(ledId);
    }
    update(id, updateLedMappingDto) {
        this.logger.log(`更新LED映射 ${id}: ${JSON.stringify(updateLedMappingDto)}`);
        if (!id || isNaN(+id) || +id <= 0) {
            throw new Error(`无效的映射ID: ${id}`);
        }
        return this.ledMappingService.update(+id, updateLedMappingDto);
    }
    remove(id) {
        this.logger.log(`删除LED映射 ${id}`);
        if (!id || isNaN(+id) || +id <= 0) {
            this.logger.error(`无效的映射ID: ${id}`);
            throw new Error(`无效的映射ID: ${id}`);
        }
        return this.ledMappingService.remove(+id);
    }
};
exports.LedMappingController = LedMappingController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [led_mapping_dto_1.CreateLedMappingDto]),
    __metadata("design:returntype", void 0)
], LedMappingController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [led_mapping_dto_1.LedMappingQueryDto]),
    __metadata("design:returntype", void 0)
], LedMappingController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LedMappingController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('led/:ledId'),
    __param(0, (0, common_1.Param)('ledId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LedMappingController.prototype, "findByLedId", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, led_mapping_dto_1.UpdateLedMappingDto]),
    __metadata("design:returntype", void 0)
], LedMappingController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LedMappingController.prototype, "remove", null);
exports.LedMappingController = LedMappingController = LedMappingController_1 = __decorate([
    (0, common_1.Controller)('system-config/led-mapping'),
    __metadata("design:paramtypes", [led_mapping_service_1.LedMappingService])
], LedMappingController);
//# sourceMappingURL=led-mapping.controller.js.map