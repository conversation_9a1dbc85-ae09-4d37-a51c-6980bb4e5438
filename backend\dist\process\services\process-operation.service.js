"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessOperationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const process_operation_entity_1 = require("../entities/process-operation.entity");
let ProcessOperationService = class ProcessOperationService {
    constructor(processOperationRepository, dataSource) {
        this.processOperationRepository = processOperationRepository;
        this.dataSource = dataSource;
    }
    async findAll(query = {}) {
        try {
            const page = query.page ? parseInt(query.page) : 1;
            const limit = query.limit ? parseInt(query.limit) : 10;
            const offset = (page - 1) * limit;
            const search = query.search;
            const isActive = query.isActive;
            let whereClause = '';
            const queryParams = [];
            if (search) {
                whereClause = `WHERE (operation.OPERATION_CODE LIKE '%${search}%' OR 
                             operation.OPERATION_NAME LIKE '%${search}%' OR 
                             operation.DESCRIPTION LIKE '%${search}%')`;
            }
            if (isActive !== undefined && isActive !== '') {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `operation.IS_ACTIVE = ${isActive}`;
            }
            const countQuery = `SELECT COUNT(*) AS total FROM PROCESS_OPERATION operation ${whereClause}`;
            const countResult = await this.dataSource.query(countQuery);
            const totalItems = parseInt(countResult[0].TOTAL);
            const sqlQuery = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT operation.OPERATION_ID AS id, 
                   operation.OPERATION_CODE AS code, 
                   operation.OPERATION_NAME AS name, 
                   operation.DESCRIPTION AS description,
                   operation.STANDARD_TIME AS standardTime,
                   operation.OPERATION_TYPE AS type,
                   operation.IS_ACTIVE AS isActive,
                   operation.CREATED_BY AS createdBy,
                   TO_CHAR(operation.CREATION_DATE, 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS creationDate,
                   operation.LAST_UPDATED_BY AS lastUpdatedBy,
                   TO_CHAR(operation.LAST_UPDATE_DATE, 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS lastUpdateDate
            FROM PROCESS_OPERATION operation
            ${whereClause}
            ORDER BY operation.OPERATION_ID DESC
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const operations = await this.dataSource.query(sqlQuery);
            const formattedOperations = operations.map(op => {
                return {
                    id: op.ID,
                    code: op.CODE,
                    name: op.NAME,
                    description: op.DESCRIPTION,
                    standardTime: op.STANDARDTIME,
                    type: op.TYPE,
                    isActive: op.ISACTIVE,
                    createdBy: op.CREATEDBY,
                    creationDate: op.CREATIONDATE,
                    lastUpdatedBy: op.LASTUPDATEDBY,
                    lastUpdateDate: op.LASTUPDATEDATE,
                    rnum: op.RNUM
                };
            });
            return {
                items: formattedOperations,
                meta: {
                    totalItems,
                    itemCount: formattedOperations.length,
                    itemsPerPage: limit,
                    totalPages: Math.ceil(totalItems / limit),
                    currentPage: page,
                },
            };
        }
        catch (error) {
            console.error('获取工序列表失败:', error);
            throw new common_1.InternalServerErrorException('获取工序列表失败');
        }
    }
    async findOne(id) {
        try {
            const sqlQuery = `
        SELECT 
          operation.OPERATION_ID AS id, 
          operation.OPERATION_CODE AS code, 
          operation.OPERATION_NAME AS name, 
          operation.DESCRIPTION AS description,
          operation.STANDARD_TIME AS standardTime,
          operation.OPERATION_TYPE AS type,
          operation.IS_ACTIVE AS isActive,
          operation.CREATED_BY AS createdBy,
          TO_CHAR(operation.CREATION_DATE, 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS creationDate,
          operation.LAST_UPDATED_BY AS lastUpdatedBy,
          TO_CHAR(operation.LAST_UPDATE_DATE, 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS lastUpdateDate
        FROM PROCESS_OPERATION operation
        WHERE operation.OPERATION_ID = :1
      `;
            const operations = await this.dataSource.query(sqlQuery, [id]);
            if (!operations || operations.length === 0) {
                throw new common_1.NotFoundException(`工序ID ${id} 不存在`);
            }
            const op = operations[0];
            const result = {
                id: op.ID,
                code: op.CODE,
                name: op.NAME,
                description: op.DESCRIPTION,
                standardTime: op.STANDARDTIME,
                type: op.TYPE,
                isActive: op.ISACTIVE,
                createdBy: op.CREATEDBY,
                creationDate: op.CREATIONDATE,
                lastUpdatedBy: op.LASTUPDATEDBY,
                lastUpdateDate: op.LASTUPDATEDATE
            };
            return result;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取工序ID ${id} 失败:`, error);
            throw new common_1.InternalServerErrorException(`获取工序失败: ${error.message}`);
        }
    }
    async create(createDto, user) {
        try {
            const operation = this.processOperationRepository.create({
                code: createDto.code,
                name: createDto.name,
                description: createDto.description,
                standardTime: createDto.standardTime,
                type: createDto.type,
                isActive: createDto.isActive ?? 1,
                createdBy: user?.username || 'system',
            });
            const savedOperation = await this.processOperationRepository.save(operation);
            const sqlQuery = `
        SELECT 
          operation.OPERATION_ID AS id, 
          operation.OPERATION_CODE AS code, 
          operation.OPERATION_NAME AS name, 
          operation.DESCRIPTION AS description,
          operation.STANDARD_TIME AS standardTime,
          operation.OPERATION_TYPE AS type,
          operation.IS_ACTIVE AS isActive,
          operation.CREATED_BY AS createdBy,
          TO_CHAR(operation.CREATION_DATE, 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS creationDate,
          operation.LAST_UPDATED_BY AS lastUpdatedBy,
          TO_CHAR(operation.LAST_UPDATE_DATE, 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS lastUpdateDate
        FROM PROCESS_OPERATION operation
        WHERE operation.OPERATION_ID = :1
      `;
            const operations = await this.dataSource.query(sqlQuery, [savedOperation.id]);
            if (operations && operations.length > 0) {
                const op = operations[0];
                const result = {
                    id: op.ID,
                    code: op.CODE,
                    name: op.NAME,
                    description: op.DESCRIPTION,
                    standardTime: op.STANDARDTIME,
                    type: op.TYPE,
                    isActive: op.ISACTIVE,
                    createdBy: op.CREATEDBY,
                    creationDate: op.CREATIONDATE,
                    lastUpdatedBy: op.LASTUPDATEDBY,
                    lastUpdateDate: op.LASTUPDATEDATE
                };
                return result;
            }
            return savedOperation;
        }
        catch (error) {
            console.error('创建工序失败:', error);
            throw new common_1.InternalServerErrorException(`创建工序失败: ${error.message}`);
        }
    }
    async update(id, updateDto, user) {
        try {
            const operation = await this.findOne(id);
            Object.assign(operation, {
                ...updateDto,
                lastUpdatedBy: user?.username || 'system',
                lastUpdateDate: new Date(),
            });
            await this.processOperationRepository.save(operation);
            const sqlQuery = `
        SELECT 
          operation.OPERATION_ID AS id, 
          operation.OPERATION_CODE AS code, 
          operation.OPERATION_NAME AS name, 
          operation.DESCRIPTION AS description,
          operation.STANDARD_TIME AS standardTime,
          operation.OPERATION_TYPE AS type,
          operation.IS_ACTIVE AS isActive,
          operation.CREATED_BY AS createdBy,
          TO_CHAR(operation.CREATION_DATE, 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS creationDate,
          operation.LAST_UPDATED_BY AS lastUpdatedBy,
          TO_CHAR(operation.LAST_UPDATE_DATE, 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS lastUpdateDate
        FROM PROCESS_OPERATION operation
        WHERE operation.OPERATION_ID = :1
      `;
            const operations = await this.dataSource.query(sqlQuery, [id]);
            if (operations && operations.length > 0) {
                const op = operations[0];
                const result = {
                    id: op.ID,
                    code: op.CODE,
                    name: op.NAME,
                    description: op.DESCRIPTION,
                    standardTime: op.STANDARDTIME,
                    type: op.TYPE,
                    isActive: op.ISACTIVE,
                    createdBy: op.CREATEDBY,
                    creationDate: op.CREATIONDATE,
                    lastUpdatedBy: op.LASTUPDATEDBY,
                    lastUpdateDate: op.LASTUPDATEDATE
                };
                return result;
            }
            return operation;
        }
        catch (error) {
            console.error(`更新工序ID ${id} 失败:`, error);
            throw new common_1.InternalServerErrorException(`更新工序失败: ${error.message}`);
        }
    }
    async remove(id) {
        const operation = await this.findOne(id);
        await this.processOperationRepository.remove(operation);
    }
};
exports.ProcessOperationService = ProcessOperationService;
exports.ProcessOperationService = ProcessOperationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(process_operation_entity_1.ProcessOperation)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource])
], ProcessOperationService);
//# sourceMappingURL=process-operation.service.js.map