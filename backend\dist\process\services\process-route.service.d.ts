import { Repository, DataSource } from 'typeorm';
import { ProcessRoute } from '../entities/process-route.entity';
import { RouteOperation } from '../entities/route-operation.entity';
import { CreateProcessRouteDto, UpdateProcessRouteDto } from '../dto/process-route.dto';
export declare class ProcessRouteService {
    private processRouteRepository;
    private routeOperationRepository;
    private dataSource;
    constructor(processRouteRepository: Repository<ProcessRoute>, routeOperationRepository: Repository<RouteOperation>, dataSource: DataSource);
    findAll(query?: any): Promise<{
        items: ProcessRoute[];
        meta: any;
    }>;
    findOne(id: number): Promise<ProcessRoute>;
    getRouteOperations(routeId: number): Promise<RouteOperation[]>;
    create(createDto: CreateProcessRouteDto, user?: any): Promise<ProcessRoute>;
    update(id: number, updateDto: UpdateProcessRouteDto, user?: any): Promise<ProcessRoute>;
    remove(id: number): Promise<void>;
    getRouteOptions(): Promise<{
        items: any[];
    }>;
}
