"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DepartmentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
let DepartmentService = class DepartmentService {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async create(createDepartmentDto) {
        return this.dataSource.transaction(async (manager) => {
            try {
                const existingDept = await manager.query(`SELECT DEPT_ID FROM LED_DEPARTMENT WHERE DEPT_CODE = :1`, [createDepartmentDto.DEPT_CODE]);
                if (existingDept.length > 0) {
                    throw new common_1.BadRequestException(`事业部编码 ${createDepartmentDto.DEPT_CODE} 已存在`);
                }
                let deptLevel = 1;
                let parentPath = '';
                if (createDepartmentDto.PARENT_DEPT_ID) {
                    const parentDeptResults = await manager.query(`SELECT DEPT_PATH, DEPT_LEVEL FROM LED_DEPARTMENT WHERE DEPT_ID = :1`, [createDepartmentDto.PARENT_DEPT_ID]);
                    if (parentDeptResults.length === 0) {
                        throw new common_1.BadRequestException(`父级事业部ID ${createDepartmentDto.PARENT_DEPT_ID} 不存在`);
                    }
                    const parentDept = parentDeptResults[0];
                    deptLevel = (parentDept.DEPT_LEVEL || 1) + 1;
                    parentPath = parentDept.DEPT_PATH;
                }
                const insertQuery = `
          INSERT INTO LED_DEPARTMENT(
            DEPT_CODE, DEPT_NAME, PARENT_DEPT_ID, DEPT_LEVEL, DEPT_PATH,
            MANAGER_USER_ID, DESCRIPTION, SORT_ORDER, IS_ACTIVE, CREATED_BY, CREATION_DATE
          ) VALUES(
            :1, :2, :3, :4, :5, :6, :7, :8, 1, 'system', SYSDATE
          ) RETURNING DEPT_ID INTO :9
        `;
                const outParam = { dir: this.dataSource.driver.oracle.BIND_OUT, type: this.dataSource.driver.oracle.NUMBER };
                const insertResult = await manager.query(insertQuery, [
                    createDepartmentDto.DEPT_CODE,
                    createDepartmentDto.DEPT_NAME,
                    createDepartmentDto.PARENT_DEPT_ID || null,
                    deptLevel,
                    parentPath,
                    createDepartmentDto.MANAGER_USER_ID || null,
                    createDepartmentDto.DESCRIPTION || null,
                    createDepartmentDto.SORT_ORDER || 0,
                    outParam,
                ]);
                const newId = insertResult[0][0];
                const finalPath = parentPath ? `${parentPath}/${newId}` : `/${newId}`;
                await manager.query(`UPDATE LED_DEPARTMENT SET DEPT_PATH = :1 WHERE DEPT_ID = :2`, [finalPath, newId]);
                return this.findOne(newId, manager);
            }
            catch (error) {
                if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                    throw error;
                }
                console.error('创建事业部失败:', error);
                throw new common_1.InternalServerErrorException('创建事业部失败，操作已回滚');
            }
        });
    }
    async findAll(params = {}) {
        try {
            console.log('🔍 部门服务 - 开始查询部门列表');
            console.log('📋 原始参数:', params);
            const page = params.page ? parseInt(params.page, 10) : 1;
            const limit = params.limit ? parseInt(params.limit, 10) : 10;
            const offset = (page - 1) * limit;
            const { search, parentId, deptLevel, isActive } = params;
            console.log('📊 解析后的参数:', { page, limit, offset, search, parentId, deptLevel, isActive });
            let whereClause = 'WHERE 1=1';
            const countQueryParams = [];
            let paramIndex = 1;
            if (search) {
                whereClause += ` AND (d.DEPT_CODE LIKE '%' || :${paramIndex} || '%' OR d.DEPT_NAME LIKE '%' || :${paramIndex} || '%')`;
                countQueryParams.push(search);
                paramIndex++;
            }
            if (parentId !== undefined) {
                if (parentId === 'null' || parentId === null) {
                    whereClause += ` AND d.PARENT_DEPT_ID IS NULL`;
                }
                else {
                    whereClause += ` AND d.PARENT_DEPT_ID = :${paramIndex}`;
                    countQueryParams.push(parentId);
                    paramIndex++;
                }
            }
            if (deptLevel) {
                whereClause += ` AND d.DEPT_LEVEL = :${paramIndex}`;
                countQueryParams.push(parseInt(deptLevel, 10));
                paramIndex++;
            }
            if (isActive !== undefined && isActive !== '') {
                whereClause += ` AND d.IS_ACTIVE = :${paramIndex}`;
                countQueryParams.push(parseInt(isActive, 10));
                paramIndex++;
            }
            const countQuery = `SELECT COUNT(*) AS total FROM LED_DEPARTMENT d ${whereClause}`;
            console.log('🔢 计数查询SQL:', countQuery);
            console.log('🔢 计数查询参数:', countQueryParams);
            const countResult = await this.dataSource.query(countQuery, countQueryParams);
            console.log('🔢 计数查询结果:', countResult);
            const totalItems = countResult[0] ? parseInt(countResult[0].TOTAL, 10) : 0;
            console.log('📊 总记录数:', totalItems);
            const dataQueryParams = [...countQueryParams];
            const dataQuery = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT d.DEPT_ID, d.DEPT_CODE, d.DEPT_NAME, d.PARENT_DEPT_ID,
                   d.DEPT_LEVEL, d.DEPT_PATH, d.MANAGER_USER_ID,
                   d.DESCRIPTION, d.SORT_ORDER, d.IS_ACTIVE,
                   p.DEPT_NAME as PARENT_DEPT_NAME,
                   u.USERNAME as MANAGER_USERNAME
            FROM LED_DEPARTMENT d
            LEFT JOIN LED_DEPARTMENT p ON d.PARENT_DEPT_ID = p.DEPT_ID
            LEFT JOIN LED_USERS u ON d.MANAGER_USER_ID = u.ID
            ${whereClause}
            ORDER BY d.DEPT_LEVEL, d.DEPT_PATH, d.SORT_ORDER, d.DEPT_CODE
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            console.log('📋 数据查询SQL:', dataQuery);
            console.log('📋 数据查询参数:', dataQueryParams);
            const departments = await this.dataSource.query(dataQuery, dataQueryParams);
            console.log('📋 查询到的部门数据:', departments.length, '条记录');
            if (departments.length > 0) {
                console.log('📋 第一条数据示例:', departments[0]);
            }
            return {
                items: departments,
                meta: {
                    totalItems,
                    itemsPerPage: limit,
                    currentPage: page,
                    totalPages: Math.ceil(totalItems / limit),
                },
            };
        }
        catch (error) {
            console.error('获取事业部列表失败:', error);
            throw new common_1.InternalServerErrorException('获取事业部列表失败');
        }
    }
    async findOne(id, manager) {
        const queryRunner = manager || this.dataSource;
        try {
            const departments = await queryRunner.query(`SELECT d.*, 
                p.DEPT_NAME as PARENT_DEPT_NAME,
                u.USERNAME as MANAGER_USERNAME
         FROM LED_DEPARTMENT d
         LEFT JOIN LED_DEPARTMENT p ON d.PARENT_DEPT_ID = p.DEPT_ID
         LEFT JOIN LED_USERS u ON d.MANAGER_USER_ID = u.ID
         WHERE d.DEPT_ID = :1`, [id]);
            if (!departments || departments.length === 0) {
                throw new common_1.NotFoundException(`ID为${id}的事业部不存在`);
            }
            return departments[0];
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取ID为${id}的事业部失败:`, error);
            throw new common_1.InternalServerErrorException(`获取ID为${id}的事业部失败`);
        }
    }
    async update(id, updateDepartmentDto) {
        return this.dataSource.transaction(async (manager) => {
            try {
                const currentDept = await this.findOne(id, manager);
                if (updateDepartmentDto.DEPT_CODE && updateDepartmentDto.DEPT_CODE !== currentDept.DEPT_CODE) {
                    const existingDept = await manager.query(`SELECT DEPT_ID FROM LED_DEPARTMENT WHERE DEPT_CODE = :1 AND DEPT_ID != :2`, [updateDepartmentDto.DEPT_CODE, id]);
                    if (existingDept.length > 0) {
                        throw new common_1.BadRequestException(`事业部编码 ${updateDepartmentDto.DEPT_CODE} 已被其他事业部使用`);
                    }
                }
                const needsPathUpdate = updateDepartmentDto.PARENT_DEPT_ID !== undefined &&
                    updateDepartmentDto.PARENT_DEPT_ID !== currentDept.PARENT_DEPT_ID;
                if (needsPathUpdate) {
                    const newParentId = updateDepartmentDto.PARENT_DEPT_ID;
                    if (newParentId) {
                        if (newParentId === id) {
                            throw new common_1.BadRequestException('不能将自己设置为父级事业部');
                        }
                        const newParentDept = await this.findOne(newParentId, manager);
                        if (newParentDept.DEPT_PATH && newParentDept.DEPT_PATH.split('/').includes(id.toString())) {
                            throw new common_1.BadRequestException('不能将子事业部设置为父级事业部');
                        }
                    }
                    await this.updateChildren(id, newParentId, manager);
                }
                const updateQueryParts = [];
                const queryParams = [];
                let paramIndex = 1;
                const fieldsToUpdate = { ...updateDepartmentDto };
                const allowedFields = ['DEPT_CODE', 'DEPT_NAME', 'MANAGER_USER_ID', 'DESCRIPTION', 'SORT_ORDER', 'IS_ACTIVE'];
                for (const key of allowedFields) {
                    if (fieldsToUpdate[key] !== undefined) {
                        updateQueryParts.push(`${key} = :${paramIndex}`);
                        queryParams.push(fieldsToUpdate[key]);
                        paramIndex++;
                    }
                }
                if (needsPathUpdate) {
                    updateQueryParts.push(`PARENT_DEPT_ID = :${paramIndex}`);
                    queryParams.push(updateDepartmentDto.PARENT_DEPT_ID);
                    paramIndex++;
                }
                if (updateQueryParts.length > 0) {
                    updateQueryParts.push(`LAST_UPDATED_BY = 'system_update'`);
                    updateQueryParts.push(`LAST_UPDATE_DATE = SYSDATE`);
                    const updateQuery = `UPDATE LED_DEPARTMENT SET ${updateQueryParts.join(', ')} WHERE DEPT_ID = :${paramIndex}`;
                    queryParams.push(id);
                    await manager.query(updateQuery, queryParams);
                }
                return this.findOne(id, manager);
            }
            catch (error) {
                if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                    throw error;
                }
                console.error(`更新ID为${id}的事业部失败:`, error);
                throw new common_1.InternalServerErrorException(`更新ID为${id}的事业部失败，操作已回滚`);
            }
        });
    }
    async updateChildren(parentId, newParentDeptId, manager) {
        let newParentPath = '';
        let newParentLevel = 0;
        if (newParentDeptId) {
            const newParentDept = await this.findOne(newParentDeptId, manager);
            newParentPath = newParentDept.DEPT_PATH;
            newParentLevel = newParentDept.DEPT_LEVEL;
        }
        const newPath = newParentPath ? `${newParentPath}/${parentId}` : `/${parentId}`;
        const newLevel = newParentLevel + 1;
        await manager.query(`UPDATE LED_DEPARTMENT SET DEPT_PATH = :1, DEPT_LEVEL = :2 WHERE DEPT_ID = :3`, [newPath, newLevel, parentId]);
        const children = await manager.query(`SELECT DEPT_ID FROM LED_DEPARTMENT WHERE PARENT_DEPT_ID = :1`, [parentId]);
        for (const child of children) {
            await this.updateChildren(child.DEPT_ID, parentId, manager);
        }
    }
    async remove(id) {
        return this.dataSource.transaction(async (manager) => {
            const children = await manager.query(`SELECT DEPT_ID FROM LED_DEPARTMENT WHERE PARENT_DEPT_ID = :1`, [id]);
            if (children.length > 0) {
                throw new common_1.BadRequestException('该事业部下存在子事业部，无法删除');
            }
            const users = await manager.query(`SELECT ID FROM LED_USERS WHERE DEPT_ID = :1`, [id]);
            if (users.length > 0) {
                throw new common_1.BadRequestException('该事业部下存在用户，无法删除');
            }
            const devices = await manager.query(`SELECT LED_ID FROM LED_DEVICE_INFO WHERE DEPT_ID = :1`, [id]);
            if (devices.length > 0) {
                throw new common_1.BadRequestException('该事业部下存在设备，无法删除');
            }
            const result = await manager.query(`DELETE FROM LED_DEPARTMENT WHERE DEPT_ID = :1`, [id]);
            if (result.affected === 0) {
                throw new common_1.NotFoundException(`ID为${id}的事业部不存在`);
            }
            return { success: true, message: '事业部删除成功' };
        });
    }
    async getTree() {
        try {
            const allDepartments = await this.dataSource.query(`
        SELECT d.DEPT_ID, d.DEPT_NAME, d.PARENT_DEPT_ID, d.SORT_ORDER, d.DEPT_CODE,
               d.DEPT_LEVEL, d.DESCRIPTION, d.IS_ACTIVE, d.MANAGER_USER_ID,
               u.USERNAME as MANAGER_USERNAME
        FROM LED_DEPARTMENT d
        LEFT JOIN LED_USERS u ON d.MANAGER_USER_ID = u.ID
        ORDER BY d.SORT_ORDER, d.DEPT_CODE
      `);
            const map = new Map();
            const tree = [];
            allDepartments.forEach(dept => {
                map.set(dept.DEPT_ID, { ...dept, children: [] });
            });
            allDepartments.forEach(dept => {
                if (dept.PARENT_DEPT_ID && map.has(dept.PARENT_DEPT_ID)) {
                    map.get(dept.PARENT_DEPT_ID).children.push(map.get(dept.DEPT_ID));
                }
                else {
                    tree.push(map.get(dept.DEPT_ID));
                }
            });
            tree.sort((a, b) => (a.SORT_ORDER || 0) - (b.SORT_ORDER || 0) || a.DEPT_CODE.localeCompare(b.DEPT_CODE));
            return tree;
        }
        catch (error) {
            console.error('获取事业部树形结构失败:', error);
            throw new common_1.InternalServerErrorException('获取事业部树形结构失败');
        }
    }
};
exports.DepartmentService = DepartmentService;
exports.DepartmentService = DepartmentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], DepartmentService);
//# sourceMappingURL=department.service.js.map