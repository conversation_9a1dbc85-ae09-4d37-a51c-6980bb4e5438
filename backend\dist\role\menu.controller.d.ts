import { MenuService } from './menu.service';
export declare class MenuController {
    private readonly menuService;
    constructor(menuService: MenuService);
    findAll(query: any): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/menu.entity").Menu[];
        meta: any;
    } | {
        success: boolean;
        message: any;
        data: any[];
        meta: {
            totalItems: number;
            itemsPerPage: number;
            currentPage: number;
            totalPages: number;
        };
    }>;
    getTree(): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/menu.entity").Menu[];
    } | {
        success: boolean;
        message: any;
        data: any[];
    }>;
    getMenuTypes(): Promise<{
        success: boolean;
        message: string;
        data: any[];
    } | {
        success: boolean;
        message: any;
        data: any[];
    }>;
    getUserMenus(userId: number): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/menu.entity").Menu[];
    } | {
        success: boolean;
        message: any;
        data: any[];
    }>;
    findOne(id: number): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/menu.entity").Menu;
    } | {
        success: boolean;
        message: any;
        data: any;
    }>;
}
