export declare class CreateProductionLineDto {
    code: string;
    name: string;
    description?: string;
    routeId?: number;
    manager?: string;
    areaCode?: string;
    isActive?: number;
}
export declare class UpdateProductionLineDto {
    code?: string;
    name?: string;
    description?: string;
    routeId?: number;
    manager?: string;
    areaCode?: string;
    isActive?: number;
}
export declare class ProductionLineResponseDto {
    id: number;
    code: string;
    name: string;
    description: string;
    routeId: number;
    manager: string;
    areaCode: string;
    isActive: number;
    createdBy: string;
    creationDate: Date;
    lastUpdatedBy: string;
    lastUpdateDate: Date;
    route?: any;
}
