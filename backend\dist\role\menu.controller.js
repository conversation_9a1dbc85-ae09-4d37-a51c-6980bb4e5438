"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MenuController = void 0;
const common_1 = require("@nestjs/common");
const menu_service_1 = require("./menu.service");
let MenuController = class MenuController {
    constructor(menuService) {
        this.menuService = menuService;
    }
    async findAll(query) {
        try {
            const result = await this.menuService.findAll(query);
            return {
                success: true,
                message: '获取菜单列表成功',
                data: result.items,
                meta: result.meta
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取菜单列表失败',
                data: [],
                meta: {
                    totalItems: 0,
                    itemsPerPage: 50,
                    currentPage: 1,
                    totalPages: 0
                }
            };
        }
    }
    async getTree() {
        try {
            const tree = await this.menuService.getTree();
            return {
                success: true,
                message: '获取菜单树形结构成功',
                data: tree
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取菜单树形结构失败',
                data: []
            };
        }
    }
    async getMenuTypes() {
        try {
            const types = await this.menuService.getMenuTypes();
            return {
                success: true,
                message: '获取菜单类型成功',
                data: types
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取菜单类型失败',
                data: []
            };
        }
    }
    async getUserMenus(userId) {
        try {
            const menus = await this.menuService.getUserMenus(userId);
            return {
                success: true,
                message: '获取用户菜单成功',
                data: menus
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取用户菜单失败',
                data: []
            };
        }
    }
    async findOne(id) {
        try {
            const menu = await this.menuService.findOne(id);
            return {
                success: true,
                message: '获取菜单详情成功',
                data: menu
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取菜单详情失败',
                data: null
            };
        }
    }
};
exports.MenuController = MenuController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MenuController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('tree'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MenuController.prototype, "getTree", null);
__decorate([
    (0, common_1.Get)('types'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MenuController.prototype, "getMenuTypes", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MenuController.prototype, "getUserMenus", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MenuController.prototype, "findOne", null);
exports.MenuController = MenuController = __decorate([
    (0, common_1.Controller)('menus'),
    __metadata("design:paramtypes", [menu_service_1.MenuService])
], MenuController);
//# sourceMappingURL=menu.controller.js.map