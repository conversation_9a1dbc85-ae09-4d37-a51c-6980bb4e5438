import { LedConcentratorService } from '../services/led-concentrator.service';
import { CreateConcentratorDto, UpdateConcentratorDto, ConcentratorQueryDto } from '../dto/led-concentrator.dto';
export declare class LedConcentratorController {
    private readonly ledConcentratorService;
    private readonly logger;
    constructor(ledConcentratorService: LedConcentratorService);
    create(createDto: CreateConcentratorDto): Promise<import("../entities/led-concentrator.entity").LedConcentrator>;
    findAll(query: ConcentratorQueryDto): Promise<{
        items: import("../entities/led-concentrator.entity").LedConcentrator[];
        meta: {
            total: number;
            currentPage?: number;
            totalPages?: number;
            itemsPerPage?: number;
        };
    }>;
    findOne(id: string): Promise<import("../entities/led-concentrator.entity").LedConcentrator>;
    update(id: string, updateDto: UpdateConcentratorDto): Promise<import("../entities/led-concentrator.entity").LedConcentrator>;
    remove(id: string): Promise<void>;
    testConnection(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
