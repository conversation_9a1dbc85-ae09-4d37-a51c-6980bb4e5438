{"version": 3, "file": "user-permission.service.js", "sourceRoot": "", "sources": ["../../src/role/user-permission.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAkH;AAClH,qCAAqC;AAK9B,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YAAoB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAK9C,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,iBAAoC;QACxE,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACtC,wCAAwC,EACxC,CAAC,MAAM,CAAC,CACT,CAAC;YACF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC;YAGD,IAAI,iBAAiB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzC,MAAM,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACpD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACvC,kDAAkD,OAAO,qBAAqB,CAC/E,CAAC;gBAEF,IAAI,KAAK,CAAC,MAAM,KAAK,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;oBACtD,MAAM,IAAI,4BAAmB,CAAC,eAAe,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;YAGD,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;gBAC7B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACtC,yEAAyE,EACzE,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAC3B,CAAC;gBACF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtB,MAAM,IAAI,4BAAmB,CAAC,eAAe,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;YAGD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACzB,8CAA8C,EAC9C,CAAC,MAAM,CAAC,CACT,CAAC;YAGF,IAAI,iBAAiB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzC,MAAM,YAAY,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAC1D,IAAI,MAAM,KAAK,MAAM,KAAK,iBAAiB,CAAC,MAAM,IAAI,MAAM,yBAAyB,CACtF,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAEZ,MAAM,WAAW,GAAG;;mBAET,YAAY;SACtB,CAAC;gBAEF,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,0BAAiB,IAAI,KAAK,YAAY,4BAAmB,EAAE,CAAC;gBAC/E,MAAM,KAAK,CAAC;YACd,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,MAAM,MAAM,SAAS,EAAE,KAAK,CAAC,CAAC;YAC5C,MAAM,IAAI,qCAA4B,CAAC,UAAU,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACtC,wCAAwC,EACxC,CAAC,MAAM,CAAC,CACT,CAAC;YACF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC;YAED,MAAM,KAAK,GAAG;;;;;;;;OAQb,CAAC;YAEF,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,0BAAiB,EAAE,CAAC;gBACvC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,OAAO,MAAM,UAAU,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,IAAI,qCAA4B,CAAC,YAAY,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,MAAc,EAAE,yBAAoD;QAChG,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACtC,wCAAwC,EACxC,CAAC,MAAM,CAAC,CACT,CAAC;YACF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC;YAGD,IAAI,yBAAyB,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrD,MAAM,MAAM,GAAG,yBAAyB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACxF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACzC,uDAAuD,MAAM,GAAG,CACjE,CAAC;gBAEF,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;gBACzD,MAAM,eAAe,GAAG,yBAAyB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAChF,MAAM,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEjF,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7B,MAAM,IAAI,4BAAmB,CAAC,eAAe,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC3E,CAAC;YACH,CAAC;YAGD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACzB,2DAA2D,EAC3D,CAAC,MAAM,CAAC,CACT,CAAC;YAGF,IAAI,yBAAyB,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrD,MAAM,YAAY,GAAG,yBAAyB,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAC1E,IAAI,MAAM,MAAM,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,cAAc,0BAA0B,CAC3F,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAEZ,MAAM,WAAW,GAAG;;mBAET,YAAY;SACtB,CAAC;gBAEF,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,0BAAiB,IAAI,KAAK,YAAY,4BAAmB,EAAE,CAAC;gBAC/E,MAAM,KAAK,CAAC;YACd,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,MAAM,MAAM,WAAW,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,IAAI,qCAA4B,CAAC,UAAU,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAAC,MAAc;QAC3C,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACtC,wCAAwC,EACxC,CAAC,MAAM,CAAC,CACT,CAAC;YACF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC;YAED,MAAM,KAAK,GAAG;;;;;;;;OAQb,CAAC;YAEF,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,0BAAiB,EAAE,CAAC;gBACvC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,OAAO,MAAM,UAAU,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,IAAI,qCAA4B,CAAC,YAAY,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAAC,MAAc,EAAE,cAAuB;QACpE,IAAI,CAAC;YACH,IAAI,WAAW,GAAG,8CAA8C,CAAC;YACjE,MAAM,WAAW,GAAU,CAAC,MAAM,CAAC,CAAC;YAEpC,IAAI,cAAc,EAAE,CAAC;gBACnB,WAAW,IAAI,+BAA+B,CAAC;gBAC/C,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,CAAC;YAED,MAAM,KAAK,GAAG;;;;;;UAMV,WAAW;;OAEd,CAAC;YAEF,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,OAAO,MAAM,aAAa,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,IAAI,qCAA4B,CAAC,eAAe,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,yBAAyB,CAAC,MAAc,EAAE,KAAa,EAAE,cAAsB;QACnF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACxC;;uFAE+E,EAC/E,CAAC,MAAM,EAAE,KAAK,EAAE,cAAc,CAAC,CAChC,CAAC;YAEF,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,OAAO,MAAM,MAAM,KAAK,IAAI,cAAc,OAAO,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAAC,MAAc;QAC3C,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACtC,kEAAkE,EAClE,CAAC,MAAM,CAAC,CACT,CAAC;YACF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAGzB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAG9C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACvC;;;;;4CAKoC,EACpC,CAAC,MAAM,CAAC,CACT,CAAC;YAGF,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;YAGtE,IAAI,UAAU,GAAG,IAAI,CAAC;YACtB,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAC5C,iDAAiD,EACjD,CAAC,QAAQ,CAAC,OAAO,CAAC,CACnB,CAAC;gBACF,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;YACrC,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,UAAU;gBACV,KAAK;gBACL,KAAK;gBACL,iBAAiB;gBACjB,OAAO,EAAE;oBACP,SAAS,EAAE,KAAK,CAAC,MAAM;oBACvB,SAAS,EAAE,KAAK,CAAC,MAAM;oBACvB,WAAW,EAAE,iBAAiB,CAAC,MAAM;oBACrC,eAAe,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;iBACpF;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,0BAAiB,EAAE,CAAC;gBACvC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,OAAO,MAAM,UAAU,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,IAAI,qCAA4B,CAAC,YAAY,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB;QACtB,OAAO;YACL,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE;YAC5D,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE;YAClE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE;SACjE,CAAC;IACJ,CAAC;CACF,CAAA;AA9TY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;qCAEqB,oBAAU;GAD/B,qBAAqB,CA8TjC"}