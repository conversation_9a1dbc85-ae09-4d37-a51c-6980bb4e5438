"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessOperationResponseDto = exports.UpdateProcessOperationDto = exports.CreateProcessOperationDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateProcessOperationDto {
}
exports.CreateProcessOperationDto = CreateProcessOperationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序编码', example: 'OP001' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateProcessOperationDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序名称', example: '组装' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateProcessOperationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序描述', required: false, example: '组装产品零部件' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateProcessOperationDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标准作业时间(分钟)', required: false, example: 10 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProcessOperationDto.prototype, "standardTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序类型', required: false, example: '组装' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateProcessOperationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活', required: false, default: 1, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateProcessOperationDto.prototype, "isActive", void 0);
class UpdateProcessOperationDto {
}
exports.UpdateProcessOperationDto = UpdateProcessOperationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序编码', required: false, example: 'OP001' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], UpdateProcessOperationDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序名称', required: false, example: '组装' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], UpdateProcessOperationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序描述', required: false, example: '组装产品零部件' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], UpdateProcessOperationDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标准作业时间(分钟)', required: false, example: 10 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateProcessOperationDto.prototype, "standardTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序类型', required: false, example: '组装' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], UpdateProcessOperationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活', required: false, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateProcessOperationDto.prototype, "isActive", void 0);
class ProcessOperationResponseDto {
}
exports.ProcessOperationResponseDto = ProcessOperationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序ID' }),
    __metadata("design:type", Number)
], ProcessOperationResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序编码' }),
    __metadata("design:type", String)
], ProcessOperationResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序名称' }),
    __metadata("design:type", String)
], ProcessOperationResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序描述' }),
    __metadata("design:type", String)
], ProcessOperationResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标准作业时间(分钟)' }),
    __metadata("design:type", Number)
], ProcessOperationResponseDto.prototype, "standardTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序类型' }),
    __metadata("design:type", String)
], ProcessOperationResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活' }),
    __metadata("design:type", Number)
], ProcessOperationResponseDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人' }),
    __metadata("design:type", String)
], ProcessOperationResponseDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建日期' }),
    __metadata("design:type", Date)
], ProcessOperationResponseDto.prototype, "creationDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新人' }),
    __metadata("design:type", String)
], ProcessOperationResponseDto.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新日期' }),
    __metadata("design:type", Date)
], ProcessOperationResponseDto.prototype, "lastUpdateDate", void 0);
//# sourceMappingURL=process-operation.dto.js.map