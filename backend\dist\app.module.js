"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const led_device_module_1 = require("./led_device/led_device.module");
const led_plan_module_1 = require("./led_plan/led_plan.module");
const led_data_module_1 = require("./led_data/led_data.module");
const process_module_1 = require("./process/process.module");
const system_config_module_1 = require("./system_config/system-config.module");
const manual_dispatch_module_1 = require("./manual_dispatch/manual-dispatch.module");
const department_module_1 = require("./department/department.module");
const role_module_1 = require("./role/role.module");
const led_user_entity_1 = require("./auth/entities/led-user.entity");
const led_device_entity_1 = require("./led_device/entities/led-device.entity");
const led_plan_entity_1 = require("./led_plan/entities/led-plan.entity");
const led_data_entity_1 = require("./led_data/entities/led-data.entity");
const process_operation_entity_1 = require("./process/entities/process-operation.entity");
const process_route_entity_1 = require("./process/entities/process-route.entity");
const route_operation_entity_1 = require("./process/entities/route-operation.entity");
const production_line_entity_1 = require("./process/entities/production-line.entity");
const system_config_entity_1 = require("./system_config/entities/system-config.entity");
const led_concentrator_entity_1 = require("./system_config/entities/led-concentrator.entity");
const concentrator_led_mapping_entity_1 = require("./system_config/entities/concentrator-led-mapping.entity");
const department_entity_1 = require("./department/entities/department.entity");
const role_entity_1 = require("./role/entities/role.entity");
const menu_entity_1 = require("./role/entities/menu.entity");
const role_menu_entity_1 = require("./role/entities/role-menu.entity");
const user_role_entity_1 = require("./role/entities/user-role.entity");
const user_device_permission_entity_1 = require("./role/entities/user-device-permission.entity");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => {
                    const dbHost = configService.get('DB_HOST');
                    const dbPort = configService.get('DB_PORT');
                    const dbService = configService.get('DB_DATABASE');
                    return {
                        type: 'oracle',
                        host: dbHost,
                        port: dbPort,
                        username: configService.get('DB_USERNAME'),
                        password: configService.get('DB_PASSWORD'),
                        serviceName: dbService,
                        entities: [
                            led_user_entity_1.LedUser,
                            led_device_entity_1.LedDevice,
                            led_plan_entity_1.LedPlan,
                            led_data_entity_1.LedData,
                            process_operation_entity_1.ProcessOperation,
                            process_route_entity_1.ProcessRoute,
                            route_operation_entity_1.RouteOperation,
                            production_line_entity_1.ProductionLine,
                            system_config_entity_1.SystemConfig,
                            led_concentrator_entity_1.LedConcentrator,
                            concentrator_led_mapping_entity_1.ConcentratorLedMapping,
                            department_entity_1.Department,
                            role_entity_1.Role,
                            menu_entity_1.Menu,
                            role_menu_entity_1.RoleMenu,
                            user_role_entity_1.UserRole,
                            user_device_permission_entity_1.UserDevicePermission
                        ],
                        synchronize: false,
                        logging: configService.get('NODE_ENV') === 'development' ? ['query', 'error'] : ['error'],
                    };
                },
            }),
            auth_module_1.AuthModule,
            led_device_module_1.LedDeviceModule,
            led_plan_module_1.LedPlanModule,
            led_data_module_1.LedDataModule,
            process_module_1.ProcessModule,
            system_config_module_1.SystemConfigModule,
            manual_dispatch_module_1.ManualDispatchModule,
            department_module_1.DepartmentModule,
            role_module_1.RoleModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map