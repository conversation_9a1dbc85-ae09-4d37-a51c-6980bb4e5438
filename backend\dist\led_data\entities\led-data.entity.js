"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedData = void 0;
const typeorm_1 = require("typeorm");
let LedData = class LedData {
};
exports.LedData = LedData;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], LedData.prototype, "ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_ID', nullable: false }),
    __metadata("design:type", String)
], LedData.prototype, "LED_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_PLAN_DATA', nullable: true, type: 'number' }),
    __metadata("design:type", Number)
], LedData.prototype, "LED_PLAN_DATA", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_REAL_DATA', nullable: true, type: 'number' }),
    __metadata("design:type", Number)
], LedData.prototype, "LED_REAL_DATA", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATE_TIME', nullable: true, type: 'timestamp' }),
    __metadata("design:type", Date)
], LedData.prototype, "CREATE_TIME", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ACCUMULATED_VALUE', nullable: true, type: 'number' }),
    __metadata("design:type", Number)
], LedData.prototype, "ACCUMULATED_VALUE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'WORK_TYPE', nullable: true, type: 'number' }),
    __metadata("design:type", Number)
], LedData.prototype, "WORK_TYPE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'WORK_DATE', nullable: true, type: 'date' }),
    __metadata("design:type", Date)
], LedData.prototype, "WORK_DATE", void 0);
exports.LedData = LedData = __decorate([
    (0, typeorm_1.Entity)('LED_DATA_INFO')
], LedData);
//# sourceMappingURL=led-data.entity.js.map