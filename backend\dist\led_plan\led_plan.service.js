"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedPlanService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const XLSX = __importStar(require("xlsx"));
let LedPlanService = class LedPlanService {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async create(createPlanDto) {
        try {
            const result = await this.dataSource.query(`INSERT INTO LED_PLAN_INFO(
          ID, LED_ID, LED_PLAN_DATA, PLAN_DATE, PLAN_TYPE, WORK_TYPE, PUSH_STATUS
        ) VALUES(
          SQL_LED_PLAN_INFO_ID.NEXTVAL, :1, :2, TO_DATE(:3, 'YYYY-MM-DD'), :4, :5, :6
        ) RETURNING ID INTO :7`, [
                createPlanDto.LED_ID,
                createPlanDto.LED_PLAN_DATA || null,
                createPlanDto.PLAN_DATE || null,
                createPlanDto.PLAN_TYPE || null,
                createPlanDto.WORK_TYPE || null,
                createPlanDto.PUSH_STATUS || '0',
                { dir: this.dataSource.driver.oracle.BIND_OUT, type: this.dataSource.driver.oracle.NUMBER }
            ]);
            const newId = result[0];
            const plans = await this.dataSource.query(`SELECT * FROM LED_PLAN_INFO WHERE ID = ${newId}`);
            if (!plans || plans.length === 0) {
                throw new common_1.NotFoundException(`ID为${newId}的LED计划不存在`);
            }
            return plans[0];
        }
        catch (error) {
            console.error('创建LED计划失败:', error);
            throw new common_1.InternalServerErrorException('创建LED计划失败');
        }
    }
    async findAll(params = {}) {
        try {
            const page = params.page ? parseInt(params.page) : 1;
            const limit = params.limit ? parseInt(params.limit) : 10;
            const offset = (page - 1) * limit;
            const search = params.search;
            let whereClause = '';
            const queryParams = [];
            let paramIndex = 1;
            if (search) {
                whereClause = `WHERE p.LED_ID LIKE '%' || :${paramIndex} || '%'`;
                queryParams.push(search);
                paramIndex++;
            }
            if (params.startDate && params.endDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `p.PLAN_DATE BETWEEN TO_DATE(:${paramIndex}, 'YYYY-MM-DD') AND TO_DATE(:${paramIndex + 1}, 'YYYY-MM-DD')`;
                queryParams.push(params.startDate);
                queryParams.push(params.endDate);
                paramIndex += 2;
            }
            else if (params.startDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `p.PLAN_DATE >= TO_DATE(:${paramIndex}, 'YYYY-MM-DD')`;
                queryParams.push(params.startDate);
                paramIndex++;
            }
            else if (params.endDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `p.PLAN_DATE <= TO_DATE(:${paramIndex}, 'YYYY-MM-DD')`;
                queryParams.push(params.endDate);
                paramIndex++;
            }
            if (params.ledId) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `p.LED_ID = :${paramIndex}`;
                queryParams.push(params.ledId);
                paramIndex++;
            }
            if (params.planType) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `p.PLAN_TYPE = :${paramIndex}`;
                queryParams.push(params.planType);
                paramIndex++;
            }
            if (params.workType) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `p.WORK_TYPE = :${paramIndex}`;
                queryParams.push(params.workType);
                paramIndex++;
            }
            if (params.pushStatus) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `p.PUSH_STATUS = :${paramIndex}`;
                queryParams.push(params.pushStatus);
                paramIndex++;
            }
            const countQuery = `SELECT COUNT(*) AS total FROM LED_PLAN_INFO p ${whereClause}`;
            const countResult = await this.dataSource.query(countQuery, queryParams);
            const totalItems = parseInt(countResult[0].TOTAL);
            const query = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT p.ID, p.LED_ID, p.LED_PLAN_DATA, 
                  TO_CHAR(p.PLAN_DATE, 'YYYY-MM-DD') AS PLAN_DATE, 
                  p.PLAN_TYPE, p.WORK_TYPE, p.PUSH_STATUS, 
                  d.LED_NAME 
            FROM LED_PLAN_INFO p
            LEFT JOIN LED_DEVICE_INFO d ON p.LED_ID = d.LED_ID
            ${whereClause}
            ORDER BY p.ID DESC
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const plans = await this.dataSource.query(query, queryParams);
            return {
                items: plans,
                meta: {
                    totalItems,
                    itemsPerPage: limit,
                    currentPage: page,
                    totalPages: Math.ceil(totalItems / limit)
                }
            };
        }
        catch (error) {
            console.error('获取LED计划列表失败:', error);
            throw new common_1.InternalServerErrorException('获取LED计划列表失败');
        }
    }
    async findOne(id) {
        try {
            const plans = await this.dataSource.query(`SELECT p.ID, p.LED_ID, p.LED_PLAN_DATA, 
                TO_CHAR(p.PLAN_DATE, 'YYYY-MM-DD') AS PLAN_DATE, 
                p.PLAN_TYPE, p.WORK_TYPE, p.PUSH_STATUS, 
                d.LED_NAME 
         FROM LED_PLAN_INFO p
         LEFT JOIN LED_DEVICE_INFO d ON p.LED_ID = d.LED_ID
         WHERE p.ID = ${id}`);
            if (!plans || plans.length === 0) {
                throw new common_1.NotFoundException(`ID为${id}的LED计划不存在`);
            }
            return plans[0];
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取ID为${id}的LED计划失败:`, error);
            throw new common_1.InternalServerErrorException(`获取ID为${id}的LED计划失败`);
        }
    }
    async update(id, updatePlanDto) {
        try {
            await this.findOne(id);
            let updateQuery = 'UPDATE LED_PLAN_INFO SET ';
            const updateValues = [];
            let paramIndex = 1;
            if (updatePlanDto.LED_ID !== undefined) {
                updateQuery += `LED_ID = :${paramIndex}, `;
                updateValues.push(updatePlanDto.LED_ID);
                paramIndex++;
            }
            if (updatePlanDto.LED_PLAN_DATA !== undefined) {
                updateQuery += `LED_PLAN_DATA = :${paramIndex}, `;
                updateValues.push(updatePlanDto.LED_PLAN_DATA);
                paramIndex++;
            }
            if (updatePlanDto.PLAN_DATE !== undefined) {
                updateQuery += `PLAN_DATE = TO_DATE(:${paramIndex}, 'YYYY-MM-DD'), `;
                updateValues.push(updatePlanDto.PLAN_DATE);
                paramIndex++;
            }
            if (updatePlanDto.PLAN_TYPE !== undefined) {
                updateQuery += `PLAN_TYPE = :${paramIndex}, `;
                updateValues.push(updatePlanDto.PLAN_TYPE);
                paramIndex++;
            }
            if (updatePlanDto.WORK_TYPE !== undefined) {
                updateQuery += `WORK_TYPE = :${paramIndex}, `;
                updateValues.push(updatePlanDto.WORK_TYPE);
                paramIndex++;
            }
            if (updatePlanDto.PUSH_STATUS !== undefined) {
                updateQuery += `PUSH_STATUS = :${paramIndex}, `;
                updateValues.push(updatePlanDto.PUSH_STATUS);
                paramIndex++;
            }
            updateQuery = updateQuery.slice(0, -2);
            updateQuery += ` WHERE ID = :${paramIndex}`;
            updateValues.push(id);
            await this.dataSource.query(updateQuery, updateValues);
            return this.findOne(id);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`更新ID为${id}的LED计划失败:`, error);
            throw new common_1.InternalServerErrorException(`更新ID为${id}的LED计划失败`);
        }
    }
    async remove(id) {
        try {
            await this.findOne(id);
            await this.dataSource.query(`DELETE FROM LED_PLAN_INFO WHERE ID = ${id}`);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`删除ID为${id}的LED计划失败:`, error);
            throw new common_1.InternalServerErrorException(`删除ID为${id}的LED计划失败`);
        }
    }
    async pushPlan(id) {
        try {
            const plan = await this.findOne(id);
            await this.dataSource.query(`UPDATE LED_PLAN_INFO SET PUSH_STATUS = '1' WHERE ID = ${id}`);
            return this.findOne(id);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`推送ID为${id}的LED计划失败:`, error);
            throw new common_1.InternalServerErrorException(`推送ID为${id}的LED计划失败`);
        }
    }
    async getLedOptions() {
        try {
            const devices = await this.dataSource.query(`SELECT LED_ID, LED_NAME FROM LED_DEVICE_INFO ORDER BY ID DESC`);
            return devices.map(device => ({
                value: device.LED_ID,
                label: device.LED_NAME
            }));
        }
        catch (error) {
            console.error('获取LED设备选项失败:', error);
            throw new common_1.InternalServerErrorException('获取LED设备选项失败');
        }
    }
    async importPlansFromExcel(file) {
        try {
            if (!file) {
                throw new common_1.BadRequestException('未上传文件');
            }
            console.log('接收到文件:', file.originalname, '大小:', file.size, '字节');
            if (!file.buffer || file.buffer.length === 0) {
                throw new common_1.BadRequestException('文件内容为空');
            }
            console.log('文件缓冲区长度:', file.buffer.length);
            const fileType = file.originalname.split('.').pop().toLowerCase();
            if (fileType !== 'xlsx' && fileType !== 'xls') {
                throw new common_1.BadRequestException('仅支持Excel文件(.xlsx, .xls)');
            }
            const result = {
                success: 0,
                failed: 0,
                errors: []
            };
            try {
                const workbook = XLSX.read(file.buffer, { type: 'buffer' });
                if (!workbook) {
                    throw new common_1.BadRequestException('无法解析Excel文件');
                }
                console.log('Excel工作簿信息:', {
                    SheetNames: workbook.SheetNames,
                    SheetCount: workbook.SheetNames.length
                });
                if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                    throw new common_1.BadRequestException('Excel文件中未找到工作表');
                }
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                if (!worksheet) {
                    throw new common_1.BadRequestException(`未找到工作表: ${sheetName}`);
                }
                let data = XLSX.utils.sheet_to_json(worksheet, { header: 'A' });
                if (!data || data.length === 0) {
                    throw new common_1.BadRequestException('Excel文件中没有数据');
                }
                const hasHeaderRow = data.length > 0 &&
                    (data[0]['A'] === '设备' || data[0]['B'] === '计划数量' ||
                        data[0]['C'] === '计划日期' || data[0]['D'] === '白夜班');
                const startIndex = hasHeaderRow ? 1 : 0;
                console.log('Excel解析结果，共', data.length, '行数据，是否有标题行:', hasHeaderRow);
                console.log('第一行数据示例:', JSON.stringify(data[startIndex]));
                for (let i = startIndex; i < data.length; i++) {
                    const row = data[i];
                    try {
                        const led_id = row['A'] || '';
                        const led_plan_data = row['B'] || '';
                        const plan_date = row['C'] || '';
                        const work_type = row['D'] || '1';
                        console.log('正在处理行:', { led_id, led_plan_data, plan_date, work_type });
                        if (!led_id) {
                            throw new Error(`设备ID不能为空，行数据: ${JSON.stringify(row)}`);
                        }
                        const ledDevice = await this.validateLedId(led_id);
                        if (!ledDevice) {
                            throw new Error(`设备ID不存在: ${led_id}`);
                        }
                        let formattedDate = '';
                        if (plan_date) {
                            if (typeof plan_date === 'string') {
                                formattedDate = this.formatDateString(plan_date);
                            }
                            else if (typeof plan_date === 'number') {
                                const date = new Date(Math.round((plan_date - 25569) * 86400 * 1000));
                                formattedDate = this.formatDate(date);
                            }
                            else if (plan_date instanceof Date) {
                                formattedDate = this.formatDate(plan_date);
                            }
                            else {
                                formattedDate = new Date().toISOString().split('T')[0];
                            }
                        }
                        else {
                            formattedDate = new Date().toISOString().split('T')[0];
                        }
                        const validWorkType = work_type === '2' ? '2' : '1';
                        const insertQuery = `
              INSERT INTO LED_PLAN_INFO(
                ID, LED_ID, LED_PLAN_DATA, PLAN_DATE, PLAN_TYPE, WORK_TYPE, PUSH_STATUS, CREATE_TIME
              ) VALUES(
                SQL_LED_PLAN_INFO_ID.NEXTVAL, '${led_id}', '${led_plan_data}', 
                TO_DATE('${formattedDate}', 'YYYY-MM-DD'), '1', 
                '${validWorkType}', '0', SYSDATE
              )
            `;
                        await this.dataSource.query(insertQuery);
                        result.success++;
                    }
                    catch (error) {
                        result.failed++;
                        result.errors.push(error.message);
                        console.error(`导入数据失败:`, error);
                    }
                }
                return result;
            }
            catch (error) {
                console.error('导入Excel数据失败:', error);
                throw new common_1.InternalServerErrorException(`导入Excel数据失败: ${error.message}`);
            }
        }
        catch (error) {
            console.error('导入Excel数据失败:', error);
            throw new common_1.InternalServerErrorException(`导入Excel数据失败: ${error.message}`);
        }
    }
    formatDateString(dateStr) {
        try {
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                return this.formatDate(date);
            }
            const parts = dateStr.split(/[-/\.]/);
            if (parts.length === 3) {
                if (parseInt(parts[0]) > 31) {
                    return `${parts[0]}-${parts[1].padStart(2, '0')}-${parts[2].padStart(2, '0')}`;
                }
                else if (parseInt(parts[2]) > 31) {
                    return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
                }
                else {
                    const month = parseInt(parts[0]);
                    const day = parseInt(parts[1]);
                    const year = parseInt(parts[2]);
                    const fullYear = year < 100 ? 2000 + year : year;
                    return `${fullYear}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                }
            }
            return dateStr;
        }
        catch (error) {
            console.error('日期格式化错误:', error);
            return new Date().toISOString().split('T')[0];
        }
    }
    async validateLedId(led_id) {
        try {
            const devices = await this.dataSource.query(`SELECT COUNT(*) AS COUNT FROM LED_DEVICE_INFO WHERE LED_ID = '${led_id}'`);
            return parseInt(devices[0].COUNT) > 0;
        }
        catch (error) {
            console.error(`验证LED设备ID ${led_id} 失败:`, error);
            return false;
        }
    }
    formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
};
exports.LedPlanService = LedPlanService;
exports.LedPlanService = LedPlanService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], LedPlanService);
//# sourceMappingURL=led_plan.service.js.map