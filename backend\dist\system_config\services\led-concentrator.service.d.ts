import { Repository, DataSource } from 'typeorm';
import { LedConcentrator } from '../entities/led-concentrator.entity';
import { ConcentratorLedMapping } from '../entities/concentrator-led-mapping.entity';
import { CreateConcentratorDto, UpdateConcentratorDto, ConcentratorQueryDto } from '../dto/led-concentrator.dto';
export declare class LedConcentratorService {
    private ledConcentratorRepository;
    private concentratorLedMappingRepository;
    private dataSource;
    private readonly logger;
    constructor(ledConcentratorRepository: Repository<LedConcentrator>, concentratorLedMappingRepository: Repository<ConcentratorLedMapping>, dataSource: DataSource);
    findAll(query: ConcentratorQueryDto): Promise<{
        items: LedConcentrator[];
        meta: {
            total: number;
            currentPage?: number;
            totalPages?: number;
            itemsPerPage?: number;
        };
    }>;
    findOne(id: number): Promise<LedConcentrator>;
    create(createConcentratorDto: CreateConcentratorDto): Promise<LedConcentrator>;
    update(id: number, updateConcentratorDto: UpdateConcentratorDto): Promise<LedConcentrator>;
    remove(id: number): Promise<void>;
    testConnection(id: number): Promise<{
        success: boolean;
        message: string;
    }>;
}
