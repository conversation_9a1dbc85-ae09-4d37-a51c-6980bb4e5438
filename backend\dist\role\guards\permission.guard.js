"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const menu_service_1 = require("../menu.service");
const user_permission_service_1 = require("../user-permission.service");
let PermissionGuard = class PermissionGuard {
    constructor(reflector, menuService, userPermissionService) {
        this.reflector = reflector;
        this.menuService = menuService;
        this.userPermissionService = userPermissionService;
    }
    async canActivate(context) {
        const requiredMenuCode = this.reflector.get('menuCode', context.getHandler());
        const requiredDevicePermission = this.reflector.get('devicePermission', context.getHandler());
        if (!requiredMenuCode && !requiredDevicePermission) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user || !user.id) {
            throw new common_1.ForbiddenException('用户未认证');
        }
        const userId = user.id;
        try {
            if (requiredMenuCode) {
                const hasMenuPermission = await this.menuService.checkUserMenuPermission(userId, requiredMenuCode);
                if (!hasMenuPermission) {
                    throw new common_1.ForbiddenException(`用户没有访问菜单 ${requiredMenuCode} 的权限`);
                }
            }
            if (requiredDevicePermission) {
                const ledId = request.params.ledId || request.body.ledId;
                if (ledId) {
                    const hasDevicePermission = await this.userPermissionService.checkUserDevicePermission(userId, ledId, requiredDevicePermission);
                    if (!hasDevicePermission) {
                        throw new common_1.ForbiddenException(`用户没有对设备 ${ledId} 的 ${requiredDevicePermission} 权限`);
                    }
                }
            }
            return true;
        }
        catch (error) {
            if (error instanceof common_1.ForbiddenException) {
                throw error;
            }
            console.error('权限检查失败:', error);
            throw new common_1.ForbiddenException('权限检查失败');
        }
    }
};
exports.PermissionGuard = PermissionGuard;
exports.PermissionGuard = PermissionGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        menu_service_1.MenuService,
        user_permission_service_1.UserPermissionService])
], PermissionGuard);
//# sourceMappingURL=permission.guard.js.map