"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const role_service_1 = require("./role.service");
const role_controller_1 = require("./role.controller");
const menu_service_1 = require("./menu.service");
const menu_controller_1 = require("./menu.controller");
const user_permission_service_1 = require("./user-permission.service");
const user_permission_controller_1 = require("./user-permission.controller");
const permission_guard_1 = require("./guards/permission.guard");
const role_entity_1 = require("./entities/role.entity");
const menu_entity_1 = require("./entities/menu.entity");
const role_menu_entity_1 = require("./entities/role-menu.entity");
const user_role_entity_1 = require("./entities/user-role.entity");
const user_device_permission_entity_1 = require("./entities/user-device-permission.entity");
let RoleModule = class RoleModule {
};
exports.RoleModule = RoleModule;
exports.RoleModule = RoleModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([role_entity_1.Role, menu_entity_1.Menu, role_menu_entity_1.RoleMenu, user_role_entity_1.UserRole, user_device_permission_entity_1.UserDevicePermission])],
        controllers: [role_controller_1.RoleController, menu_controller_1.MenuController, user_permission_controller_1.UserPermissionController],
        providers: [role_service_1.RoleService, menu_service_1.MenuService, user_permission_service_1.UserPermissionService, permission_guard_1.PermissionGuard],
        exports: [role_service_1.RoleService, menu_service_1.MenuService, user_permission_service_1.UserPermissionService, permission_guard_1.PermissionGuard],
    })
], RoleModule);
//# sourceMappingURL=role.module.js.map