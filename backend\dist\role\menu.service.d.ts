import { DataSource } from 'typeorm';
import { Menu } from './entities/menu.entity';
export declare class MenuService {
    private dataSource;
    constructor(dataSource: DataSource);
    findAll(params?: any): Promise<{
        items: Menu[];
        meta: any;
    }>;
    getTree(): Promise<Menu[]>;
    findOne(id: number): Promise<Menu>;
    getUserMenus(userId: number): Promise<Menu[]>;
    checkUserMenuPermission(userId: number, menuCode: string): Promise<boolean>;
    getMenuTypes(): Promise<any[]>;
}
