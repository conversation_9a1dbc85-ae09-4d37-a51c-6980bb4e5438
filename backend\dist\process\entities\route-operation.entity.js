"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouteOperation = void 0;
const typeorm_1 = require("typeorm");
const process_route_entity_1 = require("./process-route.entity");
const process_operation_entity_1 = require("./process-operation.entity");
let RouteOperation = class RouteOperation {
};
exports.RouteOperation = RouteOperation;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ROUTE_OPERATION_ID' }),
    __metadata("design:type", Number)
], RouteOperation.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ROUTE_ID' }),
    __metadata("design:type", Number)
], RouteOperation.prototype, "routeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'OPERATION_ID' }),
    __metadata("design:type", Number)
], RouteOperation.prototype, "operationId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'SEQUENCE_NO' }),
    __metadata("design:type", Number)
], RouteOperation.prototype, "sequenceNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_ID', length: 50, nullable: true }),
    __metadata("design:type", String)
], RouteOperation.prototype, "ledId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'X_POSITION', type: 'float', nullable: true }),
    __metadata("design:type", Number)
], RouteOperation.prototype, "xPosition", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'Y_POSITION', type: 'float', nullable: true }),
    __metadata("design:type", Number)
], RouteOperation.prototype, "yPosition", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'NEXT_OPERATION_IDS', length: 100, nullable: true }),
    __metadata("design:type", String)
], RouteOperation.prototype, "nextOperationIds", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'OPERATION_PARAMS', length: 1000, nullable: true }),
    __metadata("design:type", String)
], RouteOperation.prototype, "operationParams", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', type: 'number', default: 1 }),
    __metadata("design:type", Number)
], RouteOperation.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', length: 50, nullable: true }),
    __metadata("design:type", String)
], RouteOperation.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATION_DATE', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], RouteOperation.prototype, "creationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', length: 50, nullable: true }),
    __metadata("design:type", String)
], RouteOperation.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATE_DATE', type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], RouteOperation.prototype, "lastUpdateDate", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => process_route_entity_1.ProcessRoute, route => route.operations),
    (0, typeorm_1.JoinColumn)({ name: 'ROUTE_ID' }),
    __metadata("design:type", process_route_entity_1.ProcessRoute)
], RouteOperation.prototype, "route", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => process_operation_entity_1.ProcessOperation),
    (0, typeorm_1.JoinColumn)({ name: 'OPERATION_ID' }),
    __metadata("design:type", process_operation_entity_1.ProcessOperation)
], RouteOperation.prototype, "operation", void 0);
exports.RouteOperation = RouteOperation = __decorate([
    (0, typeorm_1.Entity)('ROUTE_OPERATION')
], RouteOperation);
//# sourceMappingURL=route-operation.entity.js.map