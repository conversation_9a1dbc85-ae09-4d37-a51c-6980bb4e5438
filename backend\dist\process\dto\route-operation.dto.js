"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouteOperationResponseDto = exports.UpdateRouteOperationDto = exports.CreateRouteOperationDto = exports.RouteOperationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class RouteOperationDto {
}
exports.RouteOperationDto = RouteOperationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线步骤ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RouteOperationDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工艺路线ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RouteOperationDto.prototype, "routeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '工序ID不能为空' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RouteOperationDto.prototype, "operationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '步骤序号' }),
    (0, class_validator_1.IsNotEmpty)({ message: '步骤序号不能为空' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RouteOperationDto.prototype, "sequenceNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'LED设备ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RouteOperationDto.prototype, "ledId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'X坐标位置', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RouteOperationDto.prototype, "xPosition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Y坐标位置', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RouteOperationDto.prototype, "yPosition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '下一步工序ID列表', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RouteOperationDto.prototype, "nextOperationIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序参数', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RouteOperationDto.prototype, "operationParams", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活', default: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RouteOperationDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序信息', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], RouteOperationDto.prototype, "operation", void 0);
class CreateRouteOperationDto extends RouteOperationDto {
}
exports.CreateRouteOperationDto = CreateRouteOperationDto;
class UpdateRouteOperationDto {
}
exports.UpdateRouteOperationDto = UpdateRouteOperationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateRouteOperationDto.prototype, "operationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '步骤序号', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateRouteOperationDto.prototype, "sequenceNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'LED设备ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateRouteOperationDto.prototype, "ledId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'X坐标位置', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateRouteOperationDto.prototype, "xPosition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Y坐标位置', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateRouteOperationDto.prototype, "yPosition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '下一步工序ID列表', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateRouteOperationDto.prototype, "nextOperationIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工序参数', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateRouteOperationDto.prototype, "operationParams", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateRouteOperationDto.prototype, "isActive", void 0);
class RouteOperationResponseDto extends RouteOperationDto {
}
exports.RouteOperationResponseDto = RouteOperationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人' }),
    __metadata("design:type", String)
], RouteOperationResponseDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建日期' }),
    __metadata("design:type", Date)
], RouteOperationResponseDto.prototype, "creationDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新人' }),
    __metadata("design:type", String)
], RouteOperationResponseDto.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新日期' }),
    __metadata("design:type", Date)
], RouteOperationResponseDto.prototype, "lastUpdateDate", void 0);
//# sourceMappingURL=route-operation.dto.js.map