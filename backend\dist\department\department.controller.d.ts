import { DepartmentService } from './department.service';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
export declare class DepartmentController {
    private readonly departmentService;
    constructor(departmentService: DepartmentService);
    create(createDepartmentDto: CreateDepartmentDto): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/department.entity").Department;
    } | {
        success: boolean;
        message: any;
        data: any;
    }>;
    findAll(query: any): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/department.entity").Department[];
        meta: any;
    } | {
        success: boolean;
        message: any;
        data: any[];
        meta: {
            totalItems: number;
            itemsPerPage: number;
            currentPage: number;
            totalPages: number;
        };
    }>;
    getTree(req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/department.entity").Department[];
    } | {
        success: boolean;
        message: any;
        data: any[];
    }>;
    findOne(id: number): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/department.entity").Department;
    } | {
        success: boolean;
        message: any;
        data: any;
    }>;
    update(id: number, updateDepartmentDto: UpdateDepartmentDto): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/department.entity").Department;
    } | {
        success: boolean;
        message: any;
        data: any;
    }>;
    remove(id: number): Promise<{
        success: boolean;
        message: any;
        data: any;
    }>;
}
