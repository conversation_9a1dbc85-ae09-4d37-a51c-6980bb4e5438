"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Menu = void 0;
const typeorm_1 = require("typeorm");
let Menu = class Menu {
};
exports.Menu = Menu;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'MENU_ID' }),
    __metadata("design:type", Number)
], Menu.prototype, "MENU_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'MENU_CODE', unique: true, nullable: false }),
    __metadata("design:type", String)
], Menu.prototype, "MENU_CODE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'MENU_NAME', nullable: false }),
    __metadata("design:type", String)
], Menu.prototype, "MENU_NAME", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PARENT_MENU_ID', nullable: true }),
    __metadata("design:type", Number)
], Menu.prototype, "PARENT_MENU_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'MENU_TYPE', default: 'MENU' }),
    __metadata("design:type", String)
], Menu.prototype, "MENU_TYPE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ROUTE_PATH', nullable: true }),
    __metadata("design:type", String)
], Menu.prototype, "ROUTE_PATH", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ICON', nullable: true }),
    __metadata("design:type", String)
], Menu.prototype, "ICON", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'SORT_ORDER', default: 0 }),
    __metadata("design:type", Number)
], Menu.prototype, "SORT_ORDER", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', default: 1 }),
    __metadata("design:type", Number)
], Menu.prototype, "IS_ACTIVE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', nullable: true }),
    __metadata("design:type", String)
], Menu.prototype, "CREATED_BY", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATION_DATE', type: 'date', default: () => 'SYSDATE' }),
    __metadata("design:type", Date)
], Menu.prototype, "CREATION_DATE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', nullable: true }),
    __metadata("design:type", String)
], Menu.prototype, "LAST_UPDATED_BY", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATE_DATE', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Menu.prototype, "LAST_UPDATE_DATE", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Menu, menu => menu.children),
    (0, typeorm_1.JoinColumn)({ name: 'PARENT_MENU_ID' }),
    __metadata("design:type", Menu)
], Menu.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Menu, menu => menu.parent),
    __metadata("design:type", Array)
], Menu.prototype, "children", void 0);
exports.Menu = Menu = __decorate([
    (0, typeorm_1.Entity)('LED_MENU')
], Menu);
//# sourceMappingURL=menu.entity.js.map