"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedDataService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
let LedDataService = class LedDataService {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async create(createDataDto) {
        try {
            const ledDevices = await this.dataSource.query('SELECT LED_NAME FROM LED_DEVICE_INFO WHERE LED_ID = :1', [createDataDto.LED_ID]);
            const createTime = createDataDto.CREATE_TIME || new Date().toISOString();
            const result = await this.dataSource.query(`INSERT INTO LED_DATA_INFO(
          ID, LED_ID, LED_PLAN_DATA, LED_REAL_DATA, CREATE_TIME
        ) VALUES(
          LED_DATA_INFO_SEQ.NEXTVAL, :1, :2, :3, TO_TIMESTAMP(:4, 'YYYY-MM-DD"T"HH24:MI:SS.FF3"Z"')
        ) RETURNING ID INTO :5`, [
                createDataDto.LED_ID,
                createDataDto.LED_PLAN_DATA || 0,
                createDataDto.LED_REAL_DATA || 0,
                createTime,
                { dir: this.dataSource.driver.oracle.BIND_OUT, type: this.dataSource.driver.oracle.NUMBER }
            ]);
            const newId = result[0];
            const newData = await this.findOne(newId);
            return newData;
        }
        catch (error) {
            console.error('创建LED数据失败:', error);
            throw new common_1.InternalServerErrorException('创建LED数据失败');
        }
    }
    async findAll(params = {}) {
        try {
            const page = params.page ? parseInt(params.page) : 1;
            const limit = params.limit ? parseInt(params.limit) : 10;
            const offset = (page - 1) * limit;
            const search = params.search;
            let whereClause = '';
            const queryParams = [];
            let paramIndex = 1;
            if (search) {
                whereClause = `WHERE d.LED_ID LIKE '%' || :${paramIndex} || '%'`;
                queryParams.push(search);
                paramIndex++;
            }
            if (params.startDate && params.endDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `d.CREATE_TIME BETWEEN TO_TIMESTAMP(:${paramIndex}, 'YYYY-MM-DD') AND TO_TIMESTAMP(:${paramIndex + 1}, 'YYYY-MM-DD') + 0.99999`;
                queryParams.push(params.startDate);
                queryParams.push(params.endDate);
                paramIndex += 2;
            }
            else if (params.startDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `d.CREATE_TIME >= TO_TIMESTAMP(:${paramIndex}, 'YYYY-MM-DD')`;
                queryParams.push(params.startDate);
                paramIndex++;
            }
            else if (params.endDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `d.CREATE_TIME <= TO_TIMESTAMP(:${paramIndex}, 'YYYY-MM-DD') + 0.99999`;
                queryParams.push(params.endDate);
                paramIndex++;
            }
            if (params.ledId) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `d.LED_ID = :${paramIndex}`;
                queryParams.push(params.ledId);
                paramIndex++;
            }
            const countQuery = `SELECT COUNT(*) AS total FROM LED_DATA_INFO d ${whereClause}`;
            const countResult = await this.dataSource.query(countQuery, queryParams);
            const totalItems = parseInt(countResult[0].TOTAL);
            const query = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT d.ID, d.LED_ID, d.LED_PLAN_DATA, d.LED_REAL_DATA, 
                   TO_CHAR(d.CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') AS CREATE_TIME,
                   d.ACCUMULATED_VALUE, d.WORK_TYPE, 
                   TO_CHAR(d.WORK_DATE, 'YYYY-MM-DD') AS WORK_DATE,
                   dev.LED_NAME 
            FROM LED_DATA_INFO d
            LEFT JOIN LED_DEVICE_INFO dev ON d.LED_ID = dev.LED_ID
            ${whereClause}
            ORDER BY d.ID DESC
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const data = await this.dataSource.query(query, queryParams);
            return {
                items: data,
                meta: {
                    totalItems,
                    itemsPerPage: limit,
                    currentPage: page,
                    totalPages: Math.ceil(totalItems / limit)
                }
            };
        }
        catch (error) {
            console.error('获取LED数据列表失败:', error);
            throw new common_1.InternalServerErrorException('获取LED数据列表失败');
        }
    }
    async findOne(id) {
        try {
            const data = await this.dataSource.query(`SELECT d.ID, d.LED_ID, d.LED_PLAN_DATA, d.LED_REAL_DATA, 
                TO_CHAR(d.CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') AS CREATE_TIME,
                d.ACCUMULATED_VALUE, d.WORK_TYPE, 
                TO_CHAR(d.WORK_DATE, 'YYYY-MM-DD') AS WORK_DATE,
                dev.LED_NAME 
         FROM LED_DATA_INFO d
         LEFT JOIN LED_DEVICE_INFO dev ON d.LED_ID = dev.LED_ID
         WHERE d.ID = ${id}`);
            if (!data || data.length === 0) {
                throw new common_1.NotFoundException(`ID为${id}的LED数据不存在`);
            }
            return data[0];
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取ID为${id}的LED数据失败:`, error);
            throw new common_1.InternalServerErrorException(`获取ID为${id}的LED数据失败`);
        }
    }
    async update(id, updateDataDto) {
        try {
            await this.findOne(id);
            let updateQuery = 'UPDATE LED_DATA_INFO SET ';
            const updateValues = [];
            let paramIndex = 1;
            if (updateDataDto.LED_ID !== undefined) {
                updateQuery += `LED_ID = :${paramIndex}, `;
                updateValues.push(updateDataDto.LED_ID);
                paramIndex++;
            }
            if (updateDataDto.LED_PLAN_DATA !== undefined) {
                updateQuery += `LED_PLAN_DATA = :${paramIndex}, `;
                updateValues.push(updateDataDto.LED_PLAN_DATA);
                paramIndex++;
            }
            if (updateDataDto.LED_REAL_DATA !== undefined) {
                updateQuery += `LED_REAL_DATA = :${paramIndex}, `;
                updateValues.push(updateDataDto.LED_REAL_DATA);
                paramIndex++;
            }
            if (updateDataDto.CREATE_TIME !== undefined) {
                updateQuery += `CREATE_TIME = TO_TIMESTAMP(:${paramIndex}, 'YYYY-MM-DD"T"HH24:MI:SS.FF3"Z"'), `;
                updateValues.push(updateDataDto.CREATE_TIME);
                paramIndex++;
            }
            updateQuery = updateQuery.slice(0, -2);
            updateQuery += ` WHERE ID = :${paramIndex}`;
            updateValues.push(id);
            await this.dataSource.query(updateQuery, updateValues);
            return this.findOne(id);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`更新ID为${id}的LED数据失败:`, error);
            throw new common_1.InternalServerErrorException(`更新ID为${id}的LED数据失败`);
        }
    }
    async remove(id) {
        try {
            await this.findOne(id);
            await this.dataSource.query('DELETE FROM LED_DATA_INFO WHERE ID = :1', [id]);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`删除ID为${id}的LED数据失败:`, error);
            throw new common_1.InternalServerErrorException(`删除ID为${id}的LED数据失败`);
        }
    }
    async getStatistics(params = {}) {
        try {
            let whereClause = '';
            const queryParams = [];
            let paramIndex = 1;
            if (params.startDate && params.endDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `CREATE_TIME BETWEEN TO_TIMESTAMP(:${paramIndex}, 'YYYY-MM-DD') AND TO_TIMESTAMP(:${paramIndex + 1}, 'YYYY-MM-DD') + 0.99999`;
                queryParams.push(params.startDate);
                queryParams.push(params.endDate);
                paramIndex += 2;
            }
            else if (params.startDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `CREATE_TIME >= TO_TIMESTAMP(:${paramIndex}, 'YYYY-MM-DD')`;
                queryParams.push(params.startDate);
                paramIndex++;
            }
            else if (params.endDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `CREATE_TIME <= TO_TIMESTAMP(:${paramIndex}, 'YYYY-MM-DD') + 0.99999`;
                queryParams.push(params.endDate);
                paramIndex++;
            }
            if (params.ledId) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `LED_ID = :${paramIndex}`;
                queryParams.push(params.ledId);
                paramIndex++;
            }
            const overallStatsQuery = `
        SELECT 
          SUM(LED_REAL_DATA) as TOTAL_REAL_DATA,
          SUM(LED_PLAN_DATA) as TOTAL_PLAN_DATA,
          CASE 
            WHEN SUM(LED_PLAN_DATA) > 0 THEN ROUND(SUM(LED_REAL_DATA) / SUM(LED_PLAN_DATA) * 100, 2)
            ELSE 0
          END as OVERALL_COMPLETION_RATE,
          COUNT(*) as DATA_COUNT
        FROM LED_DATA_INFO
        ${whereClause}
      `;
            const overallStats = await this.dataSource.query(overallStatsQuery, queryParams);
            const ledStatsQuery = `
        SELECT 
          LED_ID,
          SUM(LED_REAL_DATA) as TOTAL_REAL_DATA,
          SUM(LED_PLAN_DATA) as TOTAL_PLAN_DATA,
          CASE 
            WHEN SUM(LED_PLAN_DATA) > 0 THEN ROUND(SUM(LED_REAL_DATA) / SUM(LED_PLAN_DATA) * 100, 2)
            ELSE 0
          END as COMPLETION_RATE,
          COUNT(*) as DATA_COUNT
        FROM LED_DATA_INFO
        ${whereClause}
        GROUP BY LED_ID
        ORDER BY LED_ID
      `;
            const ledStats = await this.dataSource.query(ledStatsQuery, queryParams);
            const dateStatsQuery = `
        SELECT 
          TO_CHAR(CREATE_TIME, 'YYYY-MM-DD') as CREATE_DATE,
          SUM(LED_REAL_DATA) as TOTAL_REAL_DATA,
          SUM(LED_PLAN_DATA) as TOTAL_PLAN_DATA,
          CASE 
            WHEN SUM(LED_PLAN_DATA) > 0 THEN ROUND(SUM(LED_REAL_DATA) / SUM(LED_PLAN_DATA) * 100, 2)
            ELSE 0
          END as COMPLETION_RATE,
          COUNT(*) as DATA_COUNT
        FROM LED_DATA_INFO
        ${whereClause}
        GROUP BY TO_CHAR(CREATE_TIME, 'YYYY-MM-DD')
        ORDER BY TO_CHAR(CREATE_TIME, 'YYYY-MM-DD') DESC
      `;
            const dateStats = await this.dataSource.query(dateStatsQuery, queryParams);
            return {
                overall: overallStats[0],
                byLed: ledStats,
                byDate: dateStats
            };
        }
        catch (error) {
            console.error('获取LED数据统计失败:', error);
            throw new common_1.InternalServerErrorException('获取LED数据统计失败');
        }
    }
    async exportData(params = {}) {
        try {
            let whereClause = '';
            const queryParams = [];
            let paramIndex = 1;
            if (params.search) {
                whereClause = `WHERE LED_ID LIKE '%' || :${paramIndex} || '%'`;
                queryParams.push(params.search);
                paramIndex++;
            }
            if (params.startDate && params.endDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `CREATE_TIME BETWEEN TO_TIMESTAMP(:${paramIndex}, 'YYYY-MM-DD') AND TO_TIMESTAMP(:${paramIndex + 1}, 'YYYY-MM-DD') + 0.99999`;
                queryParams.push(params.startDate);
                queryParams.push(params.endDate);
                paramIndex += 2;
            }
            else if (params.startDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `CREATE_TIME >= TO_TIMESTAMP(:${paramIndex}, 'YYYY-MM-DD')`;
                queryParams.push(params.startDate);
                paramIndex++;
            }
            else if (params.endDate) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `CREATE_TIME <= TO_TIMESTAMP(:${paramIndex}, 'YYYY-MM-DD') + 0.99999`;
                queryParams.push(params.endDate);
                paramIndex++;
            }
            if (params.ledId) {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `LED_ID = :${paramIndex}`;
                queryParams.push(params.ledId);
                paramIndex++;
            }
            const query = `
        SELECT 
          ID,
          LED_ID,
          LED_PLAN_DATA,
          LED_REAL_DATA,
          TO_CHAR(CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') as CREATE_TIME
        FROM LED_DATA_INFO
        ${whereClause}
        ORDER BY CREATE_TIME DESC
      `;
            const data = await this.dataSource.query(query, queryParams);
            return data;
        }
        catch (error) {
            console.error('导出LED数据失败:', error);
            throw new common_1.InternalServerErrorException('导出LED数据失败');
        }
    }
};
exports.LedDataService = LedDataService;
exports.LedDataService = LedDataService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], LedDataService);
//# sourceMappingURL=led_data.service.js.map