import { LedMappingService } from '../services/led-mapping.service';
import { CreateLedMappingDto, UpdateLedMappingDto, LedMappingQueryDto } from '../dto/led-mapping.dto';
export declare class LedMappingController {
    private readonly ledMappingService;
    private readonly logger;
    constructor(ledMappingService: LedMappingService);
    create(createLedMappingDto: CreateLedMappingDto): Promise<any>;
    findAll(query: LedMappingQueryDto): Promise<{
        items: any;
        meta: {
            total: number;
            page: number;
            limit: number;
        };
    }>;
    findOne(id: string): Promise<any>;
    findByLedId(ledId: string): Promise<{
        id: any;
        concentratorId: any;
        ledId: any;
        channelNumber: any;
        description: any;
        isActive: boolean;
        concentrator: {
            id: any;
            name: any;
            code: any;
            ipAddress: any;
            portNumber: any;
            protocolType: any;
            timeoutSeconds: any;
            maxRetryCount: any;
        };
        ledName: any;
    }>;
    update(id: string, updateLedMappingDto: UpdateLedMappingDto): Promise<any>;
    remove(id: string): Promise<{
        id: number;
        message: string;
    }>;
}
