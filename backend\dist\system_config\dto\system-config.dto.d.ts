export declare class CreateSystemConfigDto {
    key: string;
    value: string;
    type: string;
    group: string;
    description: string;
    isEncrypted: boolean;
    isEditable: boolean;
    sortOrder: number;
    isActive: boolean;
}
export declare class UpdateSystemConfigDto {
    key?: string;
    value?: string;
    type?: string;
    group?: string;
    description?: string;
    isEncrypted?: boolean;
    isEditable?: boolean;
    sortOrder?: number;
    isActive?: boolean;
}
export declare class SystemConfigQueryDto {
    search?: string;
    type?: string;
    group?: string;
    key?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
}
