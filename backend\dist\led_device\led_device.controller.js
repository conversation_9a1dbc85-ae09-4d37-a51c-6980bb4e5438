"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedDeviceController = void 0;
const common_1 = require("@nestjs/common");
const led_device_service_1 = require("./led_device.service");
const create_device_dto_1 = require("./dto/create-device.dto");
const update_device_dto_1 = require("./dto/update-device.dto");
const permission_guard_1 = require("../role/guards/permission.guard");
const permission_decorator_1 = require("../role/decorators/permission.decorator");
let LedDeviceController = class LedDeviceController {
    constructor(ledDeviceService) {
        this.ledDeviceService = ledDeviceService;
    }
    create(createDeviceDto) {
        return this.ledDeviceService.create(createDeviceDto);
    }
    findAll(query) {
        return this.ledDeviceService.findAll(query);
    }
    findOne(id) {
        return this.ledDeviceService.findOne(+id);
    }
    update(id, updateDeviceDto) {
        return this.ledDeviceService.update(+id, updateDeviceDto);
    }
    remove(id) {
        return this.ledDeviceService.remove(+id);
    }
};
exports.LedDeviceController = LedDeviceController;
__decorate([
    (0, common_1.Post)(),
    (0, permission_decorator_1.RequireMenu)('DEVICE_MANAGE'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_device_dto_1.CreateDeviceDto]),
    __metadata("design:returntype", void 0)
], LedDeviceController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, permission_decorator_1.RequireMenu)('DEVICE_MANAGE'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], LedDeviceController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permission_decorator_1.RequireMenu)('DEVICE_MANAGE'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LedDeviceController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, permission_decorator_1.RequireMenu)('DEVICE_MANAGE'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_device_dto_1.UpdateDeviceDto]),
    __metadata("design:returntype", void 0)
], LedDeviceController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, permission_decorator_1.RequireMenu)('DEVICE_MANAGE'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LedDeviceController.prototype, "remove", null);
exports.LedDeviceController = LedDeviceController = __decorate([
    (0, common_1.Controller)('devices'),
    (0, common_1.UseGuards)(permission_guard_1.PermissionGuard),
    __metadata("design:paramtypes", [led_device_service_1.LedDeviceService])
], LedDeviceController);
//# sourceMappingURL=led_device.controller.js.map