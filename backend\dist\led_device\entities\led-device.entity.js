"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedDevice = void 0;
const typeorm_1 = require("typeorm");
let LedDevice = class LedDevice {
};
exports.LedDevice = LedDevice;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], LedDevice.prototype, "ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_ID', nullable: false }),
    __metadata("design:type", String)
], LedDevice.prototype, "LED_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_NAME', nullable: false }),
    __metadata("design:type", String)
], LedDevice.prototype, "LED_NAME", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DEP_NAME', nullable: true }),
    __metadata("design:type", String)
], LedDevice.prototype, "DEP_NAME", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DEP_ID', nullable: true }),
    __metadata("design:type", String)
], LedDevice.prototype, "DEP_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DEP_ID_OA', nullable: true }),
    __metadata("design:type", String)
], LedDevice.prototype, "DEP_ID_OA", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'GONGXU', nullable: true }),
    __metadata("design:type", String)
], LedDevice.prototype, "GONGXU", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CHANXIAN', nullable: true }),
    __metadata("design:type", String)
], LedDevice.prototype, "CHANXIAN", void 0);
exports.LedDevice = LedDevice = __decorate([
    (0, typeorm_1.Entity)('LED_DEVICE_INFO')
], LedDevice);
//# sourceMappingURL=led-device.entity.js.map