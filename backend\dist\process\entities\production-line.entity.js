"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductionLine = void 0;
const typeorm_1 = require("typeorm");
const process_route_entity_1 = require("./process-route.entity");
let ProductionLine = class ProductionLine {
};
exports.ProductionLine = ProductionLine;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'LINE_ID' }),
    __metadata("design:type", Number)
], ProductionLine.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LINE_CODE', length: 50, unique: true }),
    __metadata("design:type", String)
], ProductionLine.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LINE_NAME', length: 100 }),
    __metadata("design:type", String)
], ProductionLine.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRIPTION', length: 500, nullable: true }),
    __metadata("design:type", String)
], ProductionLine.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ROUTE_ID', nullable: true }),
    __metadata("design:type", Number)
], ProductionLine.prototype, "routeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LINE_MANAGER', length: 100, nullable: true }),
    __metadata("design:type", String)
], ProductionLine.prototype, "manager", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'AREA_CODE', length: 50, nullable: true }),
    __metadata("design:type", String)
], ProductionLine.prototype, "areaCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', type: 'number', default: 1 }),
    __metadata("design:type", Number)
], ProductionLine.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', length: 50, nullable: true }),
    __metadata("design:type", String)
], ProductionLine.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATION_DATE', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], ProductionLine.prototype, "creationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', length: 50, nullable: true }),
    __metadata("design:type", String)
], ProductionLine.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATE_DATE', type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ProductionLine.prototype, "lastUpdateDate", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => process_route_entity_1.ProcessRoute),
    (0, typeorm_1.JoinColumn)({ name: 'ROUTE_ID' }),
    __metadata("design:type", process_route_entity_1.ProcessRoute)
], ProductionLine.prototype, "route", void 0);
exports.ProductionLine = ProductionLine = __decorate([
    (0, typeorm_1.Entity)('PRODUCTION_LINE')
], ProductionLine);
//# sourceMappingURL=production-line.entity.js.map