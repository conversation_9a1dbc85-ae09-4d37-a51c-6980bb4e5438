import { RoleService } from './role.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { AssignMenuDto } from './dto/assign-menu.dto';
export declare class RoleController {
    private readonly roleService;
    constructor(roleService: RoleService);
    create(createRoleDto: CreateRoleDto): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/role.entity").Role;
    } | {
        success: boolean;
        message: any;
        data: any;
    }>;
    findAll(query: any): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/role.entity").Role[];
        meta: any;
    } | {
        success: boolean;
        message: any;
        data: any[];
        meta: {
            totalItems: number;
            itemsPerPage: number;
            currentPage: number;
            totalPages: number;
        };
    }>;
    getRoleTypes(): Promise<{
        success: boolean;
        message: string;
        data: any[];
    } | {
        success: boolean;
        message: any;
        data: any[];
    }>;
    findOne(id: number): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/role.entity").Role;
    } | {
        success: boolean;
        message: any;
        data: any;
    }>;
    getRoleMenus(id: number): Promise<{
        success: boolean;
        message: string;
        data: any[];
    } | {
        success: boolean;
        message: any;
        data: any[];
    }>;
    update(id: number, updateRoleDto: UpdateRoleDto): Promise<{
        success: boolean;
        message: string;
        data: import("./entities/role.entity").Role;
    } | {
        success: boolean;
        message: any;
        data: any;
    }>;
    assignMenus(id: number, assignMenuDto: AssignMenuDto): Promise<{
        success: boolean;
        message: any;
        data: any;
    }>;
    remove(id: number): Promise<{
        success: boolean;
        message: any;
        data: any;
    }>;
}
