"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductionLineResponseDto = exports.UpdateProductionLineDto = exports.CreateProductionLineDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateProductionLineDto {
}
exports.CreateProductionLineDto = CreateProductionLineDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线编码', example: 'LINE001' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateProductionLineDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线名称', example: 'LED组装产线1' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateProductionLineDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线描述', required: false, example: 'LED屏幕组装主产线' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateProductionLineDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联的工艺路线ID', required: false, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateProductionLineDto.prototype, "routeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线负责人', required: false, example: '张三' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateProductionLineDto.prototype, "manager", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '区域编码', required: false, example: 'AREA-A' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateProductionLineDto.prototype, "areaCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活', required: false, default: 1, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateProductionLineDto.prototype, "isActive", void 0);
class UpdateProductionLineDto {
}
exports.UpdateProductionLineDto = UpdateProductionLineDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线编码', required: false, example: 'LINE001' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], UpdateProductionLineDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线名称', required: false, example: 'LED组装产线1' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], UpdateProductionLineDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线描述', required: false, example: 'LED屏幕组装主产线' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], UpdateProductionLineDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联的工艺路线ID', required: false, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateProductionLineDto.prototype, "routeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线负责人', required: false, example: '张三' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], UpdateProductionLineDto.prototype, "manager", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '区域编码', required: false, example: 'AREA-A' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], UpdateProductionLineDto.prototype, "areaCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活', required: false, example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateProductionLineDto.prototype, "isActive", void 0);
class ProductionLineResponseDto {
}
exports.ProductionLineResponseDto = ProductionLineResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线ID' }),
    __metadata("design:type", Number)
], ProductionLineResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线编码' }),
    __metadata("design:type", String)
], ProductionLineResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线名称' }),
    __metadata("design:type", String)
], ProductionLineResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线描述' }),
    __metadata("design:type", String)
], ProductionLineResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联的工艺路线ID' }),
    __metadata("design:type", Number)
], ProductionLineResponseDto.prototype, "routeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '产线负责人' }),
    __metadata("design:type", String)
], ProductionLineResponseDto.prototype, "manager", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '区域编码' }),
    __metadata("design:type", String)
], ProductionLineResponseDto.prototype, "areaCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否激活' }),
    __metadata("design:type", Number)
], ProductionLineResponseDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人' }),
    __metadata("design:type", String)
], ProductionLineResponseDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建日期' }),
    __metadata("design:type", Date)
], ProductionLineResponseDto.prototype, "creationDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新人' }),
    __metadata("design:type", String)
], ProductionLineResponseDto.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后更新日期' }),
    __metadata("design:type", Date)
], ProductionLineResponseDto.prototype, "lastUpdateDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联的工艺路线', required: false }),
    __metadata("design:type", Object)
], ProductionLineResponseDto.prototype, "route", void 0);
//# sourceMappingURL=production-line.dto.js.map