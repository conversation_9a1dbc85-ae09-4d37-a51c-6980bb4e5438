"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedMappingQueryDto = exports.UpdateLedMappingDto = exports.CreateLedMappingDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateLedMappingDto {
    constructor() {
        this.isActive = true;
    }
}
exports.CreateLedMappingDto = CreateLedMappingDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '集中器ID不能为空' }),
    (0, class_validator_1.IsInt)({ message: '集中器ID必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateLedMappingDto.prototype, "concentratorId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'LED设备ID不能为空' }),
    (0, class_validator_1.IsString)({ message: 'LED设备ID必须为字符串' }),
    __metadata("design:type", String)
], CreateLedMappingDto.prototype, "ledId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '通道号不能为空' }),
    (0, class_validator_1.IsString)({ message: '通道号必须为字符串' }),
    __metadata("design:type", String)
], CreateLedMappingDto.prototype, "channelNumber", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '描述必须为字符串' }),
    __metadata("design:type", String)
], CreateLedMappingDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: '启用状态必须为布尔值' }),
    __metadata("design:type", Boolean)
], CreateLedMappingDto.prototype, "isActive", void 0);
class UpdateLedMappingDto {
}
exports.UpdateLedMappingDto = UpdateLedMappingDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '集中器ID必须为整数' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], UpdateLedMappingDto.prototype, "concentratorId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'LED设备ID必须为字符串' }),
    __metadata("design:type", String)
], UpdateLedMappingDto.prototype, "ledId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '通道号必须为字符串' }),
    __metadata("design:type", String)
], UpdateLedMappingDto.prototype, "channelNumber", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '描述必须为字符串' }),
    __metadata("design:type", String)
], UpdateLedMappingDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: '启用状态必须为布尔值' }),
    __metadata("design:type", Boolean)
], UpdateLedMappingDto.prototype, "isActive", void 0);
class LedMappingQueryDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
    }
}
exports.LedMappingQueryDto = LedMappingQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '页码必须为整数' }),
    (0, class_validator_1.Min)(1, { message: '页码必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], LedMappingQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)({ message: '每页数量必须为整数' }),
    (0, class_validator_1.Min)(1, { message: '每页数量必须大于0' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], LedMappingQueryDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'LED设备ID必须为字符串' }),
    __metadata("design:type", String)
], LedMappingQueryDto.prototype, "ledId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '集中器ID必须为字符串' }),
    __metadata("design:type", String)
], LedMappingQueryDto.prototype, "concentratorId", void 0);
//# sourceMappingURL=led-mapping.dto.js.map