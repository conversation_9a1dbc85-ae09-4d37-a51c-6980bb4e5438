"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const led_concentrator_entity_1 = require("./entities/led-concentrator.entity");
const concentrator_led_mapping_entity_1 = require("./entities/concentrator-led-mapping.entity");
const system_config_entity_1 = require("./entities/system-config.entity");
const led_concentrator_controller_1 = require("./controllers/led-concentrator.controller");
const led_mapping_controller_1 = require("./controllers/led-mapping.controller");
const system_config_controller_1 = require("./controllers/system-config.controller");
const public_system_config_controller_1 = require("./controllers/public-system-config.controller");
const led_concentrator_service_1 = require("./services/led-concentrator.service");
const led_mapping_service_1 = require("./services/led-mapping.service");
const modbus_client_service_1 = require("./services/modbus-client.service");
const system_config_service_1 = require("./services/system-config.service");
let SystemConfigModule = class SystemConfigModule {
};
exports.SystemConfigModule = SystemConfigModule;
exports.SystemConfigModule = SystemConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                led_concentrator_entity_1.LedConcentrator,
                concentrator_led_mapping_entity_1.ConcentratorLedMapping,
                system_config_entity_1.SystemConfig
            ])
        ],
        controllers: [
            led_concentrator_controller_1.LedConcentratorController,
            led_mapping_controller_1.LedMappingController,
            system_config_controller_1.SystemConfigController,
            public_system_config_controller_1.PublicSystemConfigController
        ],
        providers: [
            led_concentrator_service_1.LedConcentratorService,
            led_mapping_service_1.LedMappingService,
            modbus_client_service_1.ModbusClientService,
            system_config_service_1.SystemConfigService
        ],
        exports: [
            led_concentrator_service_1.LedConcentratorService,
            led_mapping_service_1.LedMappingService,
            modbus_client_service_1.ModbusClientService,
            system_config_service_1.SystemConfigService
        ]
    })
], SystemConfigModule);
//# sourceMappingURL=system-config.module.js.map