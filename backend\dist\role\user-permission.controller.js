"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPermissionController = void 0;
const common_1 = require("@nestjs/common");
const user_permission_service_1 = require("./user-permission.service");
const assign_user_role_dto_1 = require("./dto/assign-user-role.dto");
const assign_device_permission_dto_1 = require("./dto/assign-device-permission.dto");
let UserPermissionController = class UserPermissionController {
    constructor(userPermissionService) {
        this.userPermissionService = userPermissionService;
    }
    async assignUserRoles(userId, assignUserRoleDto) {
        try {
            await this.userPermissionService.assignUserRoles(userId, assignUserRoleDto);
            return {
                success: true,
                message: '用户角色分配成功',
                data: null
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '用户角色分配失败',
                data: null
            };
        }
    }
    async getUserRoles(userId) {
        try {
            const roles = await this.userPermissionService.getUserRoles(userId);
            return {
                success: true,
                message: '获取用户角色成功',
                data: roles
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取用户角色失败',
                data: []
            };
        }
    }
    async assignDevicePermissions(userId, assignDevicePermissionDto) {
        try {
            await this.userPermissionService.assignDevicePermissions(userId, assignDevicePermissionDto);
            return {
                success: true,
                message: '设备权限分配成功',
                data: null
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '设备权限分配失败',
                data: null
            };
        }
    }
    async getUserDevicePermissions(userId) {
        try {
            const permissions = await this.userPermissionService.getUserDevicePermissions(userId);
            return {
                success: true,
                message: '获取用户设备权限成功',
                data: permissions
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取用户设备权限失败',
                data: []
            };
        }
    }
    async getUserAccessibleDevices(userId, permissionType) {
        try {
            const devices = await this.userPermissionService.getUserAccessibleDevices(userId, permissionType);
            return {
                success: true,
                message: '获取用户可访问设备成功',
                data: devices
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取用户可访问设备失败',
                data: []
            };
        }
    }
    async getUserPermissionSummary(userId) {
        try {
            const summary = await this.userPermissionService.getUserPermissionSummary(userId);
            return {
                success: true,
                message: '获取用户权限摘要成功',
                data: summary
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取用户权限摘要失败',
                data: null
            };
        }
    }
    async checkUserDevicePermission(userId, ledId, permissionType) {
        try {
            const hasPermission = await this.userPermissionService.checkUserDevicePermission(userId, ledId, permissionType);
            return {
                success: true,
                message: '权限检查完成',
                data: {
                    userId,
                    ledId,
                    permissionType,
                    hasPermission
                }
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '权限检查失败',
                data: {
                    userId,
                    ledId,
                    permissionType,
                    hasPermission: false
                }
            };
        }
    }
    async getPermissionTypes() {
        try {
            const types = await this.userPermissionService.getPermissionTypes();
            return {
                success: true,
                message: '获取权限类型成功',
                data: types
            };
        }
        catch (error) {
            return {
                success: false,
                message: error.message || '获取权限类型失败',
                data: []
            };
        }
    }
};
exports.UserPermissionController = UserPermissionController;
__decorate([
    (0, common_1.Post)(':userId/roles'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, assign_user_role_dto_1.AssignUserRoleDto]),
    __metadata("design:returntype", Promise)
], UserPermissionController.prototype, "assignUserRoles", null);
__decorate([
    (0, common_1.Get)(':userId/roles'),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserPermissionController.prototype, "getUserRoles", null);
__decorate([
    (0, common_1.Post)(':userId/devices'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, assign_device_permission_dto_1.AssignDevicePermissionDto]),
    __metadata("design:returntype", Promise)
], UserPermissionController.prototype, "assignDevicePermissions", null);
__decorate([
    (0, common_1.Get)(':userId/devices'),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserPermissionController.prototype, "getUserDevicePermissions", null);
__decorate([
    (0, common_1.Get)(':userId/accessible-devices'),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('permissionType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], UserPermissionController.prototype, "getUserAccessibleDevices", null);
__decorate([
    (0, common_1.Get)(':userId/summary'),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserPermissionController.prototype, "getUserPermissionSummary", null);
__decorate([
    (0, common_1.Get)(':userId/check-device/:ledId/:permissionType'),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('ledId')),
    __param(2, (0, common_1.Param)('permissionType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, String]),
    __metadata("design:returntype", Promise)
], UserPermissionController.prototype, "checkUserDevicePermission", null);
__decorate([
    (0, common_1.Get)('permission-types'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserPermissionController.prototype, "getPermissionTypes", null);
exports.UserPermissionController = UserPermissionController = __decorate([
    (0, common_1.Controller)('user-permissions'),
    __metadata("design:paramtypes", [user_permission_service_1.UserPermissionService])
], UserPermissionController);
//# sourceMappingURL=user-permission.controller.js.map