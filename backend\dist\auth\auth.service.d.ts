import { Repository, DataSource } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { LedUser } from './entities/led-user.entity';
import { AuthCredentialsDto } from './dto/auth-credentials.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
export declare class AuthService {
    private usersRepository;
    private jwtService;
    private dataSource;
    constructor(usersRepository: Repository<LedUser>, jwtService: JwtService, dataSource: DataSource);
    signUp(createUserDto: CreateUserDto): Promise<void>;
    signIn(authCredentialsDto: AuthCredentialsDto): Promise<{
        accessToken: string;
    }>;
    validateUser(username: string): Promise<any>;
    findAllUsers(params: {
        page: number;
        limit: number;
        search: string;
    }): Promise<{
        items: any;
        meta: {
            totalItems: any;
            itemCount: any;
            itemsPerPage: number;
            totalPages: number;
            currentPage: number;
        };
    }>;
    findUserById(id: number): Promise<any>;
    createUser(createUserDto: CreateUserDto): Promise<any>;
    updateUser(id: number, updateUserDto: UpdateUserDto): Promise<any>;
    deleteUser(id: number): Promise<{
        success: boolean;
        message: string;
    }>;
    resetPassword(id: number, resetPasswordDto: ResetPasswordDto): Promise<{
        success: boolean;
        message: string;
    }>;
    getUserProfile(userId: number): Promise<any>;
}
