"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductionLineController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const production_line_service_1 = require("../services/production-line.service");
const production_line_dto_1 = require("../dto/production-line.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let ProductionLineController = class ProductionLineController {
    constructor(productionLineService) {
        this.productionLineService = productionLineService;
    }
    async findAll(query) {
        return this.productionLineService.findAll(query);
    }
    async findOne(id) {
        return this.productionLineService.findOne(+id);
    }
    async create(createDto, req) {
        return this.productionLineService.create(createDto, req.user);
    }
    async update(id, updateDto, req) {
        return this.productionLineService.update(+id, updateDto, req.user);
    }
    async remove(id) {
        await this.productionLineService.remove(+id);
        return { message: '产线删除成功' };
    }
};
exports.ProductionLineController = ProductionLineController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取产线列表' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '成功获取产线列表',
        type: production_line_dto_1.ProductionLineResponseDto,
        isArray: true
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProductionLineController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取单个产线详情' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '成功获取产线详情',
        type: production_line_dto_1.ProductionLineResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '产线不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProductionLineController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建新产线' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: '产线创建成功',
        type: production_line_dto_1.ProductionLineResponseDto
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [production_line_dto_1.CreateProductionLineDto, Object]),
    __metadata("design:returntype", Promise)
], ProductionLineController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新产线信息' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: '产线更新成功',
        type: production_line_dto_1.ProductionLineResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '产线不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, production_line_dto_1.UpdateProductionLineDto, Object]),
    __metadata("design:returntype", Promise)
], ProductionLineController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除产线' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NO_CONTENT, description: '产线删除成功' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '产线不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProductionLineController.prototype, "remove", null);
exports.ProductionLineController = ProductionLineController = __decorate([
    (0, swagger_1.ApiTags)('产线管理'),
    (0, common_1.Controller)('process/lines'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [production_line_service_1.ProductionLineService])
], ProductionLineController);
//# sourceMappingURL=production-line.controller.js.map