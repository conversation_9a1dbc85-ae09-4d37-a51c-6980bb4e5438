import { LedPlanService } from './led_plan.service';
import { CreatePlanDto } from './dto/create-plan.dto';
import { UpdatePlanDto } from './dto/update-plan.dto';
export declare class LedPlanController {
    private readonly ledPlanService;
    constructor(ledPlanService: LedPlanService);
    create(createPlanDto: CreatePlanDto): Promise<any>;
    importPlans(file: any): Promise<{
        success: number;
        failed: number;
        errors: string[];
    }>;
    findAll(query: any): Promise<{
        items: import("./entities/led-plan.entity").LedPlan[];
        meta: any;
    }>;
    findOne(id: string): Promise<import("./entities/led-plan.entity").LedPlan>;
    update(id: string, updatePlanDto: UpdatePlanDto): Promise<import("./entities/led-plan.entity").LedPlan>;
    remove(id: string): Promise<void>;
    push(id: string): Promise<import("./entities/led-plan.entity").LedPlan>;
    getLedOptions(): Promise<any[]>;
}
