import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { MenuService } from '../menu.service';
import { UserPermissionService } from '../user-permission.service';
export declare class PermissionGuard implements CanActivate {
    private reflector;
    private menuService;
    private userPermissionService;
    constructor(reflector: Reflector, menuService: MenuService, userPermissionService: UserPermissionService);
    canActivate(context: ExecutionContext): Promise<boolean>;
}
