import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { XMarkIcon } from '@heroicons/react/24/outline';
import departmentService from '../services/departmentService';

const DepartmentForm = ({ department, onSubmit, onCancel, loading }) => {
  const [formData, setFormData] = useState({
    DEPT_CODE: '',
    DEPT_NAME: '',
    PARENT_DEPT_ID: '',
    MANAGER_USER_ID: '',
    DESCRIPTION: '',
    SORT_ORDER: 0,
    IS_ACTIVE: 1
  });

  const [errors, setErrors] = useState({});

  // 获取部门选项（用于父级部门选择）
  const { data: departmentOptions } = useQuery({
    queryKey: ['departmentOptions'],
    queryFn: departmentService.getDepartmentOptions,
    staleTime: 5 * 60 * 1000 // 5分钟缓存
  });

  // 初始化表单数据
  useEffect(() => {
    if (department) {
      setFormData({
        DEPT_CODE: department.DEPT_CODE || '',
        DEPT_NAME: department.DEPT_NAME || '',
        PARENT_DEPT_ID: department.PARENT_DEPT_ID || '',
        MANAGER_USER_ID: department.MANAGER_USER_ID || '',
        DESCRIPTION: department.DESCRIPTION || '',
        SORT_ORDER: department.SORT_ORDER || 0,
        IS_ACTIVE: department.IS_ACTIVE !== undefined ? department.IS_ACTIVE : 1
      });
    } else {
      // 新建时重置表单
      setFormData({
        DEPT_CODE: '',
        DEPT_NAME: '',
        PARENT_DEPT_ID: '',
        MANAGER_USER_ID: '',
        DESCRIPTION: '',
        SORT_ORDER: 0,
        IS_ACTIVE: 1
      });
    }
    setErrors({});
  }, [department]);

  // 表单验证
  const validateForm = () => {
    const newErrors = {};

    if (!formData.DEPT_CODE.trim()) {
      newErrors.DEPT_CODE = '部门编码不能为空';
    } else if (formData.DEPT_CODE.length < 2) {
      newErrors.DEPT_CODE = '部门编码至少需要2个字符';
    } else if (formData.DEPT_CODE.length > 50) {
      newErrors.DEPT_CODE = '部门编码不能超过50个字符';
    }

    if (!formData.DEPT_NAME.trim()) {
      newErrors.DEPT_NAME = '部门名称不能为空';
    } else if (formData.DEPT_NAME.length < 2) {
      newErrors.DEPT_NAME = '部门名称至少需要2个字符';
    } else if (formData.DEPT_NAME.length > 100) {
      newErrors.DEPT_NAME = '部门名称不能超过100个字符';
    }

    if (formData.DESCRIPTION && formData.DESCRIPTION.length > 500) {
      newErrors.DESCRIPTION = '描述不能超过500个字符';
    }

    if (formData.SORT_ORDER < 0) {
      newErrors.SORT_ORDER = '排序不能为负数';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理输入变化
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // 处理表单提交
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // 准备提交数据
    const submitData = {
      ...formData,
      PARENT_DEPT_ID: formData.PARENT_DEPT_ID ? parseInt(formData.PARENT_DEPT_ID) : null,
      MANAGER_USER_ID: formData.MANAGER_USER_ID ? parseInt(formData.MANAGER_USER_ID) : null,
      SORT_ORDER: parseInt(formData.SORT_ORDER) || 0,
      IS_ACTIVE: parseInt(formData.IS_ACTIVE)
    };

    onSubmit(submitData);
  };

  // 过滤父级部门选项（不能选择自己或子部门）
  const getFilteredParentOptions = () => {
    if (!departmentOptions) return [];
    
    // 如果是编辑模式，需要过滤掉自己和子部门
    if (department?.DEPT_ID) {
      return departmentOptions.filter(option => {
        // 不能选择自己
        if (option.value === department.DEPT_ID) return false;
        
        // 不能选择自己的子部门（简单判断：如果路径包含当前部门ID）
        // 这里可以根据实际的路径结构进行更精确的判断
        return true;
      });
    }
    
    return departmentOptions;
  };

  const isEditing = !!department?.DEPT_ID;
  const title = isEditing ? '编辑部门' : '添加部门';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-tech-dark-800 rounded-tech w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-tech-dark-600">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-tech-dark-600">
          <h2 className="text-xl font-semibold text-tech-gray-200">{title}</h2>
          <button
            onClick={onCancel}
            className="text-tech-gray-400 hover:text-tech-gray-200 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 部门编码 */}
            <div>
              <label className="block text-sm font-medium text-tech-gray-300 mb-2">
                部门编码 <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                value={formData.DEPT_CODE}
                onChange={(e) => handleInputChange('DEPT_CODE', e.target.value)}
                className={`w-full px-3 py-2 bg-tech-dark-700 border rounded-lg text-tech-gray-200 placeholder-tech-gray-400 focus:outline-none focus:ring-2 focus:ring-tech-blue-500 ${
                  errors.DEPT_CODE ? 'border-red-500' : 'border-tech-dark-500'
                }`}
                placeholder="请输入部门编码"
              />
              {errors.DEPT_CODE && (
                <p className="mt-1 text-sm text-red-400">{errors.DEPT_CODE}</p>
              )}
            </div>

            {/* 部门名称 */}
            <div>
              <label className="block text-sm font-medium text-tech-gray-300 mb-2">
                部门名称 <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                value={formData.DEPT_NAME}
                onChange={(e) => handleInputChange('DEPT_NAME', e.target.value)}
                className={`w-full px-3 py-2 bg-tech-dark-700 border rounded-lg text-tech-gray-200 placeholder-tech-gray-400 focus:outline-none focus:ring-2 focus:ring-tech-blue-500 ${
                  errors.DEPT_NAME ? 'border-red-500' : 'border-tech-dark-500'
                }`}
                placeholder="请输入部门名称"
              />
              {errors.DEPT_NAME && (
                <p className="mt-1 text-sm text-red-400">{errors.DEPT_NAME}</p>
              )}
            </div>

            {/* 上级部门 */}
            <div>
              <label className="block text-sm font-medium text-tech-gray-300 mb-2">
                上级部门
              </label>
              <select
                value={formData.PARENT_DEPT_ID}
                onChange={(e) => handleInputChange('PARENT_DEPT_ID', e.target.value)}
                className="w-full px-3 py-2 bg-tech-dark-700 border border-tech-dark-500 rounded-lg text-tech-gray-200 focus:outline-none focus:ring-2 focus:ring-tech-blue-500"
              >
                <option value="">无上级部门（顶级部门）</option>
                {getFilteredParentOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label} ({option.code})
                  </option>
                ))}
              </select>
            </div>

            {/* 排序 */}
            <div>
              <label className="block text-sm font-medium text-tech-gray-300 mb-2">
                排序
              </label>
              <input
                type="number"
                min="0"
                value={formData.SORT_ORDER}
                onChange={(e) => handleInputChange('SORT_ORDER', e.target.value)}
                className={`w-full px-3 py-2 bg-tech-dark-700 border rounded-lg text-tech-gray-200 placeholder-tech-gray-400 focus:outline-none focus:ring-2 focus:ring-tech-blue-500 ${
                  errors.SORT_ORDER ? 'border-red-500' : 'border-tech-dark-500'
                }`}
                placeholder="0"
              />
              {errors.SORT_ORDER && (
                <p className="mt-1 text-sm text-red-400">{errors.SORT_ORDER}</p>
              )}
            </div>
          </div>

          {/* 状态 */}
          <div>
            <label className="block text-sm font-medium text-tech-gray-300 mb-2">
              状态
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value={1}
                  checked={formData.IS_ACTIVE === 1}
                  onChange={(e) => handleInputChange('IS_ACTIVE', parseInt(e.target.value))}
                  className="mr-2 text-tech-blue-600 focus:ring-tech-blue-500"
                />
                <span className="text-tech-gray-200">启用</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value={0}
                  checked={formData.IS_ACTIVE === 0}
                  onChange={(e) => handleInputChange('IS_ACTIVE', parseInt(e.target.value))}
                  className="mr-2 text-tech-blue-600 focus:ring-tech-blue-500"
                />
                <span className="text-tech-gray-200">禁用</span>
              </label>
            </div>
          </div>

          {/* 描述 */}
          <div>
            <label className="block text-sm font-medium text-tech-gray-300 mb-2">
              描述
            </label>
            <textarea
              value={formData.DESCRIPTION}
              onChange={(e) => handleInputChange('DESCRIPTION', e.target.value)}
              rows={3}
              className={`w-full px-3 py-2 bg-tech-dark-700 border rounded-lg text-tech-gray-200 placeholder-tech-gray-400 focus:outline-none focus:ring-2 focus:ring-tech-blue-500 resize-none ${
                errors.DESCRIPTION ? 'border-red-500' : 'border-tech-dark-500'
              }`}
              placeholder="请输入部门描述（可选）"
            />
            {errors.DESCRIPTION && (
              <p className="mt-1 text-sm text-red-400">{errors.DESCRIPTION}</p>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-tech-dark-600">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 bg-tech-dark-700 hover:bg-tech-dark-600 text-tech-gray-200 rounded-lg transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-tech-blue-600 hover:bg-tech-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              {loading ? (isEditing ? '更新中...' : '创建中...') : (isEditing ? '更新' : '创建')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DepartmentForm;
