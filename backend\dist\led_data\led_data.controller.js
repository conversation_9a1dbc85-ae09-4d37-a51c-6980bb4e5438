"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedDataController = void 0;
const common_1 = require("@nestjs/common");
const led_data_service_1 = require("./led_data.service");
const create_data_dto_1 = require("./dto/create-data.dto");
const update_data_dto_1 = require("./dto/update-data.dto");
let LedDataController = class LedDataController {
    constructor(ledDataService) {
        this.ledDataService = ledDataService;
    }
    create(createDataDto) {
        return this.ledDataService.create(createDataDto);
    }
    findAll(query) {
        return this.ledDataService.findAll(query);
    }
    getStatistics(query) {
        return this.ledDataService.getStatistics(query);
    }
    exportData(query) {
        return this.ledDataService.exportData(query);
    }
    findOne(id) {
        return this.ledDataService.findOne(+id);
    }
    update(id, updateDataDto) {
        return this.ledDataService.update(+id, updateDataDto);
    }
    remove(id) {
        return this.ledDataService.remove(+id);
    }
};
exports.LedDataController = LedDataController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_data_dto_1.CreateDataDto]),
    __metadata("design:returntype", void 0)
], LedDataController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], LedDataController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], LedDataController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('export'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], LedDataController.prototype, "exportData", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LedDataController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_data_dto_1.UpdateDataDto]),
    __metadata("design:returntype", void 0)
], LedDataController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], LedDataController.prototype, "remove", null);
exports.LedDataController = LedDataController = __decorate([
    (0, common_1.Controller)('data'),
    __metadata("design:paramtypes", [led_data_service_1.LedDataService])
], LedDataController);
//# sourceMappingURL=led_data.controller.js.map