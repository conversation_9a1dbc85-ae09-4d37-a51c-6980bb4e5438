export declare class RouteOperationDto {
    id?: number;
    routeId?: number;
    operationId: number;
    sequenceNo: number;
    ledId?: string;
    xPosition?: number;
    yPosition?: number;
    nextOperationIds?: string;
    operationParams?: string;
    isActive?: number;
    operation?: any;
}
export declare class CreateRouteOperationDto extends RouteOperationDto {
}
export declare class UpdateRouteOperationDto {
    operationId?: number;
    sequenceNo?: number;
    ledId?: string;
    xPosition?: number;
    yPosition?: number;
    nextOperationIds?: string;
    operationParams?: string;
    isActive?: number;
}
export declare class RouteOperationResponseDto extends RouteOperationDto {
    createdBy: string;
    creationDate: Date;
    lastUpdatedBy: string;
    lastUpdateDate: Date;
}
