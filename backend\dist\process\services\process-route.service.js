"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessRouteService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const process_route_entity_1 = require("../entities/process-route.entity");
const route_operation_entity_1 = require("../entities/route-operation.entity");
let ProcessRouteService = class ProcessRouteService {
    constructor(processRouteRepository, routeOperationRepository, dataSource) {
        this.processRouteRepository = processRouteRepository;
        this.routeOperationRepository = routeOperationRepository;
        this.dataSource = dataSource;
    }
    async findAll(query = {}) {
        try {
            const page = query.page ? parseInt(query.page) : 1;
            const limit = query.limit ? parseInt(query.limit) : 10;
            const offset = (page - 1) * limit;
            const search = query.search;
            const isActive = query.isActive;
            let whereClause = '';
            if (search) {
                whereClause = `WHERE (route.ROUTE_CODE LIKE '%${search}%' OR 
                             route.ROUTE_NAME LIKE '%${search}%' OR 
                             route.DESCRIPTION LIKE '%${search}%')`;
            }
            if (isActive !== undefined && isActive !== '') {
                whereClause = whereClause ? `${whereClause} AND ` : 'WHERE ';
                whereClause += `route.IS_ACTIVE = ${isActive}`;
            }
            const countQuery = `SELECT COUNT(*) AS total FROM PROCESS_ROUTE route ${whereClause}`;
            const countResult = await this.dataSource.query(countQuery);
            const totalItems = parseInt(countResult[0].TOTAL);
            const sqlQuery = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT route.ROUTE_ID AS "id", 
                   route.ROUTE_CODE AS "code", 
                   route.ROUTE_NAME AS "name", 
                   route.DESCRIPTION AS "description",
                   route.IS_ACTIVE AS "isActive",
                   route.CREATED_BY AS "createdBy",
                   TO_CHAR(route.CREATION_DATE, 'YYYY-MM-DD HH24:MI:SS') AS "creationDate",
                   route.LAST_UPDATED_BY AS "lastUpdatedBy",
                   TO_CHAR(route.LAST_UPDATE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS "lastUpdateDate"
            FROM PROCESS_ROUTE route
            ${whereClause}
            ORDER BY route.ROUTE_ID DESC
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const routes = await this.dataSource.query(sqlQuery);
            const formattedRoutes = routes.map(route => {
                console.log('原始路由数据:', route);
                const result = {};
                for (const key in route) {
                    const lowercaseKey = key.toLowerCase();
                    result[lowercaseKey] = route[key];
                }
                console.log('格式化后的路由数据:', result);
                return result;
            });
            for (const route of formattedRoutes) {
                route.operations = await this.getRouteOperations(route.id);
            }
            return {
                items: formattedRoutes,
                meta: {
                    totalItems,
                    itemCount: formattedRoutes.length,
                    itemsPerPage: limit,
                    totalPages: Math.ceil(totalItems / limit),
                    currentPage: page,
                },
            };
        }
        catch (error) {
            console.error('获取工艺路线列表失败:', error);
            throw new common_1.InternalServerErrorException('获取工艺路线列表失败');
        }
    }
    async findOne(id) {
        try {
            const sqlQuery = `
        SELECT route.ROUTE_ID AS id, 
               route.ROUTE_CODE AS code, 
               route.ROUTE_NAME AS name, 
               route.DESCRIPTION AS description,
               route.IS_ACTIVE AS isActive,
               route.CREATED_BY AS createdBy,
               TO_CHAR(route.CREATION_DATE, 'YYYY-MM-DD HH24:MI:SS') AS creationDate,
               route.LAST_UPDATED_BY AS lastUpdatedBy,
               TO_CHAR(route.LAST_UPDATE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS lastUpdateDate
        FROM PROCESS_ROUTE route
        WHERE route.ROUTE_ID = :1
      `;
            const routes = await this.dataSource.query(sqlQuery, [id]);
            if (!routes || routes.length === 0) {
                throw new common_1.NotFoundException(`工艺路线ID ${id} 不存在`);
            }
            const formattedRoute = {};
            for (const key in routes[0]) {
                const lowercaseKey = key.toLowerCase();
                formattedRoute[lowercaseKey] = routes[0][key];
            }
            formattedRoute.operations = await this.getRouteOperations(id);
            return formattedRoute;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取工艺路线ID ${id} 失败:`, error);
            throw new common_1.InternalServerErrorException(`获取工艺路线失败`);
        }
    }
    async getRouteOperations(routeId) {
        try {
            const sqlQuery = `
        SELECT ro.ROUTE_OPERATION_ID AS id,
               ro.ROUTE_ID AS routeId,
               ro.OPERATION_ID AS operationId,
               ro.SEQUENCE_NO AS sequenceNo,
               ro.LED_ID AS ledId,
               ro.X_POSITION AS xPosition,
               ro.Y_POSITION AS yPosition,
               ro.NEXT_OPERATION_IDS AS nextOperationIds,
               ro.OPERATION_PARAMS AS operationParams,
               ro.IS_ACTIVE AS isActive,
               ro.CREATED_BY AS createdBy,
               TO_CHAR(ro.CREATION_DATE, 'YYYY-MM-DD HH24:MI:SS') AS creationDate,
               ro.LAST_UPDATED_BY AS lastUpdatedBy,
               TO_CHAR(ro.LAST_UPDATE_DATE, 'YYYY-MM-DD HH24:MI:SS') AS lastUpdateDate,
               op.OPERATION_CODE AS "operation.code",
               op.OPERATION_NAME AS "operation.name",
               op.STANDARD_TIME AS "operation.standardTime",
               op.OPERATION_TYPE AS "operation.type"
        FROM ROUTE_OPERATION ro
        LEFT JOIN PROCESS_OPERATION op ON ro.OPERATION_ID = op.OPERATION_ID
        WHERE ro.ROUTE_ID = :1
        ORDER BY ro.SEQUENCE_NO ASC
      `;
            const operations = await this.dataSource.query(sqlQuery, [routeId]);
            return operations.map(op => {
                const formattedOp = {};
                for (const key in op) {
                    if (key.includes('.')) {
                        formattedOp[key] = op[key];
                    }
                    else {
                        const lowercaseKey = key.toLowerCase();
                        formattedOp[lowercaseKey] = op[key];
                    }
                }
                const operation = {
                    id: formattedOp.operationid,
                    code: formattedOp['operation.code'],
                    name: formattedOp['operation.name'],
                    standardTime: formattedOp['operation.standardTime'],
                    type: formattedOp['operation.type']
                };
                delete formattedOp['operation.code'];
                delete formattedOp['operation.name'];
                delete formattedOp['operation.standardTime'];
                delete formattedOp['operation.type'];
                return {
                    ...formattedOp,
                    operation
                };
            });
        }
        catch (error) {
            console.error(`获取工艺路线 ${routeId} 的工序步骤失败:`, error);
            return [];
        }
    }
    async create(createDto, user) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const route = this.processRouteRepository.create({
                code: createDto.code,
                name: createDto.name,
                description: createDto.description,
                isActive: createDto.isActive ?? 1,
                createdBy: user?.username || 'system',
            });
            const savedRoute = await queryRunner.manager.save(route);
            if (createDto.operations && createDto.operations.length > 0) {
                const operationEntities = createDto.operations.map(op => {
                    return this.routeOperationRepository.create({
                        routeId: savedRoute.id,
                        operationId: op.operationId,
                        sequenceNo: op.sequenceNo,
                        ledId: op.ledId,
                        xPosition: op.xPosition,
                        yPosition: op.yPosition,
                        nextOperationIds: op.nextOperationIds,
                        operationParams: op.operationParams,
                        isActive: op.isActive ?? 1,
                        createdBy: user?.username || 'system',
                    });
                });
                await queryRunner.manager.save(route_operation_entity_1.RouteOperation, operationEntities);
            }
            await queryRunner.commitTransaction();
            return this.findOne(savedRoute.id);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            console.error('创建工艺路线失败:', error);
            throw new common_1.InternalServerErrorException(`创建工艺路线失败: ${error.message}`);
        }
        finally {
            await queryRunner.release();
        }
    }
    async update(id, updateDto, user) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const routeQuery = `
        SELECT route.ROUTE_ID AS id, 
               route.ROUTE_CODE AS code, 
               route.ROUTE_NAME AS name, 
               route.DESCRIPTION AS description,
               route.IS_ACTIVE AS isActive
        FROM PROCESS_ROUTE route
        WHERE route.ROUTE_ID = :1
      `;
            const routes = await queryRunner.manager.query(routeQuery, [id]);
            if (!routes || routes.length === 0) {
                throw new common_1.NotFoundException(`工艺路线ID ${id} 不存在`);
            }
            const route = {};
            for (const key in routes[0]) {
                const lowercaseKey = key.toLowerCase();
                route[lowercaseKey] = routes[0][key];
            }
            const updateQuery = `
        UPDATE PROCESS_ROUTE
        SET ROUTE_CODE = :1,
            ROUTE_NAME = :2,
            DESCRIPTION = :3,
            IS_ACTIVE = :4,
            LAST_UPDATED_BY = :5,
            LAST_UPDATE_DATE = SYSDATE
        WHERE ROUTE_ID = :6
      `;
            await queryRunner.manager.query(updateQuery, [
                updateDto.code !== undefined ? updateDto.code : route.code,
                updateDto.name !== undefined ? updateDto.name : route.name,
                updateDto.description !== undefined ? updateDto.description : route.description,
                updateDto.isActive !== undefined ? updateDto.isActive : route.isactive,
                user?.username || 'system',
                id
            ]);
            if (updateDto.operations && updateDto.operations.length > 0) {
                await queryRunner.manager.query(`DELETE FROM ROUTE_OPERATION WHERE ROUTE_ID = :1`, [id]);
                for (const op of updateDto.operations) {
                    const insertOpQuery = `
            INSERT INTO ROUTE_OPERATION (
              ROUTE_ID, OPERATION_ID, SEQUENCE_NO, LED_ID, 
              X_POSITION, Y_POSITION, NEXT_OPERATION_IDS, OPERATION_PARAMS, 
              IS_ACTIVE, CREATED_BY, CREATION_DATE
            ) VALUES (
              :1, :2, :3, :4,
              :5, :6, :7, :8,
              :9, :10, SYSDATE
            )
          `;
                    await queryRunner.manager.query(insertOpQuery, [
                        id,
                        op.operationId,
                        op.sequenceNo,
                        op.ledId || null,
                        op.xPosition || null,
                        op.yPosition || null,
                        op.nextOperationIds || null,
                        op.operationParams || null,
                        op.isActive ?? 1,
                        user?.username || 'system'
                    ]);
                }
            }
            await queryRunner.commitTransaction();
            return this.findOne(id);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`更新工艺路线ID ${id} 失败:`, error);
            throw new common_1.InternalServerErrorException(`更新工艺路线失败: ${error.message}`);
        }
        finally {
            await queryRunner.release();
        }
    }
    async remove(id) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const route = await this.processRouteRepository.findOne({ where: { id } });
            if (!route) {
                throw new common_1.NotFoundException(`工艺路线ID ${id} 不存在`);
            }
            await queryRunner.manager.delete(route_operation_entity_1.RouteOperation, { routeId: id });
            await queryRunner.manager.delete(process_route_entity_1.ProcessRoute, { id });
            await queryRunner.commitTransaction();
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`删除工艺路线ID ${id} 失败:`, error);
            throw new common_1.InternalServerErrorException('删除工艺路线失败');
        }
        finally {
            await queryRunner.release();
        }
    }
    async getRouteOptions() {
        try {
            const sqlQuery = `
        SELECT route.ROUTE_ID AS id, 
               route.ROUTE_CODE AS code, 
               route.ROUTE_NAME AS name
        FROM PROCESS_ROUTE route
        WHERE route.IS_ACTIVE = 1
        ORDER BY route.ROUTE_ID DESC
      `;
            const routes = await this.dataSource.query(sqlQuery);
            return { items: routes };
        }
        catch (error) {
            console.error('获取工艺路线选项失败:', error);
            return { items: [] };
        }
    }
};
exports.ProcessRouteService = ProcessRouteService;
exports.ProcessRouteService = ProcessRouteService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(process_route_entity_1.ProcessRoute)),
    __param(1, (0, typeorm_1.InjectRepository)(route_operation_entity_1.RouteOperation)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], ProcessRouteService);
//# sourceMappingURL=process-route.service.js.map