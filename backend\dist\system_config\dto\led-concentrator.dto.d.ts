export declare class LedMappingForConcentratorDto {
    id?: number;
    concentratorId?: number;
    ledId: string;
    channelNumber?: string;
    description?: string;
    isActive?: boolean;
}
export declare class CreateConcentratorDto {
    code: string;
    name: string;
    ipAddress: string;
    portNumber: number;
    protocolType: string;
    timeoutSeconds?: number;
    maxRetryCount?: number;
    areaCode?: string;
    description?: string;
    isActive?: boolean;
    ledMappings?: LedMappingForConcentratorDto[];
}
export declare class UpdateConcentratorDto {
    code?: string;
    name?: string;
    ipAddress?: string;
    portNumber?: number;
    protocolType?: string;
    timeoutSeconds?: number;
    maxRetryCount?: number;
    areaCode?: string;
    description?: string;
    isActive?: boolean;
    ledMappings?: LedMappingForConcentratorDto[];
}
export declare class ConcentratorQueryDto {
    page?: number;
    limit?: number;
    search?: string;
    code?: string;
    name?: string;
    ipAddress?: string;
    areaCode?: string;
    isActive?: boolean;
}
