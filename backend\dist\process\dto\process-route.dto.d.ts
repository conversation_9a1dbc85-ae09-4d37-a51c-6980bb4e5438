import { RouteOperationDto } from './route-operation.dto';
export declare class CreateProcessRouteDto {
    code: string;
    name: string;
    description?: string;
    isActive?: number;
    operations?: RouteOperationDto[];
}
export declare class UpdateProcessRouteDto {
    code?: string;
    name?: string;
    description?: string;
    isActive?: number;
    operations?: RouteOperationDto[];
}
export declare class ProcessRouteResponseDto {
    id: number;
    code: string;
    name: string;
    description: string;
    isActive: number;
    createdBy: string;
    creationDate: Date;
    lastUpdatedBy: string;
    lastUpdateDate: Date;
    operations?: RouteOperationDto[];
}
