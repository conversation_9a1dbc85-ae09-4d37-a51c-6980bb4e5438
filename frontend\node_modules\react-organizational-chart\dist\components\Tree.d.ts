/// <reference types="react" />
import { TreeNodeProps } from './TreeNode';
export interface TreeProps {
    /**
     * The root label
     * */
    label: TreeNodeProps['label'];
    /**
     * The height of the line
     */
    lineHeight?: string;
    /**
     * The width of the line
     */
    lineWidth?: string;
    /**
     * The color of the line
     */
    lineColor?: string;
    /**
     * The line style for the tree
     */
    lineStyle?: 'dashed' | 'dotted' | 'double' | 'solid' | string;
    /**
     * The border radius of the line
     */
    lineBorderRadius?: string;
    /**
     * The padding between siblings
     */
    nodePadding?: string;
    children: TreeNodeProps['children'];
}
/**
 * The root of the hierarchy tree
 */
declare function Tree({ children, label, lineHeight, lineWidth, lineColor, nodePadding, lineStyle, lineBorderRadius, }: TreeProps): JSX.Element;
export default Tree;
