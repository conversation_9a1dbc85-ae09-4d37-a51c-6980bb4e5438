"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessRouteController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const process_route_service_1 = require("../services/process-route.service");
const process_route_dto_1 = require("../dto/process-route.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let ProcessRouteController = class ProcessRouteController {
    constructor(processRouteService) {
        this.processRouteService = processRouteService;
    }
    async findAll(query) {
        return this.processRouteService.findAll(query);
    }
    async getRouteOptions() {
        return this.processRouteService.getRouteOptions();
    }
    async findOne(id) {
        return this.processRouteService.findOne(+id);
    }
    async create(createDto, req) {
        return this.processRouteService.create(createDto, req.user);
    }
    async update(id, updateDto, req) {
        return this.processRouteService.update(+id, updateDto, req.user);
    }
    async remove(id) {
        return this.processRouteService.remove(+id);
    }
};
exports.ProcessRouteController = ProcessRouteController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取工艺路线列表' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功获取工艺路线列表', type: [process_route_dto_1.ProcessRouteResponseDto] }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProcessRouteController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('options'),
    (0, swagger_1.ApiOperation)({ summary: '获取工艺路线选项' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功获取工艺路线选项' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ProcessRouteController.prototype, "getRouteOptions", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取工艺路线详情' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功获取工艺路线详情', type: process_route_dto_1.ProcessRouteResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '工艺路线不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProcessRouteController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建工艺路线' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: '成功创建工艺路线', type: process_route_dto_1.ProcessRouteResponseDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [process_route_dto_1.CreateProcessRouteDto, Object]),
    __metadata("design:returntype", Promise)
], ProcessRouteController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新工艺路线' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功更新工艺路线', type: process_route_dto_1.ProcessRouteResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '工艺路线不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, process_route_dto_1.UpdateProcessRouteDto, Object]),
    __metadata("design:returntype", Promise)
], ProcessRouteController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除工艺路线' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功删除工艺路线' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: '工艺路线不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProcessRouteController.prototype, "remove", null);
exports.ProcessRouteController = ProcessRouteController = __decorate([
    (0, swagger_1.ApiTags)('工艺路线管理'),
    (0, common_1.Controller)('process/routes'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [process_route_service_1.ProcessRouteService])
], ProcessRouteController);
//# sourceMappingURL=process-route.controller.js.map