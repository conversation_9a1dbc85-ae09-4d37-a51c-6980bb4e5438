"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
let RoleService = class RoleService {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async create(createRoleDto) {
        try {
            const existingRole = await this.dataSource.query(`SELECT ROLE_ID FROM LED_ROLE WHERE ROLE_CODE = :1`, [createRoleDto.ROLE_CODE]);
            if (existingRole.length > 0) {
                throw new common_1.BadRequestException(`角色编码 ${createRoleDto.ROLE_CODE} 已存在`);
            }
            if (createRoleDto.DEPT_ID) {
                const dept = await this.dataSource.query(`SELECT DEPT_ID FROM LED_DEPARTMENT WHERE DEPT_ID = :1`, [createRoleDto.DEPT_ID]);
                if (dept.length === 0) {
                    throw new common_1.BadRequestException('指定的事业部不存在');
                }
            }
            const insertQuery = `
        INSERT INTO LED_ROLE(
          ROLE_CODE, ROLE_NAME, ROLE_TYPE, DEPT_ID, DESCRIPTION, 
          IS_ACTIVE, CREATED_BY, CREATION_DATE
        ) VALUES(
          :1, :2, :3, :4, :5, 1, 'system', SYSDATE
        )
      `;
            await this.dataSource.query(insertQuery, [
                createRoleDto.ROLE_CODE,
                createRoleDto.ROLE_NAME,
                createRoleDto.ROLE_TYPE || 'CUSTOM',
                createRoleDto.DEPT_ID || null,
                createRoleDto.DESCRIPTION || null
            ]);
            const newRole = await this.dataSource.query(`SELECT * FROM LED_ROLE WHERE ROLE_CODE = :1`, [createRoleDto.ROLE_CODE]);
            const roleId = newRole[0].ROLE_ID;
            if (createRoleDto.menuIds && createRoleDto.menuIds.length > 0) {
                await this.assignMenus(roleId, { menuIds: createRoleDto.menuIds });
            }
            return this.findOne(roleId);
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            console.error('创建角色失败:', error);
            throw new common_1.InternalServerErrorException('创建角色失败');
        }
    }
    async findAll(params = {}) {
        try {
            const page = params.page ? parseInt(params.page) : 1;
            const limit = params.limit ? parseInt(params.limit) : 10;
            const offset = (page - 1) * limit;
            const search = params.search;
            const roleType = params.roleType;
            const deptId = params.deptId;
            let whereClause = 'WHERE r.IS_ACTIVE = 1';
            const queryParams = [];
            let paramIndex = 1;
            if (search) {
                whereClause += ` AND (r.ROLE_CODE LIKE :${paramIndex} OR r.ROLE_NAME LIKE :${paramIndex})`;
                queryParams.push(`%${search}%`);
                paramIndex++;
            }
            if (roleType) {
                whereClause += ` AND r.ROLE_TYPE = :${paramIndex}`;
                queryParams.push(roleType);
                paramIndex++;
            }
            if (deptId) {
                whereClause += ` AND r.DEPT_ID = :${paramIndex}`;
                queryParams.push(deptId);
                paramIndex++;
            }
            const countQuery = `SELECT COUNT(*) AS total FROM LED_ROLE r ${whereClause}`;
            const countResult = await this.dataSource.query(countQuery, queryParams);
            const totalItems = parseInt(countResult[0].TOTAL);
            const query = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT r.*, 
                   d.DEPT_NAME as DEPT_NAME
            FROM LED_ROLE r
            LEFT JOIN LED_DEPARTMENT d ON r.DEPT_ID = d.DEPT_ID
            ${whereClause}
            ORDER BY r.ROLE_TYPE, r.ROLE_CODE
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const roles = await this.dataSource.query(query, queryParams);
            return {
                items: roles,
                meta: {
                    totalItems,
                    itemsPerPage: limit,
                    currentPage: page,
                    totalPages: Math.ceil(totalItems / limit)
                }
            };
        }
        catch (error) {
            console.error('获取角色列表失败:', error);
            throw new common_1.InternalServerErrorException('获取角色列表失败');
        }
    }
    async findOne(id) {
        try {
            const roles = await this.dataSource.query(`SELECT r.*, 
                d.DEPT_NAME as DEPT_NAME
         FROM LED_ROLE r
         LEFT JOIN LED_DEPARTMENT d ON r.DEPT_ID = d.DEPT_ID
         WHERE r.ROLE_ID = :1`, [id]);
            if (!roles || roles.length === 0) {
                throw new common_1.NotFoundException(`ID为${id}的角色不存在`);
            }
            return roles[0];
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取ID为${id}的角色失败:`, error);
            throw new common_1.InternalServerErrorException(`获取ID为${id}的角色失败`);
        }
    }
    async update(id, updateRoleDto) {
        try {
            await this.findOne(id);
            if (updateRoleDto.ROLE_CODE) {
                const existingRole = await this.dataSource.query(`SELECT ROLE_ID FROM LED_ROLE WHERE ROLE_CODE = :1 AND ROLE_ID != :2`, [updateRoleDto.ROLE_CODE, id]);
                if (existingRole.length > 0) {
                    throw new common_1.BadRequestException(`角色编码 ${updateRoleDto.ROLE_CODE} 已被其他角色使用`);
                }
            }
            if (updateRoleDto.DEPT_ID) {
                const dept = await this.dataSource.query(`SELECT DEPT_ID FROM LED_DEPARTMENT WHERE DEPT_ID = :1`, [updateRoleDto.DEPT_ID]);
                if (dept.length === 0) {
                    throw new common_1.BadRequestException('指定的事业部不存在');
                }
            }
            let updateQuery = 'UPDATE LED_ROLE SET ';
            const updateValues = [];
            let paramIndex = 1;
            if (updateRoleDto.ROLE_CODE !== undefined) {
                updateQuery += `ROLE_CODE = :${paramIndex}, `;
                updateValues.push(updateRoleDto.ROLE_CODE);
                paramIndex++;
            }
            if (updateRoleDto.ROLE_NAME !== undefined) {
                updateQuery += `ROLE_NAME = :${paramIndex}, `;
                updateValues.push(updateRoleDto.ROLE_NAME);
                paramIndex++;
            }
            if (updateRoleDto.ROLE_TYPE !== undefined) {
                updateQuery += `ROLE_TYPE = :${paramIndex}, `;
                updateValues.push(updateRoleDto.ROLE_TYPE);
                paramIndex++;
            }
            if (updateRoleDto.DEPT_ID !== undefined) {
                updateQuery += `DEPT_ID = :${paramIndex}, `;
                updateValues.push(updateRoleDto.DEPT_ID);
                paramIndex++;
            }
            if (updateRoleDto.DESCRIPTION !== undefined) {
                updateQuery += `DESCRIPTION = :${paramIndex}, `;
                updateValues.push(updateRoleDto.DESCRIPTION);
                paramIndex++;
            }
            updateQuery += `LAST_UPDATE_DATE = SYSDATE, LAST_UPDATED_BY = :${paramIndex} `;
            updateValues.push('system');
            paramIndex++;
            updateQuery += `WHERE ROLE_ID = :${paramIndex}`;
            updateValues.push(id);
            await this.dataSource.query(updateQuery, updateValues);
            if (updateRoleDto.menuIds !== undefined) {
                await this.assignMenus(id, { menuIds: updateRoleDto.menuIds });
            }
            return this.findOne(id);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            console.error(`更新ID为${id}的角色失败:`, error);
            throw new common_1.InternalServerErrorException(`更新ID为${id}的角色失败`);
        }
    }
    async remove(id) {
        try {
            await this.findOne(id);
            const users = await this.dataSource.query(`SELECT COUNT(*) AS count FROM LED_USER_ROLE WHERE ROLE_ID = :1`, [id]);
            if (users[0].COUNT > 0) {
                throw new common_1.BadRequestException('该角色正在被用户使用，无法删除');
            }
            await this.dataSource.query(`DELETE FROM LED_ROLE_MENU WHERE ROLE_ID = :1`, [id]);
            await this.dataSource.query(`DELETE FROM LED_ROLE WHERE ROLE_ID = :1`, [id]);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            console.error(`删除ID为${id}的角色失败:`, error);
            throw new common_1.InternalServerErrorException(`删除ID为${id}的角色失败`);
        }
    }
    async assignMenus(roleId, assignMenuDto) {
        try {
            await this.findOne(roleId);
            if (assignMenuDto.menuIds.length > 0) {
                const menuIds = assignMenuDto.menuIds.join(',');
                const menus = await this.dataSource.query(`SELECT MENU_ID FROM LED_MENU WHERE MENU_ID IN (${menuIds})`);
                if (menus.length !== assignMenuDto.menuIds.length) {
                    throw new common_1.BadRequestException('部分菜单ID不存在');
                }
            }
            await this.dataSource.query(`DELETE FROM LED_ROLE_MENU WHERE ROLE_ID = :1`, [roleId]);
            if (assignMenuDto.menuIds.length > 0) {
                const insertValues = assignMenuDto.menuIds.map(menuId => `(${roleId}, ${menuId}, 1, 'system', SYSDATE)`).join(',');
                const insertQuery = `
          INSERT INTO LED_ROLE_MENU(ROLE_ID, MENU_ID, IS_ACTIVE, CREATED_BY, CREATION_DATE)
          VALUES ${insertValues}
        `;
                await this.dataSource.query(insertQuery);
            }
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            console.error(`为角色${roleId}分配菜单权限失败:`, error);
            throw new common_1.InternalServerErrorException('分配菜单权限失败');
        }
    }
    async getRoleMenus(roleId) {
        try {
            await this.findOne(roleId);
            const query = `
        SELECT m.*, rm.ROLE_MENU_ID
        FROM LED_MENU m
        JOIN LED_ROLE_MENU rm ON m.MENU_ID = rm.MENU_ID
        WHERE rm.ROLE_ID = :1 AND rm.IS_ACTIVE = 1 AND m.IS_ACTIVE = 1
        ORDER BY m.SORT_ORDER, m.MENU_CODE
      `;
            return await this.dataSource.query(query, [roleId]);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取角色${roleId}的菜单权限失败:`, error);
            throw new common_1.InternalServerErrorException('获取角色菜单权限失败');
        }
    }
    async getRoleTypes() {
        return [
            { value: 'SYSTEM', label: '系统角色', description: '系统内置角色，拥有系统级权限' },
            { value: 'DEPT', label: '部门角色', description: '部门级角色，权限范围限定在特定事业部' },
            { value: 'CUSTOM', label: '自定义角色', description: '用户自定义角色，可灵活配置权限' }
        ];
    }
};
exports.RoleService = RoleService;
exports.RoleService = RoleService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], RoleService);
//# sourceMappingURL=role.service.js.map