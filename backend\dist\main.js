"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
if (process.env.NODE_ENV !== 'production') {
    require('ts-node/register');
    require('tsconfig-paths/register');
}
const oracledb = require('oracledb');
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const express_1 = require("express");
const swagger_1 = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
const seed_1 = require("./database/seeds/seed");
console.log('Initializing OracleDB client...');
const oracleClientPath = process.env.ORACLE_CLIENT_PATH || 'D:\\instantclient_19_9';
console.log('Using Oracle client path:', oracleClientPath);
try {
    oracledb.initOracleClient({ libDir: oracleClientPath });
    console.log('OracleDB client initialized successfully.');
}
catch (err) {
    console.error('OracleDB client initialization failed:', err);
    console.error('Please ensure Oracle Instant Client is installed at:', oracleClientPath);
    process.exit(1);
}
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    app.use((0, express_1.json)({ limit: '10mb' }));
    app.use((0, express_1.urlencoded)({ extended: true, limit: '10mb' }));
    app.setGlobalPrefix('api');
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: false,
    }));
    app.enableCors({
        origin: configService.get('FRONTEND_URL') || 'http://localhost:5173',
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        credentials: true,
    });
    const config = new swagger_1.DocumentBuilder()
        .setTitle('LED数据看板系统API')
        .setDescription('LED数据看板系统API文档')
        .setVersion('1.0')
        .addTag('led-sjd')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api-docs', app, document);
    const dataSource = app.get(typeorm_1.DataSource);
    await (0, seed_1.runSeeds)(dataSource);
    const port = configService.get('PORT') || 3000;
    await app.listen(port);
    console.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap();
//# sourceMappingURL=main.js.map