import { useState, useEffect } from 'react';

/**
 * 自定义钩子，用于检测媒体查询是否匹配
 * @param {string} query - CSS媒体查询字符串，例如 '(max-width: 768px)'
 * @returns {boolean} - 媒体查询是否匹配
 */
export const useMediaQuery = (query) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    // 创建媒体查询
    const mediaQuery = window.matchMedia(query);
    
    // 设置初始值
    setMatches(mediaQuery.matches);

    // 定义事件处理函数
    const handleChange = (event) => {
      setMatches(event.matches);
    };

    // 添加事件监听器
    mediaQuery.addEventListener('change', handleChange);

    // 清理函数
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [query]);

  return matches;
};

export default useMediaQuery; 