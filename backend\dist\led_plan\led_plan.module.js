"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedPlanModule = void 0;
const common_1 = require("@nestjs/common");
const led_plan_service_1 = require("./led_plan.service");
const led_plan_controller_1 = require("./led_plan.controller");
const typeorm_1 = require("@nestjs/typeorm");
const led_plan_entity_1 = require("./entities/led-plan.entity");
const platform_express_1 = require("@nestjs/platform-express");
let LedPlanModule = class LedPlanModule {
};
exports.LedPlanModule = LedPlanModule;
exports.LedPlanModule = LedPlanModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([led_plan_entity_1.LedPlan]),
            platform_express_1.MulterModule.register({
                limits: {
                    fileSize: 10 * 1024 * 1024,
                },
                preservePath: true,
            }),
        ],
        controllers: [led_plan_controller_1.LedPlanController],
        providers: [led_plan_service_1.LedPlanService],
        exports: [led_plan_service_1.LedPlanService],
    })
], LedPlanModule);
//# sourceMappingURL=led_plan.module.js.map