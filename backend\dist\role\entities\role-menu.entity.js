"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleMenu = void 0;
const typeorm_1 = require("typeorm");
const role_entity_1 = require("./role.entity");
const menu_entity_1 = require("./menu.entity");
let RoleMenu = class RoleMenu {
};
exports.RoleMenu = RoleMenu;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ROLE_MENU_ID' }),
    __metadata("design:type", Number)
], RoleMenu.prototype, "ROLE_MENU_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ROLE_ID', nullable: false }),
    __metadata("design:type", Number)
], RoleMenu.prototype, "ROLE_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'MENU_ID', nullable: false }),
    __metadata("design:type", Number)
], RoleMenu.prototype, "MENU_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', default: 1 }),
    __metadata("design:type", Number)
], RoleMenu.prototype, "IS_ACTIVE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', nullable: true }),
    __metadata("design:type", String)
], RoleMenu.prototype, "CREATED_BY", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATION_DATE', type: 'date', default: () => 'SYSDATE' }),
    __metadata("design:type", Date)
], RoleMenu.prototype, "CREATION_DATE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', nullable: true }),
    __metadata("design:type", String)
], RoleMenu.prototype, "LAST_UPDATED_BY", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATE_DATE', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], RoleMenu.prototype, "LAST_UPDATE_DATE", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => role_entity_1.Role),
    (0, typeorm_1.JoinColumn)({ name: 'ROLE_ID' }),
    __metadata("design:type", role_entity_1.Role)
], RoleMenu.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => menu_entity_1.Menu),
    (0, typeorm_1.JoinColumn)({ name: 'MENU_ID' }),
    __metadata("design:type", menu_entity_1.Menu)
], RoleMenu.prototype, "menu", void 0);
exports.RoleMenu = RoleMenu = __decorate([
    (0, typeorm_1.Entity)('LED_ROLE_MENU')
], RoleMenu);
//# sourceMappingURL=role-menu.entity.js.map