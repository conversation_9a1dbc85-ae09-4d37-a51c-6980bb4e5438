import { ManualDispatchService } from './manual-dispatch.service';
import { LedMappingService } from '../system_config/services/led-mapping.service';
import { CreateLedMappingDto } from '../system_config/dto/led-mapping.dto';
declare class BatchDataItemDto {
    ledId: string;
    planValue: number;
    workType?: string;
}
declare class BatchSendDataDto {
    data: BatchDataItemDto[];
}
export declare class ManualDispatchController {
    private readonly manualDispatchService;
    private readonly ledMappingService;
    private readonly logger;
    constructor(manualDispatchService: ManualDispatchService, ledMappingService: LedMappingService);
    getAllConcentrators(): Promise<any[]>;
    getLedMapping(ledId: string): Promise<{
        id: any;
        concentratorId: any;
        ledId: any;
        channelNumber: any;
        description: any;
        isActive: boolean;
        concentrator: {
            id: any;
            name: any;
            code: any;
            ipAddress: any;
            portNumber: any;
            protocolType: any;
            timeoutSeconds: any;
            maxRetryCount: any;
        };
        ledName: any;
    }>;
    sendDataToLed(ledId: string, data: any): Promise<{
        success: boolean;
        message: string;
        details: {
            ledId: string;
            concentratorId: any;
            concentratorName: any;
            channelNumber: any;
            planValue: number;
            workType: string;
            workTypeText: string;
            timestamp: Date;
            planRecordId: any;
        };
    }>;
    readDataFromLed(ledId: string): Promise<{
        success: boolean;
        message: string;
        data: {
            ledId: string;
            planValue: number;
            actualValue: number;
            timestamp: Date;
        };
    }>;
    createMapping(createMappingDto: CreateLedMappingDto): Promise<any>;
    sendBatchDataToLeds(batchData: BatchSendDataDto): Promise<{
        total: number;
        success: number;
        failed: number;
        results: any[];
        errors: any[];
    }>;
    testRegisters(): Promise<{
        success: boolean;
        rawRegisters: number[];
        ledDevices: any[];
        message: string;
    }>;
    debugBatch(batchData: BatchSendDataDto): Promise<{
        total: number;
        success: number;
        failed: number;
        results: any[];
        errors: any[];
    }>;
    getAllLedDataByConcentrator(concentratorId: string): Promise<{
        concentratorId: number;
        ledDevices: any[];
        message: string;
        concentratorName?: undefined;
        summary?: undefined;
    } | {
        concentratorId: number;
        concentratorName: any;
        ledDevices: any[];
        summary: {
            total: number;
            success: number;
            failed: number;
        };
        message?: undefined;
    }>;
    getLedDataHistory(ledId: string, page?: string, limit?: string): Promise<{
        items: any;
        pagination: {
            page: number;
            limit: number;
            totalItems: number;
            totalPages: number;
        };
    }>;
    getRecentDispatchData(page?: string, limit?: string): Promise<{
        items: any;
        pagination: {
            page: number;
            limit: number;
            totalItems: number;
            totalPages: number;
        };
    }>;
    getModbusStatus(): Promise<{
        mode: string;
        description: string;
        isConnected: boolean;
        environment: string;
    }>;
    testModbusConnection(testData: {
        ip: string;
        port: number;
    }): Promise<{
        success: boolean;
        message: string;
        duration: number;
        testData?: undefined;
        mode?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        duration: number;
        testData: number[];
        mode: string;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        duration: number;
        error: any;
        testData?: undefined;
        mode?: undefined;
    }>;
    testPlanTable(): Promise<{
        tableExists: boolean;
        totalRecords: any;
        structure: any;
        sampleData: any;
        error?: undefined;
    } | {
        tableExists: boolean;
        error: any;
        totalRecords?: undefined;
        structure?: undefined;
        sampleData?: undefined;
    }>;
    testInsertPlan(data: {
        ledId: string;
        planValue: number;
        workType?: string;
    }): Promise<{
        success: boolean;
        message: string;
        result: {
            id: any;
            ledId: any;
            planValue: any;
            planDate: any;
            planType: any;
            workType: any;
            pushStatus: any;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        result?: undefined;
    }>;
}
export {};
