{"hash": "9e0e5adb", "browserHash": "f0845aaa", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "8a9a4c96", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "f8d5bbe3", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "30794f8d", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "777eef99", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "9b376e2a", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.esm.js", "file": "@reduxjs_toolkit.js", "fileHash": "768179b5", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/lib/index.mjs", "file": "@tanstack_react-query.js", "fileHash": "0b67badc", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "e5e9baf3", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "6d252928", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "0c9b2b85", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "27ba5208", "needsInterop": false}, "react-organizational-chart": {"src": "../../react-organizational-chart/dist/index.module.js", "file": "react-organizational-chart.js", "fileHash": "2d74d7cf", "needsInterop": false}, "react-redux": {"src": "../../react-redux/es/index.js", "file": "react-redux.js", "fileHash": "a0b1e988", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "479e41fa", "needsInterop": false}, "reactflow": {"src": "../../reactflow/dist/esm/index.mjs", "file": "reactflow.js", "fileHash": "325bc23d", "needsInterop": false}}, "chunks": {"chunk-TOTO4VJY": {"file": "chunk-TOTO4VJY.js"}, "chunk-WLPH6RON": {"file": "chunk-WLPH6RON.js"}, "chunk-WALXKXZM": {"file": "chunk-WALXKXZM.js"}, "chunk-WQMOH32Y": {"file": "chunk-WQMOH32Y.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}