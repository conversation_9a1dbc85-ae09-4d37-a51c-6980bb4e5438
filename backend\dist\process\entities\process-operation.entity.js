"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessOperation = void 0;
const typeorm_1 = require("typeorm");
let ProcessOperation = class ProcessOperation {
};
exports.ProcessOperation = ProcessOperation;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'OPERATION_ID' }),
    __metadata("design:type", Number)
], ProcessOperation.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'OPERATION_CODE', length: 50, unique: true }),
    __metadata("design:type", String)
], ProcessOperation.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'OPERATION_NAME', length: 100 }),
    __metadata("design:type", String)
], ProcessOperation.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRIPTION', length: 500, nullable: true }),
    __metadata("design:type", String)
], ProcessOperation.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'STANDARD_TIME', type: 'float', nullable: true }),
    __metadata("design:type", Number)
], ProcessOperation.prototype, "standardTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'OPERATION_TYPE', length: 50, nullable: true }),
    __metadata("design:type", String)
], ProcessOperation.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', type: 'number', default: 1 }),
    __metadata("design:type", Number)
], ProcessOperation.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', length: 50, nullable: true }),
    __metadata("design:type", String)
], ProcessOperation.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATION_DATE', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], ProcessOperation.prototype, "creationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', length: 50, nullable: true }),
    __metadata("design:type", String)
], ProcessOperation.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATE_DATE', type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ProcessOperation.prototype, "lastUpdateDate", void 0);
exports.ProcessOperation = ProcessOperation = __decorate([
    (0, typeorm_1.Entity)('PROCESS_OPERATION')
], ProcessOperation);
//# sourceMappingURL=process-operation.entity.js.map