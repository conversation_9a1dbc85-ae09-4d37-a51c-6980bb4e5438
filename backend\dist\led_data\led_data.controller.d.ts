import { LedDataService } from './led_data.service';
import { CreateDataDto } from './dto/create-data.dto';
import { UpdateDataDto } from './dto/update-data.dto';
export declare class LedDataController {
    private readonly ledDataService;
    constructor(ledDataService: LedDataService);
    create(createDataDto: CreateDataDto): Promise<any>;
    findAll(query: any): Promise<{
        items: any[];
        meta: any;
    }>;
    getStatistics(query: any): Promise<any>;
    exportData(query: any): Promise<any[]>;
    findOne(id: string): Promise<any>;
    update(id: string, updateDataDto: UpdateDataDto): Promise<import("./entities/led-data.entity").LedData>;
    remove(id: string): Promise<void>;
}
