import { ProcessRoute } from './process-route.entity';
import { ProcessOperation } from './process-operation.entity';
export declare class RouteOperation {
    id: number;
    routeId: number;
    operationId: number;
    sequenceNo: number;
    ledId: string;
    xPosition: number;
    yPosition: number;
    nextOperationIds: string;
    operationParams: string;
    isActive: number;
    createdBy: string;
    creationDate: Date;
    lastUpdatedBy: string;
    lastUpdateDate: Date;
    route: ProcessRoute;
    operation: ProcessOperation;
}
