"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LedConcentratorController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedConcentratorController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const led_concentrator_service_1 = require("../services/led-concentrator.service");
const led_concentrator_dto_1 = require("../dto/led-concentrator.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let LedConcentratorController = LedConcentratorController_1 = class LedConcentratorController {
    constructor(ledConcentratorService) {
        this.ledConcentratorService = ledConcentratorService;
        this.logger = new common_1.Logger(LedConcentratorController_1.name);
    }
    async create(createDto) {
        this.logger.log(`创建LED集中器: ${JSON.stringify(createDto)}`);
        return this.ledConcentratorService.create(createDto);
    }
    async findAll(query) {
        return this.ledConcentratorService.findAll(query);
    }
    async findOne(id) {
        return this.ledConcentratorService.findOne(+id);
    }
    async update(id, updateDto) {
        this.logger.log(`更新LED集中器 ${id}: ${JSON.stringify(updateDto)}`);
        return this.ledConcentratorService.update(+id, updateDto);
    }
    async remove(id) {
        this.logger.log(`删除LED集中器 ${id}`);
        return this.ledConcentratorService.remove(+id);
    }
    async testConnection(id) {
        return this.ledConcentratorService.testConnection(+id);
    }
};
exports.LedConcentratorController = LedConcentratorController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建新集中器' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: '成功创建集中器' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [led_concentrator_dto_1.CreateConcentratorDto]),
    __metadata("design:returntype", Promise)
], LedConcentratorController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有集中器' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功获取集中器列表' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [led_concentrator_dto_1.ConcentratorQueryDto]),
    __metadata("design:returntype", Promise)
], LedConcentratorController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定集中器' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功获取集中器详情' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LedConcentratorController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新集中器' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功更新集中器' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, led_concentrator_dto_1.UpdateConcentratorDto]),
    __metadata("design:returntype", Promise)
], LedConcentratorController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除集中器' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '成功删除集中器' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LedConcentratorController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/test-connection'),
    (0, swagger_1.ApiOperation)({ summary: '测试集中器连接' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '连接测试完成' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LedConcentratorController.prototype, "testConnection", null);
exports.LedConcentratorController = LedConcentratorController = LedConcentratorController_1 = __decorate([
    (0, swagger_1.ApiTags)('集中器管理'),
    (0, common_1.Controller)('system-config/concentrators'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [led_concentrator_service_1.LedConcentratorService])
], LedConcentratorController);
//# sourceMappingURL=led-concentrator.controller.js.map