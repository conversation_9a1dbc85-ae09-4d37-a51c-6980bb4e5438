"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDepartmentDto = void 0;
const class_validator_1 = require("class-validator");
class CreateDepartmentDto {
}
exports.CreateDepartmentDto = CreateDepartmentDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2, { message: '事业部编码至少需要2个字符' }),
    (0, class_validator_1.MaxLength)(50, { message: '事业部编码不能超过50个字符' }),
    __metadata("design:type", String)
], CreateDepartmentDto.prototype, "DEPT_CODE", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2, { message: '事业部名称至少需要2个字符' }),
    (0, class_validator_1.MaxLength)(100, { message: '事业部名称不能超过100个字符' }),
    __metadata("design:type", String)
], CreateDepartmentDto.prototype, "DEPT_NAME", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '父级事业部ID必须是数字' }),
    __metadata("design:type", Number)
], CreateDepartmentDto.prototype, "PARENT_DEPT_ID", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '事业部层级必须是数字' }),
    __metadata("design:type", Number)
], CreateDepartmentDto.prototype, "DEPT_LEVEL", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500, { message: '事业部路径不能超过500个字符' }),
    __metadata("design:type", String)
], CreateDepartmentDto.prototype, "DEPT_PATH", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '负责人ID必须是数字' }),
    __metadata("design:type", Number)
], CreateDepartmentDto.prototype, "MANAGER_USER_ID", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500, { message: '描述不能超过500个字符' }),
    __metadata("design:type", String)
], CreateDepartmentDto.prototype, "DESCRIPTION", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序必须是数字' }),
    __metadata("design:type", Number)
], CreateDepartmentDto.prototype, "SORT_ORDER", void 0);
//# sourceMappingURL=create-department.dto.js.map