"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPermissionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
let UserPermissionService = class UserPermissionService {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async assignUserRoles(userId, assignUserRoleDto) {
        try {
            const user = await this.dataSource.query(`SELECT ID FROM LED_USERS WHERE ID = :1`, [userId]);
            if (user.length === 0) {
                throw new common_1.NotFoundException('用户不存在');
            }
            if (assignUserRoleDto.roleIds.length > 0) {
                const roleIds = assignUserRoleDto.roleIds.join(',');
                const roles = await this.dataSource.query(`SELECT ROLE_ID FROM LED_ROLE WHERE ROLE_ID IN (${roleIds}) AND IS_ACTIVE = 1`);
                if (roles.length !== assignUserRoleDto.roleIds.length) {
                    throw new common_1.BadRequestException('部分角色ID不存在或已禁用');
                }
            }
            if (assignUserRoleDto.deptId) {
                const dept = await this.dataSource.query(`SELECT DEPT_ID FROM LED_DEPARTMENT WHERE DEPT_ID = :1 AND IS_ACTIVE = 1`, [assignUserRoleDto.deptId]);
                if (dept.length === 0) {
                    throw new common_1.BadRequestException('指定的事业部不存在或已禁用');
                }
            }
            await this.dataSource.query(`DELETE FROM LED_USER_ROLE WHERE USER_ID = :1`, [userId]);
            if (assignUserRoleDto.roleIds.length > 0) {
                const insertValues = assignUserRoleDto.roleIds.map(roleId => `(${userId}, ${roleId}, ${assignUserRoleDto.deptId || 'NULL'}, 1, 'system', SYSDATE)`).join(',');
                const insertQuery = `
          INSERT INTO LED_USER_ROLE(USER_ID, ROLE_ID, DEPT_ID, IS_ACTIVE, CREATED_BY, CREATION_DATE)
          VALUES ${insertValues}
        `;
                await this.dataSource.query(insertQuery);
            }
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            console.error(`为用户${userId}分配角色失败:`, error);
            throw new common_1.InternalServerErrorException('分配用户角色失败');
        }
    }
    async getUserRoles(userId) {
        try {
            const user = await this.dataSource.query(`SELECT ID FROM LED_USERS WHERE ID = :1`, [userId]);
            if (user.length === 0) {
                throw new common_1.NotFoundException('用户不存在');
            }
            const query = `
        SELECT r.*, ur.USER_ROLE_ID, ur.DEPT_ID as USER_ROLE_DEPT_ID,
               d.DEPT_NAME as USER_ROLE_DEPT_NAME
        FROM LED_ROLE r
        JOIN LED_USER_ROLE ur ON r.ROLE_ID = ur.ROLE_ID
        LEFT JOIN LED_DEPARTMENT d ON ur.DEPT_ID = d.DEPT_ID
        WHERE ur.USER_ID = :1 AND ur.IS_ACTIVE = 1 AND r.IS_ACTIVE = 1
        ORDER BY r.ROLE_TYPE, r.ROLE_CODE
      `;
            return await this.dataSource.query(query, [userId]);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取用户${userId}的角色列表失败:`, error);
            throw new common_1.InternalServerErrorException('获取用户角色列表失败');
        }
    }
    async assignDevicePermissions(userId, assignDevicePermissionDto) {
        try {
            const user = await this.dataSource.query(`SELECT ID FROM LED_USERS WHERE ID = :1`, [userId]);
            if (user.length === 0) {
                throw new common_1.NotFoundException('用户不存在');
            }
            if (assignDevicePermissionDto.permissions.length > 0) {
                const ledIds = assignDevicePermissionDto.permissions.map(p => `'${p.ledId}'`).join(',');
                const devices = await this.dataSource.query(`SELECT LED_ID FROM LED_DEVICE_INFO WHERE LED_ID IN (${ledIds})`);
                const existingLedIds = devices.map((d) => d.LED_ID);
                const requestedLedIds = assignDevicePermissionDto.permissions.map(p => p.ledId);
                const missingLedIds = requestedLedIds.filter(id => !existingLedIds.includes(id));
                if (missingLedIds.length > 0) {
                    throw new common_1.BadRequestException(`以下LED设备不存在: ${missingLedIds.join(', ')}`);
                }
            }
            await this.dataSource.query(`DELETE FROM LED_USER_DEVICE_PERMISSION WHERE USER_ID = :1`, [userId]);
            if (assignDevicePermissionDto.permissions.length > 0) {
                const insertValues = assignDevicePermissionDto.permissions.map(permission => `(${userId}, '${permission.ledId}', '${permission.permissionType}', 1, 'system', SYSDATE)`).join(',');
                const insertQuery = `
          INSERT INTO LED_USER_DEVICE_PERMISSION(USER_ID, LED_ID, PERMISSION_TYPE, IS_ACTIVE, CREATED_BY, CREATION_DATE)
          VALUES ${insertValues}
        `;
                await this.dataSource.query(insertQuery);
            }
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            console.error(`为用户${userId}分配设备权限失败:`, error);
            throw new common_1.InternalServerErrorException('分配设备权限失败');
        }
    }
    async getUserDevicePermissions(userId) {
        try {
            const user = await this.dataSource.query(`SELECT ID FROM LED_USERS WHERE ID = :1`, [userId]);
            if (user.length === 0) {
                throw new common_1.NotFoundException('用户不存在');
            }
            const query = `
        SELECT udp.*, d.LED_NAME, d.GONGXU, d.CHANXIAN,
               dept.DEPT_NAME
        FROM LED_USER_DEVICE_PERMISSION udp
        JOIN LED_DEVICE_INFO d ON udp.LED_ID = d.LED_ID
        LEFT JOIN LED_DEPARTMENT dept ON d.DEPT_ID = dept.DEPT_ID
        WHERE udp.USER_ID = :1 AND udp.IS_ACTIVE = 1
        ORDER BY udp.PERMISSION_TYPE, d.LED_ID
      `;
            return await this.dataSource.query(query, [userId]);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取用户${userId}的设备权限失败:`, error);
            throw new common_1.InternalServerErrorException('获取用户设备权限失败');
        }
    }
    async getUserAccessibleDevices(userId, permissionType) {
        try {
            let whereClause = 'WHERE udp.USER_ID = :1 AND udp.IS_ACTIVE = 1';
            const queryParams = [userId];
            if (permissionType) {
                whereClause += ' AND udp.PERMISSION_TYPE = :2';
                queryParams.push(permissionType);
            }
            const query = `
        SELECT DISTINCT d.*, dept.DEPT_NAME,
               udp.PERMISSION_TYPE, udp.USER_LED_ID
        FROM LED_DEVICE_INFO d
        JOIN LED_USER_DEVICE_PERMISSION udp ON d.LED_ID = udp.LED_ID
        LEFT JOIN LED_DEPARTMENT dept ON d.DEPT_ID = dept.DEPT_ID
        ${whereClause}
        ORDER BY d.LED_ID
      `;
            return await this.dataSource.query(query, queryParams);
        }
        catch (error) {
            console.error(`获取用户${userId}可访问的设备列表失败:`, error);
            throw new common_1.InternalServerErrorException('获取用户可访问设备列表失败');
        }
    }
    async checkUserDevicePermission(userId, ledId, permissionType) {
        try {
            const result = await this.dataSource.query(`SELECT COUNT(*) AS count
         FROM LED_USER_DEVICE_PERMISSION
         WHERE USER_ID = :1 AND LED_ID = :2 AND PERMISSION_TYPE = :3 AND IS_ACTIVE = 1`, [userId, ledId, permissionType]);
            return parseInt(result[0].COUNT) > 0;
        }
        catch (error) {
            console.error(`检查用户${userId}对设备${ledId}的${permissionType}权限失败:`, error);
            return false;
        }
    }
    async getUserPermissionSummary(userId) {
        try {
            const user = await this.dataSource.query(`SELECT ID, USERNAME, EMAIL, DEPT_ID FROM LED_USERS WHERE ID = :1`, [userId]);
            if (user.length === 0) {
                throw new common_1.NotFoundException('用户不存在');
            }
            const userInfo = user[0];
            const roles = await this.getUserRoles(userId);
            const menus = await this.dataSource.query(`SELECT DISTINCT m.*
         FROM LED_MENU m
         JOIN LED_ROLE_MENU rm ON m.MENU_ID = rm.MENU_ID
         JOIN LED_USER_ROLE ur ON rm.ROLE_ID = ur.ROLE_ID
         WHERE ur.USER_ID = :1 AND m.IS_ACTIVE = 1 AND rm.IS_ACTIVE = 1 AND ur.IS_ACTIVE = 1
         ORDER BY m.SORT_ORDER, m.MENU_CODE`, [userId]);
            const devicePermissions = await this.getUserDevicePermissions(userId);
            let department = null;
            if (userInfo.DEPT_ID) {
                const deptResult = await this.dataSource.query(`SELECT * FROM LED_DEPARTMENT WHERE DEPT_ID = :1`, [userInfo.DEPT_ID]);
                department = deptResult[0] || null;
            }
            return {
                user: userInfo,
                department,
                roles,
                menus,
                devicePermissions,
                summary: {
                    roleCount: roles.length,
                    menuCount: menus.length,
                    deviceCount: devicePermissions.length,
                    permissionTypes: [...new Set(devicePermissions.map((p) => p.PERMISSION_TYPE))]
                }
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error(`获取用户${userId}的权限摘要失败:`, error);
            throw new common_1.InternalServerErrorException('获取用户权限摘要失败');
        }
    }
    async getPermissionTypes() {
        return [
            { value: 'VIEW', label: '查看权限', description: '只能查看设备状态和数据' },
            { value: 'CONTROL', label: '控制权限', description: '可以向设备发送数据和控制指令' },
            { value: 'MANAGE', label: '管理权限', description: '可以管理设备配置和所有操作' }
        ];
    }
};
exports.UserPermissionService = UserPermissionService;
exports.UserPermissionService = UserPermissionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], UserPermissionService);
//# sourceMappingURL=user-permission.service.js.map