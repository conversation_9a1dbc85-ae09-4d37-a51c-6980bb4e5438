import { DataSource } from 'typeorm';
import { CreateDeviceDto } from './dto/create-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';
import { LedDevice } from './entities/led-device.entity';
export declare class LedDeviceService {
    private dataSource;
    constructor(dataSource: DataSource);
    create(createDeviceDto: CreateDeviceDto): Promise<any>;
    findAll(params?: any): Promise<{
        items: LedDevice[];
        meta: any;
    }>;
    findOne(id: number): Promise<LedDevice>;
    update(id: number, updateDeviceDto: UpdateDeviceDto): Promise<LedDevice>;
    remove(id: number): Promise<void>;
}
