import { Repository, DataSource } from 'typeorm';
import { LedConcentrator } from '../system_config/entities/led-concentrator.entity';
import { ConcentratorLedMapping } from '../system_config/entities/concentrator-led-mapping.entity';
import { LedData } from '../led_data/entities/led-data.entity';
import { ModbusClientService } from '../system_config/services/modbus-client.service';
import { LedMappingService } from '../system_config/services/led-mapping.service';
export declare class ManualDispatchService {
    private ledConcentratorRepository;
    private concentratorLedMappingRepository;
    private ledDataRepository;
    private dataSource;
    private modbusClientService;
    private ledMappingService;
    private readonly logger;
    constructor(ledConcentratorRepository: Repository<LedConcentrator>, concentratorLedMappingRepository: Repository<ConcentratorLedMapping>, ledDataRepository: Repository<LedData>, dataSource: DataSource, modbusClientService: ModbusClientService, ledMappingService: LedMappingService);
    getAllActiveConcentrators(): Promise<any[]>;
    getLedMapping(ledId: string): Promise<{
        id: any;
        concentratorId: any;
        ledId: any;
        channelNumber: any;
        description: any;
        isActive: boolean;
        concentrator: {
            id: any;
            name: any;
            code: any;
            ipAddress: any;
            portNumber: any;
            protocolType: any;
            timeoutSeconds: any;
            maxRetryCount: any;
        };
        ledName: any;
    }>;
    sendDataToLed(ledId: string, planValue: number, workType?: string): Promise<{
        success: boolean;
        message: string;
        details: {
            ledId: string;
            concentratorId: any;
            concentratorName: any;
            channelNumber: any;
            planValue: number;
            workType: string;
            workTypeText: string;
            timestamp: Date;
            planRecordId: any;
        };
    }>;
    readDataFromLed(ledId: string): Promise<{
        success: boolean;
        message: string;
        data: {
            ledId: string;
            planValue: number;
            actualValue: number;
            timestamp: Date;
        };
    }>;
    sendBatchDataToLeds(batchData: Array<{
        ledId: string;
        planValue: number;
        workType?: string;
    }>): Promise<{
        total: number;
        success: number;
        failed: number;
        results: any[];
        errors: any[];
    }>;
    private sendDataToLedDirectWrite;
    debugBatchSend(batchData: Array<{
        ledId: string;
        planValue: number;
        workType?: string;
    }>): Promise<{
        total: number;
        success: number;
        failed: number;
        results: any[];
        errors: any[];
    }>;
    private sendDataToLedSingle;
    private sendDataToLedBatchDirect;
    testAllRegisters(): Promise<{
        success: boolean;
        rawRegisters: number[];
        ledDevices: any[];
        message: string;
    }>;
    private sendDataToLedBatch;
    getAllLedDataByConcentrator(concentratorId: number): Promise<{
        concentratorId: number;
        ledDevices: any[];
        message: string;
        concentratorName?: undefined;
        summary?: undefined;
    } | {
        concentratorId: number;
        concentratorName: any;
        ledDevices: any[];
        summary: {
            total: number;
            success: number;
            failed: number;
        };
        message?: undefined;
    }>;
    private savePlanToDatabase;
    private saveDataToDatabase;
    getLedDataHistory(ledId: string, page?: number, limit?: number): Promise<{
        items: any;
        pagination: {
            page: number;
            limit: number;
            totalItems: number;
            totalPages: number;
        };
    }>;
    getRecentDispatchData(page?: number, limit?: number): Promise<{
        items: any;
        pagination: {
            page: number;
            limit: number;
            totalItems: number;
            totalPages: number;
        };
    }>;
    getModbusStatus(): Promise<{
        mode: string;
        description: string;
        isConnected: boolean;
        environment: string;
    }>;
    testModbusConnection(ip: string, port: number): Promise<{
        success: boolean;
        message: string;
        duration: number;
        testData?: undefined;
        mode?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        duration: number;
        testData: number[];
        mode: string;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        duration: number;
        error: any;
        testData?: undefined;
        mode?: undefined;
    }>;
    testPlanTable(): Promise<{
        tableExists: boolean;
        totalRecords: any;
        structure: any;
        sampleData: any;
        error?: undefined;
    } | {
        tableExists: boolean;
        error: any;
        totalRecords?: undefined;
        structure?: undefined;
        sampleData?: undefined;
    }>;
    testInsertPlan(ledId: string, planValue: number, workType: string): Promise<{
        success: boolean;
        message: string;
        result: {
            id: any;
            ledId: any;
            planValue: any;
            planDate: any;
            planType: any;
            workType: any;
            pushStatus: any;
        };
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        error: any;
        result?: undefined;
    }>;
}
