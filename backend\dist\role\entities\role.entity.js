"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Role = void 0;
const typeorm_1 = require("typeorm");
const department_entity_1 = require("../../department/entities/department.entity");
let Role = class Role {
};
exports.Role = Role;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ROLE_ID' }),
    __metadata("design:type", Number)
], Role.prototype, "ROLE_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ROLE_CODE', unique: true, nullable: false }),
    __metadata("design:type", String)
], Role.prototype, "ROLE_CODE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ROLE_NAME', nullable: false }),
    __metadata("design:type", String)
], Role.prototype, "ROLE_NAME", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ROLE_TYPE', default: 'CUSTOM' }),
    __metadata("design:type", String)
], Role.prototype, "ROLE_TYPE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DEPT_ID', nullable: true }),
    __metadata("design:type", Number)
], Role.prototype, "DEPT_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRIPTION', nullable: true }),
    __metadata("design:type", String)
], Role.prototype, "DESCRIPTION", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', default: 1 }),
    __metadata("design:type", Number)
], Role.prototype, "IS_ACTIVE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', nullable: true }),
    __metadata("design:type", String)
], Role.prototype, "CREATED_BY", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATION_DATE', type: 'date', default: () => 'SYSDATE' }),
    __metadata("design:type", Date)
], Role.prototype, "CREATION_DATE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', nullable: true }),
    __metadata("design:type", String)
], Role.prototype, "LAST_UPDATED_BY", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATE_DATE', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Role.prototype, "LAST_UPDATE_DATE", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => department_entity_1.Department),
    (0, typeorm_1.JoinColumn)({ name: 'DEPT_ID' }),
    __metadata("design:type", department_entity_1.Department)
], Role.prototype, "department", void 0);
exports.Role = Role = __decorate([
    (0, typeorm_1.Entity)('LED_ROLE')
], Role);
//# sourceMappingURL=role.entity.js.map