"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const process_operation_entity_1 = require("./entities/process-operation.entity");
const process_route_entity_1 = require("./entities/process-route.entity");
const route_operation_entity_1 = require("./entities/route-operation.entity");
const production_line_entity_1 = require("./entities/production-line.entity");
const process_operation_service_1 = require("./services/process-operation.service");
const production_line_service_1 = require("./services/production-line.service");
const process_route_service_1 = require("./services/process-route.service");
const process_operation_controller_1 = require("./controllers/process-operation.controller");
const production_line_controller_1 = require("./controllers/production-line.controller");
const process_route_controller_1 = require("./controllers/process-route.controller");
let ProcessModule = class ProcessModule {
};
exports.ProcessModule = ProcessModule;
exports.ProcessModule = ProcessModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                process_operation_entity_1.ProcessOperation,
                process_route_entity_1.ProcessRoute,
                route_operation_entity_1.RouteOperation,
                production_line_entity_1.ProductionLine
            ]),
        ],
        controllers: [
            process_operation_controller_1.ProcessOperationController,
            production_line_controller_1.ProductionLineController,
            process_route_controller_1.ProcessRouteController
        ],
        providers: [
            process_operation_service_1.ProcessOperationService,
            production_line_service_1.ProductionLineService,
            process_route_service_1.ProcessRouteService
        ],
        exports: [
            process_operation_service_1.ProcessOperationService,
            production_line_service_1.ProductionLineService,
            process_route_service_1.ProcessRouteService
        ]
    })
], ProcessModule);
//# sourceMappingURL=process.module.js.map