import { UserPermissionService } from './user-permission.service';
import { AssignUserRoleDto } from './dto/assign-user-role.dto';
import { AssignDevicePermissionDto } from './dto/assign-device-permission.dto';
export declare class UserPermissionController {
    private readonly userPermissionService;
    constructor(userPermissionService: UserPermissionService);
    assignUserRoles(userId: number, assignUserRoleDto: AssignUserRoleDto): Promise<{
        success: boolean;
        message: any;
        data: any;
    }>;
    getUserRoles(userId: number): Promise<{
        success: boolean;
        message: string;
        data: any[];
    } | {
        success: boolean;
        message: any;
        data: any[];
    }>;
    assignDevicePermissions(userId: number, assignDevicePermissionDto: AssignDevicePermissionDto): Promise<{
        success: boolean;
        message: any;
        data: any;
    }>;
    getUserDevicePermissions(userId: number): Promise<{
        success: boolean;
        message: string;
        data: any[];
    } | {
        success: boolean;
        message: any;
        data: any[];
    }>;
    getUserAccessibleDevices(userId: number, permissionType?: string): Promise<{
        success: boolean;
        message: string;
        data: any[];
    } | {
        success: boolean;
        message: any;
        data: any[];
    }>;
    getUserPermissionSummary(userId: number): Promise<{
        success: boolean;
        message: string;
        data: any;
    } | {
        success: boolean;
        message: any;
        data: any;
    }>;
    checkUserDevicePermission(userId: number, ledId: string, permissionType: string): Promise<{
        success: boolean;
        message: any;
        data: {
            userId: number;
            ledId: string;
            permissionType: string;
            hasPermission: boolean;
        };
    }>;
    getPermissionTypes(): Promise<{
        success: boolean;
        message: string;
        data: any[];
    } | {
        success: boolean;
        message: any;
        data: any[];
    }>;
}
