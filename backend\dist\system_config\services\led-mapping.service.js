"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LedMappingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedMappingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const concentrator_led_mapping_entity_1 = require("../entities/concentrator-led-mapping.entity");
const led_concentrator_entity_1 = require("../entities/led-concentrator.entity");
let LedMappingService = LedMappingService_1 = class LedMappingService {
    constructor(mappingRepository, concentratorRepository, dataSource) {
        this.mappingRepository = mappingRepository;
        this.concentratorRepository = concentratorRepository;
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(LedMappingService_1.name);
    }
    async create(createLedMappingDto) {
        try {
            const concentratorExists = await this.checkConcentratorExists(createLedMappingDto.concentratorId);
            if (!concentratorExists) {
                throw new common_1.NotFoundException(`集中器ID ${createLedMappingDto.concentratorId} 不存在`);
            }
            const existingQuery = `
        SELECT MAPPING_ID, IS_ACTIVE
        FROM CONCENTRATOR_LED_MAPPING
        WHERE LED_ID = :1 AND CONCENTRATOR_ID = :2
      `;
            const existingResults = await this.dataSource.query(existingQuery, [
                createLedMappingDto.ledId,
                createLedMappingDto.concentratorId
            ]);
            if (existingResults && existingResults.length > 0) {
                const existingMapping = existingResults[0];
                if (existingMapping.IS_ACTIVE === '1') {
                    throw new Error(`LED设备 "${createLedMappingDto.ledId}" 已经映射到集中器 "${createLedMappingDto.concentratorId}"，不能重复创建`);
                }
                else {
                    const mappingId = existingMapping.MAPPING_ID;
                    this.logger.log(`重新激活LED设备 ${createLedMappingDto.ledId} 的映射(ID: ${mappingId})`);
                    const updateQuery = `
            UPDATE CONCENTRATOR_LED_MAPPING SET
              CHANNEL_NUMBER = :1,
              DESCRIPTION = :2,
              IS_ACTIVE = '1',
              LAST_UPDATED_BY = 'SYSTEM',
              LAST_UPDATE_DATE = SYSDATE
            WHERE MAPPING_ID = :3
          `;
                    await this.dataSource.query(updateQuery, [
                        createLedMappingDto.channelNumber,
                        createLedMappingDto.description || null,
                        mappingId
                    ]);
                    const updatedMapping = await this.findOne(mappingId);
                    return {
                        ...updatedMapping,
                        message: '映射重新激活成功'
                    };
                }
            }
            const ledMappingQuery = `
        SELECT CONCENTRATOR_ID
        FROM CONCENTRATOR_LED_MAPPING
        WHERE LED_ID = :1 AND IS_ACTIVE = '1'
      `;
            const ledMappingResults = await this.dataSource.query(ledMappingQuery, [createLedMappingDto.ledId]);
            if (ledMappingResults && ledMappingResults.length > 0) {
                throw new Error(`LED设备 "${createLedMappingDto.ledId}" 已经映射到其他集中器，一个LED设备只能映射到一个集中器`);
            }
            const maxIdResult = await this.dataSource.query(`SELECT NVL(MAX(MAPPING_ID), 0) + 1 AS NEXT_ID FROM CONCENTRATOR_LED_MAPPING`);
            const nextId = maxIdResult[0].NEXT_ID;
            this.logger.log(`生成新映射ID: ${nextId}`);
            await this.dataSource.query(`INSERT INTO CONCENTRATOR_LED_MAPPING(
          MAPPING_ID, 
          CONCENTRATOR_ID, 
          LED_ID, 
          CHANNEL_NUMBER, 
          DESCRIPTION, 
          IS_ACTIVE, 
          CREATED_BY, 
          CREATION_DATE
        ) VALUES(
          :1, :2, :3, :4, :5, :6, 'SYSTEM', SYSDATE
        )`, [
                nextId,
                createLedMappingDto.concentratorId,
                createLedMappingDto.ledId,
                createLedMappingDto.channelNumber,
                createLedMappingDto.description || null,
                createLedMappingDto.isActive ? '1' : '0'
            ]);
            const newMapping = await this.findOne(nextId);
            return {
                ...newMapping,
                message: '成功创建新映射'
            };
        }
        catch (error) {
            this.logger.error(`创建LED映射失败: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`创建LED映射失败: ${error.message}`);
        }
    }
    async findAll(queryParams) {
        try {
            const page = queryParams.page || 1;
            const limit = queryParams.limit || 10;
            const offset = (page - 1) * limit;
            const ledId = queryParams.ledId;
            const concentratorId = queryParams.concentratorId;
            let whereClause = "WHERE m.IS_ACTIVE = '1'";
            const params = [];
            let paramIndex = 1;
            if (ledId) {
                whereClause += ` AND m.LED_ID = :${paramIndex}`;
                params.push(ledId);
                paramIndex++;
            }
            if (concentratorId) {
                whereClause += ` AND m.CONCENTRATOR_ID = :${paramIndex}`;
                params.push(parseInt(concentratorId));
                paramIndex++;
            }
            const countQuery = `
        SELECT COUNT(*) AS total 
        FROM CONCENTRATOR_LED_MAPPING m 
        ${whereClause}
      `;
            const countResult = await this.dataSource.query(countQuery, params);
            const totalItems = parseInt(countResult[0].TOTAL);
            const sqlQuery = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT
              m.MAPPING_ID as "id",
              m.LED_ID as "ledId",
              m.CONCENTRATOR_ID as "concentratorId",
              m.CHANNEL_NUMBER as "channelNumber",
              m.DESCRIPTION as "description",
              m.IS_ACTIVE as "isActive",
              c.CONCENTRATOR_NAME as "concentratorName",
              c.CONCENTRATOR_CODE as "concentratorCode",
              d.LED_NAME as "ledName"
            FROM 
              CONCENTRATOR_LED_MAPPING m
            LEFT JOIN 
              LED_CONCENTRATOR c ON c.CONCENTRATOR_ID = m.CONCENTRATOR_ID
            LEFT JOIN
              LED_DEVICE_INFO d ON d.LED_ID = m.LED_ID
            ${whereClause}
            ORDER BY m.MAPPING_ID DESC
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const items = await this.dataSource.query(sqlQuery, params);
            return {
                items,
                meta: {
                    total: totalItems,
                    page,
                    limit
                }
            };
        }
        catch (error) {
            this.logger.error(`查询LED映射失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`查询LED映射失败: ${error.message}`);
        }
    }
    async findOne(id) {
        try {
            const query = `
        SELECT
          m.MAPPING_ID as "id",
          m.LED_ID as "ledId",
          m.CONCENTRATOR_ID as "concentratorId",
          m.CHANNEL_NUMBER as "channelNumber",
          m.DESCRIPTION as "description",
          m.IS_ACTIVE as "isActive",
          c.CONCENTRATOR_NAME as "concentratorName",
          c.CONCENTRATOR_CODE as "concentratorCode",
          c.IP_ADDRESS as "ipAddress",
          c.PORT_NUMBER as "portNumber",
          c.PROTOCOL_TYPE as "protocolType",
          c.TIMEOUT_SECONDS as "timeoutSeconds",
          c.MAX_RETRY_COUNT as "maxRetryCount",
          d.LED_NAME as "ledName"
        FROM 
          CONCENTRATOR_LED_MAPPING m
        LEFT JOIN 
          LED_CONCENTRATOR c ON c.CONCENTRATOR_ID = m.CONCENTRATOR_ID
        LEFT JOIN
          LED_DEVICE_INFO d ON d.LED_ID = m.LED_ID
        WHERE 
          m.MAPPING_ID = :1
      `;
            const mappings = await this.dataSource.query(query, [id]);
            if (!mappings || mappings.length === 0) {
                throw new common_1.NotFoundException(`未找到ID为 ${id} 的LED映射`);
            }
            return mappings[0];
        }
        catch (error) {
            this.logger.error(`查询LED映射 ${id} 失败: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`查询LED映射 ${id} 失败: ${error.message}`);
        }
    }
    async findByLedId(ledId) {
        try {
            const mapping = await this.findMappingByLedId(ledId);
            if (!mapping) {
                throw new common_1.NotFoundException(`未找到LED设备 ${ledId} 的映射配置`);
            }
            return mapping;
        }
        catch (error) {
            this.logger.error(`查询LED设备 ${ledId} 的映射失败: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`查询LED设备 ${ledId} 的映射失败: ${error.message}`);
        }
    }
    async findMappingByLedId(ledId) {
        try {
            const query = `
        SELECT
          m.MAPPING_ID as "id",
          m.LED_ID as "ledId",
          m.CONCENTRATOR_ID as "concentratorId",
          m.CHANNEL_NUMBER as "channelNumber",
          m.DESCRIPTION as "description",
          m.IS_ACTIVE as "isActive",
          c.CONCENTRATOR_NAME as "concentratorName",
          c.CONCENTRATOR_CODE as "concentratorCode",
          c.IP_ADDRESS as "ipAddress",
          c.PORT_NUMBER as "portNumber",
          c.PROTOCOL_TYPE as "protocolType",
          c.TIMEOUT_SECONDS as "timeoutSeconds",
          c.MAX_RETRY_COUNT as "maxRetryCount",
          d.LED_NAME as "ledName"
        FROM 
          CONCENTRATOR_LED_MAPPING m
        LEFT JOIN 
          LED_CONCENTRATOR c ON c.CONCENTRATOR_ID = m.CONCENTRATOR_ID
        LEFT JOIN
          LED_DEVICE_INFO d ON d.LED_ID = m.LED_ID
        WHERE 
          m.LED_ID = :1
      `;
            const mappings = await this.dataSource.query(query, [ledId]);
            if (!mappings || mappings.length === 0) {
                return null;
            }
            const mapping = mappings[0];
            return {
                id: mapping.id,
                concentratorId: mapping.concentratorId,
                ledId: mapping.ledId,
                channelNumber: mapping.channelNumber,
                description: mapping.description,
                isActive: mapping.isActive === '1',
                concentrator: {
                    id: mapping.concentratorId,
                    name: mapping.concentratorName,
                    code: mapping.concentratorCode,
                    ipAddress: mapping.ipAddress,
                    portNumber: mapping.portNumber,
                    protocolType: mapping.protocolType,
                    timeoutSeconds: mapping.timeoutSeconds || 10,
                    maxRetryCount: mapping.maxRetryCount || 3
                },
                ledName: mapping.ledName
            };
        }
        catch (error) {
            this.logger.error(`查询LED设备 ${ledId} 的映射失败: ${error.message}`, error.stack);
            return null;
        }
    }
    async update(id, updateLedMappingDto) {
        try {
            const existingMapping = await this.findOne(id);
            if (!existingMapping) {
                throw new common_1.NotFoundException(`未找到ID为 ${id} 的LED映射`);
            }
            const newConcentratorId = updateLedMappingDto.concentratorId !== undefined
                ? updateLedMappingDto.concentratorId
                : existingMapping.concentratorId;
            const newLedId = updateLedMappingDto.ledId !== undefined
                ? updateLedMappingDto.ledId
                : existingMapping.ledId;
            if (newConcentratorId !== existingMapping.concentratorId || newLedId !== existingMapping.ledId) {
                const conflictQuery = `
          SELECT COUNT(*) AS count
          FROM CONCENTRATOR_LED_MAPPING
          WHERE CONCENTRATOR_ID = :1 AND LED_ID = :2 AND MAPPING_ID != :3
        `;
                const conflictResult = await this.dataSource.query(conflictQuery, [
                    newConcentratorId,
                    newLedId,
                    id
                ]);
                if (parseInt(conflictResult[0].COUNT) > 0) {
                    throw new Error(`集中器 "${newConcentratorId}" 和 LED设备 "${newLedId}" 的组合已存在，不能重复映射`);
                }
            }
            if (updateLedMappingDto.concentratorId && updateLedMappingDto.concentratorId !== existingMapping.concentratorId) {
                const concentratorExists = await this.checkConcentratorExists(updateLedMappingDto.concentratorId);
                if (!concentratorExists) {
                    throw new common_1.NotFoundException(`集中器ID ${updateLedMappingDto.concentratorId} 不存在`);
                }
            }
            let updateQuery = 'UPDATE CONCENTRATOR_LED_MAPPING SET ';
            const updateValues = [];
            let paramIndex = 1;
            if (updateLedMappingDto.concentratorId !== undefined) {
                updateQuery += `CONCENTRATOR_ID = :${paramIndex}, `;
                updateValues.push(updateLedMappingDto.concentratorId);
                paramIndex++;
            }
            if (updateLedMappingDto.ledId !== undefined) {
                updateQuery += `LED_ID = :${paramIndex}, `;
                updateValues.push(updateLedMappingDto.ledId);
                paramIndex++;
            }
            if (updateLedMappingDto.channelNumber !== undefined) {
                updateQuery += `CHANNEL_NUMBER = :${paramIndex}, `;
                updateValues.push(updateLedMappingDto.channelNumber);
                paramIndex++;
            }
            if (updateLedMappingDto.description !== undefined) {
                updateQuery += `DESCRIPTION = :${paramIndex}, `;
                updateValues.push(updateLedMappingDto.description);
                paramIndex++;
            }
            if (updateLedMappingDto.isActive !== undefined) {
                updateQuery += `IS_ACTIVE = :${paramIndex}, `;
                updateValues.push(updateLedMappingDto.isActive ? '1' : '0');
                paramIndex++;
            }
            updateQuery += `LAST_UPDATED_BY = 'SYSTEM', LAST_UPDATE_DATE = SYSDATE `;
            updateQuery += `WHERE MAPPING_ID = :${paramIndex}`;
            updateValues.push(id);
            await this.dataSource.query(updateQuery, updateValues);
            const updatedMapping = await this.findOne(id);
            return {
                ...updatedMapping,
                message: '映射更新成功'
            };
        }
        catch (error) {
            this.logger.error(`更新LED映射 ${id} 失败: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`更新LED映射 ${id} 失败: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            this.logger.log(`开始删除LED映射，ID: ${id}`);
            if (!id || isNaN(id) || id <= 0) {
                throw new Error(`无效的映射ID: ${id}`);
            }
            const mapping = await this.findOne(id);
            if (!mapping) {
                throw new common_1.NotFoundException(`未找到ID为 ${id} 的LED映射`);
            }
            this.logger.log(`找到映射记录: LED=${mapping.ledId}, 集中器=${mapping.concentratorId}`);
            const result = await this.dataSource.query(`UPDATE CONCENTRATOR_LED_MAPPING
         SET IS_ACTIVE = '0', LAST_UPDATED_BY = 'SYSTEM', LAST_UPDATE_DATE = SYSDATE
         WHERE MAPPING_ID = :1`, [id]);
            this.logger.log(`映射删除成功，ID: ${id}`);
            return {
                id,
                message: '映射已成功删除'
            };
        }
        catch (error) {
            this.logger.error(`删除LED映射 ${id} 失败: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`删除LED映射 ${id} 失败: ${error.message}`);
        }
    }
    async checkConcentratorExists(id) {
        try {
            const query = `SELECT COUNT(*) AS count FROM LED_CONCENTRATOR WHERE CONCENTRATOR_ID = :1`;
            const result = await this.dataSource.query(query, [id]);
            return parseInt(result[0].COUNT) > 0;
        }
        catch (error) {
            this.logger.error(`检查集中器 ${id} 是否存在失败: ${error.message}`, error.stack);
            return false;
        }
    }
};
exports.LedMappingService = LedMappingService;
exports.LedMappingService = LedMappingService = LedMappingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(concentrator_led_mapping_entity_1.ConcentratorLedMapping)),
    __param(1, (0, typeorm_1.InjectRepository)(led_concentrator_entity_1.LedConcentrator)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], LedMappingService);
//# sourceMappingURL=led-mapping.service.js.map