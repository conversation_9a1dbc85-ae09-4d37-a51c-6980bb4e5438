import apiClient from '../../../services/apiClient';

const BASE_URL = '/departments';

// 获取所有部门（分页）
export const getAllDepartments = async (params = {}) => {
  try {
    console.log('API请求参数:', params);
    const response = await apiClient.get(BASE_URL, { params });
    console.log('API响应数据:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取部门列表失败:', error);
    throw error;
  }
};

// 获取部门树形结构
export const getDepartmentTree = async () => {
  try {
    const response = await apiClient.get(`${BASE_URL}/tree`);
    return response.data;
  } catch (error) {
    console.error('获取部门树形结构失败:', error);
    throw error;
  }
};

// 获取单个部门详情
export const getDepartmentById = async (id) => {
  try {
    const response = await apiClient.get(`${BASE_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`获取部门${id}详情失败:`, error);
    throw error;
  }
};

// 创建部门
export const createDepartment = async (departmentData) => {
  try {
    const response = await apiClient.post(BASE_URL, departmentData);
    return response.data;
  } catch (error) {
    console.error('创建部门失败:', error);
    throw error;
  }
};

// 更新部门
export const updateDepartment = async (id, departmentData) => {
  try {
    const response = await apiClient.patch(`${BASE_URL}/${id}`, departmentData);
    return response.data;
  } catch (error) {
    console.error(`更新部门${id}失败:`, error);
    throw error;
  }
};

// 删除部门
export const deleteDepartment = async (id) => {
  try {
    const response = await apiClient.delete(`${BASE_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`删除部门${id}失败:`, error);
    throw error;
  }
};

// 获取部门选项（用于下拉框）
export const getDepartmentOptions = async () => {
  try {
    const response = await apiClient.get(`${BASE_URL}`, { 
      params: { limit: 1000 } // 获取所有部门用于下拉选择
    });
    
    if (response.data && response.data.data) {
      return response.data.data.map(dept => ({
        value: dept.DEPT_ID,
        label: dept.DEPT_NAME,
        code: dept.DEPT_CODE,
        level: dept.DEPT_LEVEL,
        parentId: dept.PARENT_DEPT_ID
      }));
    }
    return [];
  } catch (error) {
    console.error('获取部门选项失败:', error);
    return [];
  }
};

export default {
  getAllDepartments,
  getDepartmentTree,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartmentOptions
};
