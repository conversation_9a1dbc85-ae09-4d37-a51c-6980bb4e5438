import apiClient from '../../../services/apiClient';

const BASE_URL = '/departments';

// 获取所有部门（分页）
export const getAllDepartments = async (params = {}) => {
  try {
    console.log('🚀 发送API请求 - 获取部门列表');
    console.log('📋 请求参数:', params);
    console.log('🔗 请求URL:', `${BASE_URL}?${new URLSearchParams(params).toString()}`);

    const response = await apiClient.get(BASE_URL, { params });

    console.log('📡 API响应状态:', response.status);
    console.log('📦 API响应数据:', response.data);

    // 确保返回正确的数据格式
    if (response.data && response.data.success) {
      const result = {
        data: response.data.data || [],
        meta: response.data.meta || {
          totalItems: 0,
          itemsPerPage: params.limit || 10,
          currentPage: params.page || 1,
          totalPages: 0
        }
      };
      console.log('✅ 数据处理成功:', result);
      return result;
    } else {
      console.warn('⚠️ API返回失败，使用空数据:', response.data);
      // 如果API返回失败，返回空数据
      return {
        data: [],
        meta: {
          totalItems: 0,
          itemsPerPage: params.limit || 10,
          currentPage: params.page || 1,
          totalPages: 0
        }
      };
    }
  } catch (error) {
    console.error('❌ 获取部门列表失败:', error);
    console.error('🔍 错误详情:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url
    });
    throw error;
  }
};

// 获取部门树形结构
export const getDepartmentTree = async () => {
  try {
    const response = await apiClient.get(`${BASE_URL}/tree`);
    console.log('树形数据响应:', response.data);

    // 确保返回正确的数据格式
    if (response.data && response.data.success) {
      return {
        data: response.data.data || []
      };
    } else {
      return {
        data: []
      };
    }
  } catch (error) {
    console.error('获取部门树形结构失败:', error);
    throw error;
  }
};

// 获取单个部门详情
export const getDepartmentById = async (id) => {
  try {
    const response = await apiClient.get(`${BASE_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`获取部门${id}详情失败:`, error);
    throw error;
  }
};

// 创建部门
export const createDepartment = async (departmentData) => {
  try {
    const response = await apiClient.post(BASE_URL, departmentData);
    return response.data;
  } catch (error) {
    console.error('创建部门失败:', error);
    throw error;
  }
};

// 更新部门
export const updateDepartment = async (id, departmentData) => {
  try {
    const response = await apiClient.patch(`${BASE_URL}/${id}`, departmentData);
    return response.data;
  } catch (error) {
    console.error(`更新部门${id}失败:`, error);
    throw error;
  }
};

// 删除部门
export const deleteDepartment = async (id) => {
  try {
    const response = await apiClient.delete(`${BASE_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`删除部门${id}失败:`, error);
    throw error;
  }
};

// 获取部门选项（用于下拉框）
export const getDepartmentOptions = async () => {
  try {
    const response = await apiClient.get(`${BASE_URL}`, { 
      params: { limit: 1000 } // 获取所有部门用于下拉选择
    });
    
    if (response.data && response.data.data) {
      return response.data.data.map(dept => ({
        value: dept.DEPT_ID,
        label: dept.DEPT_NAME,
        code: dept.DEPT_CODE,
        level: dept.DEPT_LEVEL,
        parentId: dept.PARENT_DEPT_ID
      }));
    }
    return [];
  } catch (error) {
    console.error('获取部门选项失败:', error);
    return [];
  }
};

export default {
  getAllDepartments,
  getDepartmentTree,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartmentOptions
};
