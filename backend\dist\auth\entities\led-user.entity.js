"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LedUser = void 0;
const typeorm_1 = require("typeorm");
const department_entity_1 = require("../../department/entities/department.entity");
let LedUser = class LedUser {
};
exports.LedUser = LedUser;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], LedUser.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar2', length: 255, unique: true, name: 'USERNAME' }),
    __metadata("design:type", String)
], LedUser.prototype, "username", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar2', length: 255, name: 'PASSWORD' }),
    __metadata("design:type", String)
], LedUser.prototype, "password", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DEPT_ID', nullable: true }),
    __metadata("design:type", Number)
], LedUser.prototype, "deptId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamp', name: 'CREATE_TIME', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], LedUser.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamp', name: 'UPDATE_TIME', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], LedUser.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => department_entity_1.Department),
    (0, typeorm_1.JoinColumn)({ name: 'DEPT_ID' }),
    __metadata("design:type", department_entity_1.Department)
], LedUser.prototype, "department", void 0);
exports.LedUser = LedUser = __decorate([
    (0, typeorm_1.Entity)('LED_USERS')
], LedUser);
//# sourceMappingURL=led-user.entity.js.map