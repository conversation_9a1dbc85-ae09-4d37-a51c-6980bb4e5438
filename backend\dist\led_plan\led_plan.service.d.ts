import { DataSource } from 'typeorm';
import { CreatePlanDto } from './dto/create-plan.dto';
import { UpdatePlanDto } from './dto/update-plan.dto';
import { LedPlan } from './entities/led-plan.entity';
export declare class LedPlanService {
    private dataSource;
    constructor(dataSource: DataSource);
    create(createPlanDto: CreatePlanDto): Promise<any>;
    findAll(params?: any): Promise<{
        items: LedPlan[];
        meta: any;
    }>;
    findOne(id: number): Promise<LedPlan>;
    update(id: number, updatePlanDto: UpdatePlanDto): Promise<LedPlan>;
    remove(id: number): Promise<void>;
    pushPlan(id: number): Promise<LedPlan>;
    getLedOptions(): Promise<any[]>;
    importPlansFromExcel(file: any): Promise<{
        success: number;
        failed: number;
        errors: string[];
    }>;
    private formatDateString;
    private validateLedId;
    private formatDate;
}
