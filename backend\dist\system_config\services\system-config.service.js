"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SystemConfigService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
let SystemConfigService = SystemConfigService_1 = class SystemConfigService {
    constructor(dataSource) {
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(SystemConfigService_1.name);
    }
    async findAll(query) {
        this.logger.log(`查询系统配置，参数: ${JSON.stringify(query)}`);
        try {
            if (query.search) {
                return await this.searchConfigs(query.search);
            }
            let whereClause = '';
            const params = [];
            let paramIndex = 1;
            if (query.type) {
                whereClause += whereClause ? ` AND ` : ` WHERE `;
                whereClause += `CONFIG_TYPE = :${paramIndex}`;
                params.push(query.type);
                paramIndex++;
            }
            if (query.group) {
                whereClause += whereClause ? ` AND ` : ` WHERE `;
                whereClause += `CONFIG_GROUP = :${paramIndex}`;
                params.push(query.group);
                paramIndex++;
            }
            if (query.key) {
                whereClause += whereClause ? ` AND ` : ` WHERE `;
                whereClause += `CONFIG_KEY LIKE :${paramIndex}`;
                params.push(`%${query.key}%`);
                paramIndex++;
            }
            if (query.isActive !== undefined) {
                whereClause += whereClause ? ` AND ` : ` WHERE `;
                whereClause += `IS_ACTIVE = :${paramIndex}`;
                params.push(query.isActive ? 1 : 0);
                paramIndex++;
                this.logger.debug(`isActive 查询条件: ${query.isActive}, 类型: ${typeof query.isActive}`);
            }
            const countQuery = `SELECT COUNT(*) AS TOTAL FROM SYSTEM_CONFIG${whereClause}`;
            const countResult = await this.dataSource.query(countQuery, params);
            const total = parseInt(countResult[0].TOTAL);
            const page = query.page ? parseInt(query.page.toString()) : 1;
            const limit = query.limit ? parseInt(query.limit.toString()) : total;
            const offset = (page - 1) * limit;
            const selectQuery = `
        SELECT * FROM (
          SELECT a.*, ROWNUM rnum FROM (
            SELECT * FROM SYSTEM_CONFIG${whereClause}
            ORDER BY SORT_ORDER ASC, CONFIG_ID ASC
          ) a WHERE ROWNUM <= ${offset + limit}
        ) WHERE rnum > ${offset}
      `;
            const rawItems = await this.dataSource.query(selectQuery, params);
            const items = rawItems.map(item => this.transformDatabaseRecord(item));
            this.logger.log(`查询结果：找到 ${items.length} 条记录，总共 ${total} 条`);
            return {
                items,
                meta: {
                    total,
                    currentPage: page,
                    totalPages: Math.ceil(total / limit),
                    itemsPerPage: limit
                }
            };
        }
        catch (error) {
            this.logger.error(`查询系统配置失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`查询系统配置失败: ${error.message}`);
        }
    }
    async searchConfigs(searchTerm) {
        this.logger.log(`执行通用搜索，搜索词: ${searchTerm}`);
        try {
            const searchPattern = `%${searchTerm}%`;
            const searchQuery = `
        SELECT * FROM SYSTEM_CONFIG
        WHERE (
          CONFIG_KEY LIKE :1 OR
          CONFIG_VALUE LIKE :2 OR
          CONFIG_TYPE LIKE :3 OR
          CONFIG_GROUP LIKE :4 OR
          DESCRIPTION LIKE :5
        )
        ORDER BY SORT_ORDER ASC, CONFIG_ID ASC
      `;
            const rawItems = await this.dataSource.query(searchQuery, [
                searchPattern, searchPattern, searchPattern, searchPattern, searchPattern
            ]);
            const items = rawItems.map(item => this.transformDatabaseRecord(item));
            const total = items.length;
            this.logger.log(`通用搜索结果：找到 ${total} 条记录`);
            return { items, meta: { total } };
        }
        catch (error) {
            this.logger.error(`执行通用搜索失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`执行通用搜索失败: ${error.message}`);
        }
    }
    async findOne(id) {
        try {
            this.logger.log(`查询ID为 ${id} 的系统配置`);
            const results = await this.dataSource.query(`SELECT * FROM SYSTEM_CONFIG WHERE CONFIG_ID = :1`, [id]);
            if (!results || results.length === 0) {
                throw new common_1.NotFoundException(`System config with ID ${id} not found`);
            }
            return this.transformDatabaseRecord(results[0]);
        }
        catch (error) {
            this.logger.error(`查询ID为 ${id} 的系统配置失败: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`查询系统配置失败: ${error.message}`);
        }
    }
    async findByKey(key) {
        try {
            this.logger.log(`查询KEY为 ${key} 的系统配置`);
            const results = await this.dataSource.query(`SELECT * FROM SYSTEM_CONFIG WHERE CONFIG_KEY = :1`, [key]);
            if (!results || results.length === 0) {
                throw new common_1.NotFoundException(`System config with key ${key} not found`);
            }
            return this.transformDatabaseRecord(results[0]);
        }
        catch (error) {
            this.logger.error(`查询KEY为 ${key} 的系统配置失败: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`查询系统配置失败: ${error.message}`);
        }
    }
    async create(createSystemConfigDto) {
        try {
            this.logger.log(`创建系统配置: ${JSON.stringify(createSystemConfigDto)}`);
            const existingConfig = await this.dataSource.query(`SELECT COUNT(*) AS count FROM SYSTEM_CONFIG WHERE CONFIG_KEY = :1`, [createSystemConfigDto.key]);
            if (parseInt(existingConfig[0].COUNT) > 0) {
                throw new Error(`配置键名 "${createSystemConfigDto.key}" 已存在，请使用不同的键名`);
            }
            await this.dataSource.query(`INSERT INTO SYSTEM_CONFIG(
          CONFIG_ID, CONFIG_KEY, CONFIG_VALUE, CONFIG_TYPE, CONFIG_GROUP,
          DESCRIPTION, IS_ENCRYPTED, IS_EDITABLE, SORT_ORDER, IS_ACTIVE,
          CREATED_BY, CREATION_DATE
        ) VALUES(
          SYSTEM_CONFIG_SEQ.NEXTVAL, :1, :2, :3, :4, :5,
          :6, :7, :8, :9, :10, SYSDATE
        )`, [
                createSystemConfigDto.key,
                createSystemConfigDto.value || null,
                createSystemConfigDto.type,
                createSystemConfigDto.group,
                createSystemConfigDto.description || null,
                createSystemConfigDto.isEncrypted !== undefined ? (createSystemConfigDto.isEncrypted ? 1 : 0) : 0,
                createSystemConfigDto.isEditable !== undefined ? (createSystemConfigDto.isEditable ? 1 : 0) : 1,
                createSystemConfigDto.sortOrder || 0,
                createSystemConfigDto.isActive !== undefined ? (createSystemConfigDto.isActive ? 1 : 0) : 1,
                'SYSTEM'
            ]);
            const newRecord = await this.dataSource.query(`SELECT CONFIG_ID FROM SYSTEM_CONFIG WHERE CONFIG_KEY = :1`, [createSystemConfigDto.key]);
            const newId = newRecord[0].CONFIG_ID;
            this.logger.log(`系统配置创建成功，ID: ${newId}`);
            return {
                success: true,
                message: '系统配置创建成功',
                data: await this.findOne(newId)
            };
        }
        catch (error) {
            this.logger.error(`创建系统配置失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`创建系统配置失败: ${error.message}`);
        }
    }
    async update(id, updateSystemConfigDto) {
        try {
            const config = await this.findOne(id);
            if (!config.isEditable) {
                throw new Error(`System config with ID ${id} is not editable`);
            }
            let updateQuery = 'UPDATE SYSTEM_CONFIG SET ';
            const updateValues = [];
            let paramIndex = 1;
            if (updateSystemConfigDto.key !== undefined) {
                updateQuery += `CONFIG_KEY = :${paramIndex}, `;
                updateValues.push(updateSystemConfigDto.key);
                paramIndex++;
            }
            if (updateSystemConfigDto.value !== undefined) {
                updateQuery += `CONFIG_VALUE = :${paramIndex}, `;
                updateValues.push(updateSystemConfigDto.value);
                paramIndex++;
            }
            if (updateSystemConfigDto.type !== undefined) {
                updateQuery += `CONFIG_TYPE = :${paramIndex}, `;
                updateValues.push(updateSystemConfigDto.type);
                paramIndex++;
            }
            if (updateSystemConfigDto.group !== undefined) {
                updateQuery += `CONFIG_GROUP = :${paramIndex}, `;
                updateValues.push(updateSystemConfigDto.group);
                paramIndex++;
            }
            if (updateSystemConfigDto.description !== undefined) {
                updateQuery += `DESCRIPTION = :${paramIndex}, `;
                updateValues.push(updateSystemConfigDto.description);
                paramIndex++;
            }
            if (updateSystemConfigDto.isEncrypted !== undefined) {
                updateQuery += `IS_ENCRYPTED = :${paramIndex}, `;
                updateValues.push(updateSystemConfigDto.isEncrypted ? 1 : 0);
                paramIndex++;
            }
            if (updateSystemConfigDto.isEditable !== undefined) {
                updateQuery += `IS_EDITABLE = :${paramIndex}, `;
                updateValues.push(updateSystemConfigDto.isEditable ? 1 : 0);
                paramIndex++;
            }
            if (updateSystemConfigDto.sortOrder !== undefined) {
                updateQuery += `SORT_ORDER = :${paramIndex}, `;
                updateValues.push(updateSystemConfigDto.sortOrder);
                paramIndex++;
            }
            if (updateSystemConfigDto.isActive !== undefined) {
                updateQuery += `IS_ACTIVE = :${paramIndex}, `;
                updateValues.push(updateSystemConfigDto.isActive ? 1 : 0);
                paramIndex++;
            }
            updateQuery += `LAST_UPDATE_DATE = SYSDATE, LAST_UPDATED_BY = :${paramIndex}, `;
            updateValues.push('SYSTEM');
            paramIndex++;
            updateQuery = updateQuery.slice(0, -2);
            updateQuery += ` WHERE CONFIG_ID = :${paramIndex}`;
            updateValues.push(id);
            await this.dataSource.query(updateQuery, updateValues);
            this.logger.log(`系统配置更新成功，ID: ${id}`);
            return {
                success: true,
                message: '系统配置更新成功',
                data: await this.findOne(id)
            };
        }
        catch (error) {
            this.logger.error(`更新ID为 ${id} 的系统配置失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`更新系统配置失败: ${error.message}`);
        }
    }
    async updateByKey(key, value) {
        try {
            const config = await this.findByKey(key);
            if (!config.isEditable) {
                throw new Error(`System config with key ${key} is not editable`);
            }
            await this.dataSource.query(`UPDATE SYSTEM_CONFIG
         SET CONFIG_VALUE = :1, LAST_UPDATE_DATE = SYSDATE, LAST_UPDATED_BY = :2
         WHERE CONFIG_KEY = :3`, [value, 'SYSTEM', key]);
            this.logger.log(`系统配置更新成功，KEY: ${key}`);
            return {
                success: true,
                message: '系统配置更新成功',
                data: await this.findByKey(key)
            };
        }
        catch (error) {
            this.logger.error(`更新键为 ${key} 的系统配置失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`更新系统配置失败: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            const config = await this.findOne(id);
            if (!config.isEditable) {
                throw new Error(`System config with ID ${id} is not editable and cannot be deleted`);
            }
            this.logger.log(`删除ID为 ${id} 的系统配置`);
            await this.dataSource.query(`DELETE FROM SYSTEM_CONFIG WHERE CONFIG_ID = :1`, [id]);
            this.logger.log(`系统配置删除成功，ID: ${id}`);
            return {
                success: true,
                message: '系统配置删除成功'
            };
        }
        catch (error) {
            this.logger.error(`删除ID为 ${id} 的系统配置失败: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException(`删除系统配置失败: ${error.message}`);
        }
    }
    transformDatabaseRecord(record) {
        return {
            id: record.CONFIG_ID,
            key: record.CONFIG_KEY,
            value: record.CONFIG_VALUE,
            type: record.CONFIG_TYPE,
            group: record.CONFIG_GROUP,
            description: record.DESCRIPTION,
            isEncrypted: record.IS_ENCRYPTED === 1,
            isEditable: record.IS_EDITABLE === 1,
            sortOrder: record.SORT_ORDER,
            isActive: record.IS_ACTIVE === 1,
            createdBy: record.CREATED_BY,
            creationDate: record.CREATION_DATE,
            lastUpdatedBy: record.LAST_UPDATED_BY,
            lastUpdateDate: record.LAST_UPDATE_DATE
        };
    }
};
exports.SystemConfigService = SystemConfigService;
exports.SystemConfigService = SystemConfigService = SystemConfigService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], SystemConfigService);
//# sourceMappingURL=system-config.service.js.map