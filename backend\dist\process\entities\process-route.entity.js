"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessRoute = void 0;
const typeorm_1 = require("typeorm");
const route_operation_entity_1 = require("./route-operation.entity");
let ProcessRoute = class ProcessRoute {
};
exports.ProcessRoute = ProcessRoute;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ROUTE_ID' }),
    __metadata("design:type", Number)
], ProcessRoute.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ROUTE_CODE', length: 50, unique: true }),
    __metadata("design:type", String)
], ProcessRoute.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ROUTE_NAME', length: 100 }),
    __metadata("design:type", String)
], ProcessRoute.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRIPTION', length: 500, nullable: true }),
    __metadata("design:type", String)
], ProcessRoute.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', type: 'number', default: 1 }),
    __metadata("design:type", Number)
], ProcessRoute.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', length: 50, nullable: true }),
    __metadata("design:type", String)
], ProcessRoute.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATION_DATE', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], ProcessRoute.prototype, "creationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', length: 50, nullable: true }),
    __metadata("design:type", String)
], ProcessRoute.prototype, "lastUpdatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATE_DATE', type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ProcessRoute.prototype, "lastUpdateDate", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => route_operation_entity_1.RouteOperation, routeOperation => routeOperation.route),
    __metadata("design:type", Array)
], ProcessRoute.prototype, "operations", void 0);
exports.ProcessRoute = ProcessRoute = __decorate([
    (0, typeorm_1.Entity)('PROCESS_ROUTE')
], ProcessRoute);
//# sourceMappingURL=process-route.entity.js.map