import { Role } from './role.entity';
import { Department } from '../../department/entities/department.entity';
export declare class UserRole {
    USER_ROLE_ID: number;
    USER_ID: number;
    ROLE_ID: number;
    DEPT_ID: number;
    IS_ACTIVE: number;
    CREATED_BY: string;
    CREATION_DATE: Date;
    LAST_UPDATED_BY: string;
    LAST_UPDATE_DATE: Date;
    role?: Role;
    department?: Department;
}
