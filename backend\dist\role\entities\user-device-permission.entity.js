"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserDevicePermission = void 0;
const typeorm_1 = require("typeorm");
let UserDevicePermission = class UserDevicePermission {
};
exports.UserDevicePermission = UserDevicePermission;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'USER_LED_ID' }),
    __metadata("design:type", Number)
], UserDevicePermission.prototype, "USER_LED_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USER_ID', nullable: false }),
    __metadata("design:type", Number)
], UserDevicePermission.prototype, "USER_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LED_ID', nullable: false }),
    __metadata("design:type", String)
], UserDevicePermission.prototype, "LED_ID", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PERMISSION_TYPE', nullable: false }),
    __metadata("design:type", String)
], UserDevicePermission.prototype, "PERMISSION_TYPE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_ACTIVE', default: 1 }),
    __metadata("design:type", Number)
], UserDevicePermission.prototype, "IS_ACTIVE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATED_BY', nullable: true }),
    __metadata("design:type", String)
], UserDevicePermission.prototype, "CREATED_BY", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CREATION_DATE', type: 'date', default: () => 'SYSDATE' }),
    __metadata("design:type", Date)
], UserDevicePermission.prototype, "CREATION_DATE", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATED_BY', nullable: true }),
    __metadata("design:type", String)
], UserDevicePermission.prototype, "LAST_UPDATED_BY", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LAST_UPDATE_DATE', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], UserDevicePermission.prototype, "LAST_UPDATE_DATE", void 0);
exports.UserDevicePermission = UserDevicePermission = __decorate([
    (0, typeorm_1.Entity)('LED_USER_DEVICE_PERMISSION')
], UserDevicePermission);
//# sourceMappingURL=user-device-permission.entity.js.map