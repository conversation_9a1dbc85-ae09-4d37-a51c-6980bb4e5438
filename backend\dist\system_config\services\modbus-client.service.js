"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ModbusClientService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModbusClientService = void 0;
const common_1 = require("@nestjs/common");
const ModbusRTU = require('modbus-serial');
class MockModbusRTU {
    constructor() {
        this.isOpen = false;
        this.deviceId = 1;
        if (MockModbusRTU.globalMockRegisters.size === 0) {
            console.log('🔧 初始化MockModbusRTU寄存器数据');
            for (let i = 0; i < 100; i++) {
                MockModbusRTU.globalMockRegisters.set(i, 0);
            }
            console.log('🔧 MockModbusRTU寄存器初始化完成，共100个寄存器');
        }
        else {
            console.log(`🔧 MockModbusRTU实例创建，当前寄存器数据量: ${MockModbusRTU.globalMockRegisters.size}`);
            console.log('🔧 当前寄存器状态:');
            for (let i = 0; i < 20; i++) {
                const value = MockModbusRTU.globalMockRegisters.get(i) || 0;
                if (value !== 0) {
                    console.log(`🔧   寄存器${i}: ${value}`);
                }
            }
        }
    }
    get mockRegisters() {
        return MockModbusRTU.globalMockRegisters;
    }
    async connectTCP(ip, options) {
        await new Promise(resolve => setTimeout(resolve, 500));
        this.isOpen = true;
    }
    async close() {
        await new Promise(resolve => setTimeout(resolve, 200));
        this.isOpen = false;
    }
    setTimeout(timeout) {
    }
    setID(id) {
        this.deviceId = id;
    }
    async writeRegister(address, value) {
        await new Promise(resolve => setTimeout(resolve, 100));
        this.mockRegisters.set(address, value);
        return { address, value };
    }
    async writeRegisters(address, values) {
        await new Promise(resolve => setTimeout(resolve, 100 * values.length));
        console.log(`MockModbusRTU写入前寄存器状态:`);
        for (let i = address; i < address + values.length + 2; i++) {
            console.log(`  寄存器${i}: ${this.mockRegisters.get(i) || 0}`);
        }
        values.forEach((value, index) => {
            this.mockRegisters.set(address + index, value);
            console.log(`  写入寄存器${address + index}: ${value}`);
        });
        console.log(`MockModbusRTU写入后寄存器状态:`);
        for (let i = address; i < address + values.length + 2; i++) {
            console.log(`  寄存器${i}: ${this.mockRegisters.get(i) || 0}`);
        }
        return { address, count: values.length };
    }
    async readHoldingRegisters(address, length) {
        await new Promise(resolve => setTimeout(resolve, 100 * length));
        console.log(`MockModbusRTU读取寄存器 ${address} 开始的 ${length} 个值:`);
        const data = [];
        for (let i = 0; i < length; i++) {
            const value = this.mockRegisters.get(address + i) || 0;
            data.push(value);
            console.log(`  寄存器${address + i}: ${value}`);
        }
        return { data };
    }
}
MockModbusRTU.globalMockRegisters = new Map();
let ModbusClientService = ModbusClientService_1 = class ModbusClientService {
    constructor() {
        this.logger = new common_1.Logger(ModbusClientService_1.name);
        this.isRealMode = process.env.MODBUS_REAL_MODE === 'true';
        console.log('🔧 ModbusClientService构造函数被调用');
        console.log(`🔧 MODBUS_REAL_MODE环境变量: ${process.env.MODBUS_REAL_MODE}`);
        console.log(`🔧 使用真实模式: ${this.isRealMode}`);
        if (this.isRealMode) {
            this.client = new ModbusRTU();
            this.logger.log('使用真实ModbusTCP客户端');
        }
        else {
            console.log('🔧 创建MockModbusRTU实例');
            this.client = new MockModbusRTU();
            this.logger.log('使用模拟ModbusTCP客户端进行开发和测试');
            console.log('🔧 MockModbusRTU实例创建完成');
        }
    }
    async connect(ip, port, timeout = 5000) {
        try {
            this.logger.log(`正在连接到 ${ip}:${port} (${this.isRealMode ? '真实模式' : '模拟模式'})`);
            if (this.client.isOpen) {
                await this.close();
            }
            if (this.isRealMode) {
                const realClient = this.client;
                realClient.setTimeout(timeout);
                realClient.setID(1);
                await realClient.connectTCP(ip, { port });
            }
            else {
                await this.client.connectTCP(ip, { port });
            }
            this.logger.log(`成功连接到 ${ip}:${port}`);
            return true;
        }
        catch (error) {
            this.logger.error(`连接到 ${ip}:${port} 失败: ${error.message}`);
            return false;
        }
    }
    async close() {
        try {
            if (this.client.isOpen) {
                await this.client.close();
                this.logger.log('ModbusTCP连接已关闭');
            }
        }
        catch (error) {
            this.logger.error(`关闭ModbusTCP连接失败: ${error.message}`);
        }
    }
    async writeRegister(address, value) {
        try {
            if (this.isRealMode) {
                const realClient = this.client;
                realClient.setID(1);
                await realClient.writeRegister(address, value);
            }
            else {
                const mockClient = this.client;
                await mockClient.writeRegister(address, value);
            }
            this.logger.log(`成功写入寄存器 ${address} 值: ${value}`);
            return true;
        }
        catch (error) {
            this.logger.error(`写入寄存器 ${address} 失败: ${error.message}`);
            return false;
        }
    }
    async writeRegisters(address, values) {
        try {
            if (this.isRealMode) {
                const realClient = this.client;
                realClient.setID(1);
                await realClient.writeRegisters(address, values);
            }
            else {
                const mockClient = this.client;
                await mockClient.writeRegisters(address, values);
            }
            this.logger.log(`成功写入寄存器 ${address} 开始的 ${values.length} 个值: ${JSON.stringify(values)}`);
            return true;
        }
        catch (error) {
            this.logger.error(`写入寄存器 ${address} 开始的 ${values.length} 个值失败: ${error.message}`);
            return false;
        }
    }
    async readHoldingRegisters(address, length) {
        try {
            if (this.isRealMode) {
                const realClient = this.client;
                realClient.setID(1);
                const result = await realClient.readHoldingRegisters(address, length);
                this.logger.log(`成功读取寄存器 ${address} 开始的 ${length} 个值: ${JSON.stringify(result.data)}`);
                return result.data;
            }
            else {
                const mockClient = this.client;
                const result = await mockClient.readHoldingRegisters(address, length);
                this.logger.log(`成功读取寄存器 ${address} 开始的 ${length} 个值: ${JSON.stringify(result.data)}`);
                return result.data;
            }
        }
        catch (error) {
            this.logger.error(`读取寄存器 ${address} 开始的 ${length} 个值失败: ${error.message}`);
            throw error;
        }
    }
    splitTo16Bit(value) {
        const highWord = Math.floor(value / 65536);
        const lowWord = value % 65536;
        return [highWord, lowWord];
    }
    mergeTo32Bit(highWord, lowWord) {
        return highWord * 65536 + lowWord;
    }
};
exports.ModbusClientService = ModbusClientService;
exports.ModbusClientService = ModbusClientService = ModbusClientService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], ModbusClientService);
//# sourceMappingURL=modbus-client.service.js.map